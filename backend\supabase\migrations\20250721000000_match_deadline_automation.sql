-- Match Deadline Automation Migration
-- Date: 2025-07-21
-- Description: Automated result finalization system with deadline tracking and audit logging

-- ==================== MATCH AUDIT LOGGING SYSTEM ====================

-- Match Audit Log Table - Track all automated actions and system events
CREATE TABLE IF NOT EXISTS match_audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
  action_type VARCHAR(50) NOT NULL CHECK (action_type IN (
    'auto_finalized', 
    'deadline_expired', 
    'participant_defaulted',
    'auto_winner_assigned',
    'auto_loser_assigned',
    'match_auto_completed',
    'deadline_notification_sent',
    'manual_admin_override'
  )),
  triggered_by VARCHAR(20) DEFAULT 'system' CHECK (triggered_by IN ('system', 'admin', 'cron_job')),
  admin_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- Only for manual admin actions
  
  -- Action details
  affected_participants JSONB DEFAULT '[]', -- Array of participant user IDs
  action_details JSONB NOT NULL DEFAULT '{}', -- Detailed information about the action
  reason TEXT NOT NULL,
  
  -- Timing information
  deadline_time TIMESTAMP WITH TIME ZONE,
  action_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Result summary
  participants_submitted INTEGER DEFAULT 0,
  participants_defaulted INTEGER DEFAULT 0,
  total_participants INTEGER DEFAULT 0,
  
  -- System metadata
  system_version VARCHAR(20) DEFAULT '1.0',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Match Deadline Status Table - Track real-time deadline information
CREATE TABLE IF NOT EXISTS match_deadline_status (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE UNIQUE,
  
  -- Deadline tracking
  result_submission_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
  deadline_warning_sent BOOLEAN DEFAULT FALSE,
  deadline_critical_sent BOOLEAN DEFAULT FALSE,
  deadline_expired BOOLEAN DEFAULT FALSE,
  
  -- Participant tracking
  total_participants INTEGER NOT NULL DEFAULT 0,
  participants_submitted INTEGER DEFAULT 0,
  participants_remaining INTEGER GENERATED ALWAYS AS (total_participants - participants_submitted) STORED,
  
  -- Processing status
  auto_finalization_attempted BOOLEAN DEFAULT FALSE,
  auto_finalization_completed BOOLEAN DEFAULT FALSE,
  auto_finalization_error TEXT,
  
  -- Timestamps
  last_checked TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  finalized_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==================== ENHANCE EXISTING TABLES ====================

-- Add deadline automation fields to matches table
ALTER TABLE matches 
ADD COLUMN IF NOT EXISTS auto_finalization_enabled BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS deadline_notifications_enabled BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS auto_finalized BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS auto_finalized_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS auto_finalized_reason TEXT;

-- Add deadline tracking to match_participants
ALTER TABLE match_participants 
ADD COLUMN IF NOT EXISTS deadline_notified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS deadline_notification_sent_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS auto_assigned_result BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS auto_assignment_reason TEXT;

-- ==================== INDEXES FOR PERFORMANCE ====================

-- Match audit log indexes
CREATE INDEX IF NOT EXISTS idx_match_audit_log_match_id ON match_audit_log(match_id);
CREATE INDEX IF NOT EXISTS idx_match_audit_log_action_type ON match_audit_log(action_type);
CREATE INDEX IF NOT EXISTS idx_match_audit_log_timestamp ON match_audit_log(action_timestamp);
CREATE INDEX IF NOT EXISTS idx_match_audit_log_triggered_by ON match_audit_log(triggered_by);

-- Match deadline status indexes
CREATE INDEX IF NOT EXISTS idx_match_deadline_status_match_id ON match_deadline_status(match_id);
CREATE INDEX IF NOT EXISTS idx_match_deadline_status_deadline ON match_deadline_status(result_submission_deadline);
CREATE INDEX IF NOT EXISTS idx_match_deadline_status_expired ON match_deadline_status(deadline_expired);
CREATE INDEX IF NOT EXISTS idx_match_deadline_status_finalization ON match_deadline_status(auto_finalization_completed);

-- Enhanced matches indexes for deadline queries
CREATE INDEX IF NOT EXISTS idx_matches_result_deadline ON matches(result_submission_deadline) WHERE result_submission_deadline IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_matches_auto_finalized ON matches(auto_finalized);
CREATE INDEX IF NOT EXISTS idx_matches_status_deadline ON matches(status, result_submission_deadline);

-- Enhanced match_participants indexes
CREATE INDEX IF NOT EXISTS idx_match_participants_result_submitted ON match_participants(match_id, result_submitted_at);
CREATE INDEX IF NOT EXISTS idx_match_participants_auto_assigned ON match_participants(auto_assigned_result);

-- ==================== ROW LEVEL SECURITY ====================

-- Enable RLS on new tables
ALTER TABLE match_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_deadline_status ENABLE ROW LEVEL SECURITY;

-- RLS Policies for match_audit_log (Admin and system access only)
CREATE POLICY "Admins can view all match audit logs" 
  ON match_audit_log FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'manager')
    )
  );

CREATE POLICY "System can insert audit logs" 
  ON match_audit_log FOR INSERT 
  WITH CHECK (triggered_by = 'system' OR triggered_by = 'cron_job');

CREATE POLICY "Admins can insert audit logs" 
  ON match_audit_log FOR INSERT 
  WITH CHECK (
    triggered_by = 'admin' AND
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'manager')
    )
  );

-- RLS Policies for match_deadline_status
CREATE POLICY "Users can view deadline status for their matches" 
  ON match_deadline_status FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM match_participants mp
      WHERE mp.match_id = match_deadline_status.match_id 
      AND mp.user_id = auth.uid()
    )
  );

CREATE POLICY "Admins can view all deadline statuses" 
  ON match_deadline_status FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'manager')
    )
  );

CREATE POLICY "System can manage deadline status" 
  ON match_deadline_status FOR ALL 
  USING (true); -- System operations bypass RLS

-- ==================== TRIGGERS ====================

-- Update timestamp trigger for match_deadline_status
CREATE OR REPLACE FUNCTION update_match_deadline_status_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_match_deadline_status_updated_at
  BEFORE UPDATE ON match_deadline_status
  FOR EACH ROW
  EXECUTE FUNCTION update_match_deadline_status_updated_at();

-- Auto-create deadline status when match is created
CREATE OR REPLACE FUNCTION create_match_deadline_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create if match has a result submission deadline
  IF NEW.result_submission_deadline IS NOT NULL THEN
    INSERT INTO match_deadline_status (
      match_id,
      result_submission_deadline,
      total_participants
    ) VALUES (
      NEW.id,
      NEW.result_submission_deadline,
      NEW.max_participants
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_create_match_deadline_status
  AFTER INSERT ON matches
  FOR EACH ROW
  EXECUTE FUNCTION create_match_deadline_status();

-- Update participant count when participants join/leave
CREATE OR REPLACE FUNCTION update_deadline_participant_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Participant joined
    UPDATE match_deadline_status 
    SET total_participants = (
      SELECT COUNT(*) FROM match_participants 
      WHERE match_id = NEW.match_id AND participant_type = 'player'
    )
    WHERE match_id = NEW.match_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Participant left
    UPDATE match_deadline_status 
    SET total_participants = (
      SELECT COUNT(*) FROM match_participants 
      WHERE match_id = OLD.match_id AND participant_type = 'player'
    )
    WHERE match_id = OLD.match_id;
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Result submitted
    IF OLD.result_submitted_at IS NULL AND NEW.result_submitted_at IS NOT NULL THEN
      UPDATE match_deadline_status 
      SET participants_submitted = (
        SELECT COUNT(*) FROM match_participants 
        WHERE match_id = NEW.match_id 
        AND participant_type = 'player' 
        AND result_submitted_at IS NOT NULL
      )
      WHERE match_id = NEW.match_id;
    END IF;
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_deadline_participant_count
  AFTER INSERT OR UPDATE OR DELETE ON match_participants
  FOR EACH ROW
  EXECUTE FUNCTION update_deadline_participant_count();

-- ==================== FUNCTIONS ====================

-- Function to get matches approaching deadline
CREATE OR REPLACE FUNCTION get_matches_approaching_deadline(warning_minutes INTEGER DEFAULT 10)
RETURNS TABLE (
  match_id UUID,
  deadline TIMESTAMP WITH TIME ZONE,
  minutes_remaining INTEGER,
  participants_submitted INTEGER,
  total_participants INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    mds.match_id,
    mds.result_submission_deadline,
    EXTRACT(EPOCH FROM (mds.result_submission_deadline - NOW()))::INTEGER / 60 AS minutes_remaining,
    mds.participants_submitted,
    mds.total_participants
  FROM match_deadline_status mds
  JOIN matches m ON m.id = mds.match_id
  WHERE 
    mds.deadline_expired = FALSE
    AND mds.result_submission_deadline > NOW()
    AND mds.result_submission_deadline <= NOW() + INTERVAL '1 minute' * warning_minutes
    AND m.status IN ('live', 'active')
    AND mds.participants_submitted < mds.total_participants;
END;
$$ LANGUAGE plpgsql;

-- Function to get expired matches needing finalization
CREATE OR REPLACE FUNCTION get_expired_matches_for_finalization()
RETURNS TABLE (
  match_id UUID,
  deadline TIMESTAMP WITH TIME ZONE,
  participants_submitted INTEGER,
  total_participants INTEGER,
  match_status VARCHAR
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    mds.match_id,
    mds.result_submission_deadline,
    mds.participants_submitted,
    mds.total_participants,
    m.status
  FROM match_deadline_status mds
  JOIN matches m ON m.id = mds.match_id
  WHERE 
    mds.result_submission_deadline <= NOW()
    AND mds.deadline_expired = FALSE
    AND mds.auto_finalization_completed = FALSE
    AND m.status IN ('live', 'active', 'pending_verification')
    AND m.auto_finalization_enabled = TRUE;
END;
$$ LANGUAGE plpgsql;
