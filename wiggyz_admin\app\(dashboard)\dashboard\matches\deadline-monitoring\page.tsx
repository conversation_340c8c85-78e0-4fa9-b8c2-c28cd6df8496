'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Users, 
  Activity,
  RefreshCw,
  Play,
  BarChart3,
  Calendar
} from 'lucide-react'
import { format } from 'date-fns'

interface DeadlineStats {
  totalMatchesWithDeadlines: number;
  autoFinalizedMatches: number;
  expiredMatches: number;
  matchesApproachingDeadline: number;
  timestamp: string;
}

interface ApproachingMatch {
  match_id: string;
  deadline: string;
  minutes_remaining: number;
  participants_submitted: number;
  total_participants: number;
}

interface AuditLogEntry {
  id: string;
  match_id: string;
  action_type: string;
  triggered_by: string;
  affected_participants: string[];
  action_details: any;
  reason: string;
  deadline_time?: string;
  participants_submitted: number;
  participants_defaulted: number;
  total_participants: number;
  action_timestamp: string;
}

export default function DeadlineMonitoringPage() {
  const { toast } = useToast()
  const [stats, setStats] = useState<DeadlineStats | null>(null)
  const [approachingMatches, setApproachingMatches] = useState<ApproachingMatch[]>([])
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [refreshData, setRefreshData] = useState(0)

  useEffect(() => {
    fetchData()
  }, [refreshData])

  const fetchData = async () => {
    try {
      setIsLoading(true)
      
      // Fetch deadline statistics
      const statsResponse = await fetch('/api/v1/matches/deadline-stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData)
      }

      // Fetch matches approaching deadline
      const approachingResponse = await fetch('/api/v1/matches/approaching-deadline?warning_minutes=30', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (approachingResponse.ok) {
        const approachingData = await approachingResponse.json()
        setApproachingMatches(approachingData.matches || [])
      }

      // Fetch recent audit logs (last 50 entries)
      const auditResponse = await fetch('/api/v1/matches/audit-log/recent?limit=50', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (auditResponse.ok) {
        const auditData = await auditResponse.json()
        setAuditLogs(auditData.auditLogs || [])
      }

    } catch (error) {
      console.error('Error fetching deadline monitoring data:', error)
      toast({
        title: "Error",
        description: "Failed to fetch deadline monitoring data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleManualProcessing = async () => {
    try {
      setIsProcessing(true)
      
      const response = await fetch('/api/v1/matches/process-expired', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: "Processing Complete",
          description: `Processed ${result.processed} matches with ${result.errors} errors`,
        })
        
        // Refresh data
        setRefreshData(prev => prev + 1)
      } else {
        throw new Error('Failed to process expired matches')
      }

    } catch (error) {
      console.error('Error processing expired matches:', error)
      toast({
        title: "Processing Failed",
        description: "Failed to process expired matches",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const getActionTypeColor = (actionType: string) => {
    switch (actionType) {
      case 'auto_finalized':
        return 'bg-blue-100 text-blue-800'
      case 'deadline_expired':
        return 'bg-red-100 text-red-800'
      case 'participant_defaulted':
        return 'bg-orange-100 text-orange-800'
      case 'auto_winner_assigned':
        return 'bg-green-100 text-green-800'
      case 'match_auto_completed':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getUrgencyColor = (minutesRemaining: number) => {
    if (minutesRemaining <= 2) return 'text-red-600'
    if (minutesRemaining <= 10) return 'text-orange-600'
    return 'text-yellow-600'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Deadline Monitoring</h1>
          <p className="text-muted-foreground">
            Monitor automated result finalization and deadline tracking
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => setRefreshData(prev => prev + 1)}
            variant="outline"
            size="sm"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={handleManualProcessing}
            disabled={isProcessing}
            size="sm"
          >
            <Play className="h-4 w-4 mr-2" />
            {isProcessing ? 'Processing...' : 'Process Expired'}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Matches</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalMatchesWithDeadlines || 0}</div>
            <p className="text-xs text-muted-foreground">
              Matches with deadlines
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Auto-Finalized</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.autoFinalizedMatches || 0}</div>
            <p className="text-xs text-muted-foreground">
              Automatically completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.expiredMatches || 0}</div>
            <p className="text-xs text-muted-foreground">
              Past deadline
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approaching</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.matchesApproachingDeadline || 0}</div>
            <p className="text-xs text-muted-foreground">
              Deadline in 30 min
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="approaching" className="space-y-4">
        <TabsList>
          <TabsTrigger value="approaching">Approaching Deadlines</TabsTrigger>
          <TabsTrigger value="audit">Audit Log</TabsTrigger>
        </TabsList>

        <TabsContent value="approaching" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Matches Approaching Deadline
              </CardTitle>
              <CardDescription>
                Matches with deadlines in the next 30 minutes
              </CardDescription>
            </CardHeader>
            <CardContent>
              {approachingMatches.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No matches approaching deadline</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Match ID</TableHead>
                      <TableHead>Deadline</TableHead>
                      <TableHead>Time Remaining</TableHead>
                      <TableHead>Submissions</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {approachingMatches.map((match) => (
                      <TableRow key={match.match_id}>
                        <TableCell className="font-mono text-sm">
                          {match.match_id.slice(0, 8)}...
                        </TableCell>
                        <TableCell>
                          {format(new Date(match.deadline), 'MMM dd, HH:mm')}
                        </TableCell>
                        <TableCell>
                          <span className={getUrgencyColor(match.minutes_remaining)}>
                            {match.minutes_remaining} min
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            {match.participants_submitted}/{match.total_participants}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={match.minutes_remaining <= 2 ? "destructive" : "secondary"}>
                            {match.minutes_remaining <= 2 ? "Critical" : "Warning"}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Audit Log
              </CardTitle>
              <CardDescription>
                Recent automated actions and system events
              </CardDescription>
            </CardHeader>
            <CardContent>
              {auditLogs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No audit logs found</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Timestamp</TableHead>
                      <TableHead>Match ID</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Participants</TableHead>
                      <TableHead>Reason</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {auditLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            {format(new Date(log.action_timestamp), 'MMM dd, HH:mm:ss')}
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {log.match_id.slice(0, 8)}...
                        </TableCell>
                        <TableCell>
                          <Badge className={getActionTypeColor(log.action_type)}>
                            {log.action_type.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>Submitted: {log.participants_submitted}</div>
                            <div>Defaulted: {log.participants_defaulted}</div>
                            <div>Total: {log.total_participants}</div>
                          </div>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {log.reason}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
