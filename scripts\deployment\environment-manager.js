#!/usr/bin/env node

/**
 * Environment Manager for WiggyZ Deployment System
 * Manages environment configurations across Flutter app, admin dashboard, and backend
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class EnvironmentManager {
  constructor() {
    this.rootDir = path.resolve(__dirname, '../..');
    this.configPath = path.join(this.rootDir, 'config', 'environments.json');
    this.environments = this.loadEnvironments();
  }

  loadEnvironments() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      console.error('❌ Failed to load environment configuration:', error.message);
      process.exit(1);
    }
  }

  validateEnvironment(envName) {
    if (!this.environments[envName]) {
      console.error(`❌ Environment '${envName}' not found. Available: ${Object.keys(this.environments).join(', ')}`);
      process.exit(1);
    }
    return this.environments[envName];
  }

  updateFlutterConfig(environment) {
    const flutterConfigPath = path.join(this.rootDir, 'frontend', 'lib', 'core', 'api', 'api_config.dart');
    
    if (!fs.existsSync(flutterConfigPath)) {
      console.warn('⚠️  Flutter config file not found, skipping Flutter update');
      return;
    }

    try {
      let configContent = fs.readFileSync(flutterConfigPath, 'utf8');
      
      // Update the baseUrl getter
      const newBaseUrl = environment.frontend.apiBaseUrl;
      configContent = configContent.replace(
        /return\s+['"`][^'"`]+['"`];/,
        `return '${newBaseUrl}';`
      );

      fs.writeFileSync(flutterConfigPath, configContent);
      console.log(`✅ Updated Flutter API config to: ${newBaseUrl}`);
    } catch (error) {
      console.error('❌ Failed to update Flutter config:', error.message);
    }
  }

  updateAdminConfig(environment) {
    // Update admin dashboard environment variables
    const adminEnvPath = path.join(this.rootDir, 'wiggyz_admin', '.env.local');
    
    try {
      const envContent = `NEXT_PUBLIC_API_URL=${environment.admin.apiBaseUrl}
NODE_ENV=${environment.environment}
NEXT_PUBLIC_ENVIRONMENT=${environment.environment}
`;
      
      fs.writeFileSync(adminEnvPath, envContent);
      console.log(`✅ Updated admin dashboard config to: ${environment.admin.apiBaseUrl}`);
    } catch (error) {
      console.error('❌ Failed to update admin config:', error.message);
    }
  }

  switchEnvironment(envName) {
    console.log(`🔄 Switching to ${envName} environment...`);
    
    const environment = this.validateEnvironment(envName);
    
    // Update Flutter app configuration
    this.updateFlutterConfig(environment);
    
    // Update admin dashboard configuration
    this.updateAdminConfig(environment);
    
    // Create current environment marker
    const currentEnvPath = path.join(this.rootDir, 'config', 'current-environment.json');
    fs.writeFileSync(currentEnvPath, JSON.stringify({
      environment: envName,
      config: environment,
      switchedAt: new Date().toISOString()
    }, null, 2));
    
    console.log(`✅ Successfully switched to ${envName} environment`);
    console.log(`📍 Backend URL: ${environment.backend.url}`);
    console.log(`📱 Flutter API: ${environment.frontend.apiBaseUrl}`);
    console.log(`🖥️  Admin API: ${environment.admin.apiBaseUrl}`);
  }

  getCurrentEnvironment() {
    const currentEnvPath = path.join(this.rootDir, 'config', 'current-environment.json');
    
    if (fs.existsSync(currentEnvPath)) {
      try {
        const currentEnv = JSON.parse(fs.readFileSync(currentEnvPath, 'utf8'));
        return currentEnv;
      } catch (error) {
        console.warn('⚠️  Failed to read current environment, defaulting to development');
      }
    }
    
    return {
      environment: 'development',
      config: this.environments.development
    };
  }

  listEnvironments() {
    console.log('📋 Available environments:');
    Object.keys(this.environments).forEach(env => {
      const config = this.environments[env];
      console.log(`  ${env}:`);
      console.log(`    Backend: ${config.backend.url}`);
      console.log(`    Frontend: ${config.frontend.apiBaseUrl}`);
      console.log(`    Admin: ${config.admin.apiBaseUrl}`);
      console.log('');
    });
  }

  validateConfiguration() {
    console.log('🔍 Validating environment configuration...');
    
    const currentEnv = this.getCurrentEnvironment();
    const config = currentEnv.config;
    
    // Check if URLs are accessible (for production/staging)
    if (config.environment !== 'development') {
      console.log(`🌐 Testing connectivity to ${config.backend.url}...`);
      // Add connectivity tests here if needed
    }
    
    console.log('✅ Configuration validation complete');
    return true;
  }
}

// CLI Interface
if (require.main === module) {
  const manager = new EnvironmentManager();
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('WiggyZ Environment Manager');
    console.log('Usage:');
    console.log('  node environment-manager.js switch <environment>');
    console.log('  node environment-manager.js current');
    console.log('  node environment-manager.js list');
    console.log('  node environment-manager.js validate');
    process.exit(0);
  }
  
  const command = args[0];
  
  switch (command) {
    case 'switch':
      if (!args[1]) {
        console.error('❌ Please specify an environment to switch to');
        process.exit(1);
      }
      manager.switchEnvironment(args[1]);
      break;
      
    case 'current':
      const current = manager.getCurrentEnvironment();
      console.log(`📍 Current environment: ${current.environment}`);
      console.log(`🕒 Switched at: ${current.switchedAt || 'Unknown'}`);
      break;
      
    case 'list':
      manager.listEnvironments();
      break;
      
    case 'validate':
      manager.validateConfiguration();
      break;
      
    default:
      console.error(`❌ Unknown command: ${command}`);
      process.exit(1);
  }
}

module.exports = EnvironmentManager;
