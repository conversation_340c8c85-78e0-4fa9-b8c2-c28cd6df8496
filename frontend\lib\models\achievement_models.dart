/// Achievement system models for the WiggyZ app
/// These models handle user achievements, progress tracking, and achievement unlocking

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Enum for achievement types
enum AchievementType {
  matchWin,
  tournamentWin,
  loginStreak,
  socialActivity,
  spending,
  referral,
  special,
}

/// Extension for achievement type utilities
extension AchievementTypeExtension on AchievementType {
  String get value {
    switch (this) {
      case AchievementType.matchWin:
        return 'match_win';
      case AchievementType.tournamentWin:
        return 'tournament_win';
      case AchievementType.loginStreak:
        return 'login_streak';
      case AchievementType.socialActivity:
        return 'social_activity';
      case AchievementType.spending:
        return 'spending';
      case AchievementType.referral:
        return 'referral';
      case AchievementType.special:
        return 'special';
    }
  }

  static AchievementType fromString(String value) {
    switch (value) {
      case 'match_win':
        return AchievementType.matchWin;
      case 'tournament_win':
        return AchievementType.tournamentWin;
      case 'login_streak':
        return AchievementType.loginStreak;
      case 'social_activity':
        return AchievementType.socialActivity;
      case 'spending':
        return AchievementType.spending;
      case 'referral':
        return AchievementType.referral;
      case 'special':
        return AchievementType.special;
      default:
        return AchievementType.special;
    }
  }

  /// Get display name for achievement type
  String get displayName {
    switch (this) {
      case AchievementType.matchWin:
        return 'Match Victory';
      case AchievementType.tournamentWin:
        return 'Tournament Champion';
      case AchievementType.loginStreak:
        return 'Daily Login';
      case AchievementType.socialActivity:
        return 'Social Butterfly';
      case AchievementType.spending:
        return 'Big Spender';
      case AchievementType.referral:
        return 'Referral Master';
      case AchievementType.special:
        return 'Special Achievement';
    }
  }

  /// Get icon for achievement type
  IconData get icon {
    switch (this) {
      case AchievementType.matchWin:
        return Icons.emoji_events;
      case AchievementType.tournamentWin:
        return Icons.military_tech;
      case AchievementType.loginStreak:
        return Icons.calendar_today;
      case AchievementType.socialActivity:
        return Icons.people;
      case AchievementType.spending:
        return Icons.diamond;
      case AchievementType.referral:
        return Icons.share;
      case AchievementType.special:
        return Icons.star;
    }
  }

  /// Get color for achievement type
  Color get color {
    switch (this) {
      case AchievementType.matchWin:
        return Colors.amber;
      case AchievementType.tournamentWin:
        return Colors.purple;
      case AchievementType.loginStreak:
        return Colors.blue;
      case AchievementType.socialActivity:
        return Colors.green;
      case AchievementType.spending:
        return Colors.cyan;
      case AchievementType.referral:
        return Colors.orange;
      case AchievementType.special:
        return Colors.pink;
    }
  }
}

/// Model for individual achievements
class AchievementModel {
  final String id;
  final String userId;
  final String title;
  final String description;
  final int target;
  final int current;
  final String? iconName;
  final String? type;
  final int? rewardPoints;
  final int? rewardDiamonds;
  final bool isCompleted;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AchievementModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.target,
    required this.current,
    this.iconName,
    this.type,
    this.rewardPoints,
    this.rewardDiamonds,
    required this.isCompleted,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AchievementModel.fromJson(Map<String, dynamic> json) {
    try {
      return AchievementModel(
        id: json['id']?.toString() ?? 'unknown-${DateTime.now().millisecondsSinceEpoch}',
        userId: json['user_id']?.toString() ?? 'current-user', // Make user_id optional since API doesn't return it
        title: json['title']?.toString() ?? 'Unknown Achievement',
        description: json['description']?.toString() ?? 'No description available',
        target: (json['target'] as num?)?.toInt() ?? 1,
        current: (json['current'] as num?)?.toInt() ?? 0,
        iconName: json['icon_name']?.toString(),
        type: json['type']?.toString(),
        rewardPoints: (json['reward_points'] as num?)?.toInt(),
        rewardDiamonds: (json['reward_diamonds'] as num?)?.toInt(),
        isCompleted: json['is_completed'] as bool? ?? (json['current'] != null && json['target'] != null && (json['current'] as num) >= (json['target'] as num)),
        completedAt: json['completed_at'] != null
            ? DateTime.tryParse(json['completed_at'].toString())
            : null,
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString()) ?? DateTime.now()
            : DateTime.now(),
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'].toString()) ?? DateTime.now()
            : DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error parsing AchievementModel from JSON: $e');
      debugPrint('JSON data: $json');
      // Return a default achievement model to prevent crashes
      return AchievementModel(
        id: json['id']?.toString() ?? 'unknown',
        userId: 'current-user', // Use default user ID
        title: 'Error Loading Achievement',
        description: 'Failed to load achievement data',
        target: 1,
        current: 0,
        iconName: null,
        type: null,
        rewardPoints: null,
        rewardDiamonds: null,
        isCompleted: false,
        completedAt: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'description': description,
      'target': target,
      'current': current,
      'icon_name': iconName,
      'type': type,
      'reward_points': rewardPoints,
      'reward_diamonds': rewardDiamonds,
      'is_completed': isCompleted,
      'completed_at': completedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Get progress percentage (0.0 to 1.0)
  double get progressPercentage {
    if (target <= 0) return 0.0;
    return (current / target).clamp(0.0, 1.0);
  }

  /// Get achievement type enum
  AchievementType get achievementType {
    return AchievementTypeExtension.fromString(type ?? 'special');
  }

  /// Get display icon
  IconData get displayIcon {
    if (iconName != null) {
      // Map icon names to actual icons
      switch (iconName!.toLowerCase()) {
        case 'trophy':
          return Icons.emoji_events;
        case 'star':
          return Icons.star;
        case 'medal':
          return Icons.military_tech;
        case 'crown':
          return Icons.workspace_premium;
        case 'diamond':
          return Icons.diamond;
        case 'fire':
          return Icons.local_fire_department;
        case 'heart':
          return Icons.favorite;
        case 'shield':
          return Icons.shield;
        default:
          return achievementType.icon;
      }
    }
    return achievementType.icon;
  }

  /// Get display color
  Color get displayColor {
    return achievementType.color;
  }

  /// Check if achievement is ready to be claimed
  bool get isReadyToClaim {
    return current >= target && !isCompleted;
  }

  /// Get remaining progress needed
  int get remainingProgress {
    return (target - current).clamp(0, target);
  }

  /// Create a copy with updated progress
  AchievementModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    int? target,
    int? current,
    String? iconName,
    String? type,
    int? rewardPoints,
    int? rewardDiamonds,
    bool? isCompleted,
    DateTime? completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AchievementModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      target: target ?? this.target,
      current: current ?? this.current,
      iconName: iconName ?? this.iconName,
      type: type ?? this.type,
      rewardPoints: rewardPoints ?? this.rewardPoints,
      rewardDiamonds: rewardDiamonds ?? this.rewardDiamonds,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Model for user achievement unlocks
class UserAchievementModel {
  final String id;
  final String userId;
  final String achievementId;
  final DateTime unlockedAt;
  final bool rewardClaimed;
  final DateTime? rewardClaimedAt;
  final AchievementModel? achievement;

  const UserAchievementModel({
    required this.id,
    required this.userId,
    required this.achievementId,
    required this.unlockedAt,
    required this.rewardClaimed,
    this.rewardClaimedAt,
    this.achievement,
  });

  factory UserAchievementModel.fromJson(Map<String, dynamic> json) {
    return UserAchievementModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      achievementId: json['achievement_id'] as String,
      unlockedAt: DateTime.parse(json['unlocked_at'] as String),
      rewardClaimed: json['reward_claimed'] as bool? ?? false,
      rewardClaimedAt: json['reward_claimed_at'] != null 
          ? DateTime.parse(json['reward_claimed_at'] as String) 
          : null,
      achievement: json['achievements'] != null 
          ? AchievementModel.fromJson(json['achievements'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'achievement_id': achievementId,
      'unlocked_at': unlockedAt.toIso8601String(),
      'reward_claimed': rewardClaimed,
      'reward_claimed_at': rewardClaimedAt?.toIso8601String(),
      'achievements': achievement?.toJson(),
    };
  }

  /// Check if reward is ready to be claimed
  bool get isRewardReadyToClaim {
    return !rewardClaimed && achievement != null;
  }
}

/// Model for achievement progress update
class AchievementProgressUpdate {
  final String achievementId;
  final int progressIncrement;
  final String? description;

  const AchievementProgressUpdate({
    required this.achievementId,
    required this.progressIncrement,
    this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'achievement_id': achievementId,
      'progress_increment': progressIncrement,
      'description': description,
    };
  }
}
