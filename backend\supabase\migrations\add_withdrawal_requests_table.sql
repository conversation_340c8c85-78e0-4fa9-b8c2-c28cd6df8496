-- Create withdrawal_requests table for manual withdrawal processing
-- This table tracks withdrawal requests that need admin approval

CREATE TABLE IF NOT EXISTS withdrawal_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
  transaction_id UUID NOT NULL REFERENCES wallet_transactions(id) ON DELETE CASCADE,
  
  -- Withdrawal details
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  currency VARCHAR(3) NOT NULL DEFAULT 'INR',
  account_details JSONB NOT NULL,
  
  -- Request status and processing
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'completed', 'rejected')),
  admin_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  admin_notes TEXT,
  
  -- Timestamps
  requested_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE, -- When admin approved/rejected
  completed_at TIMESTAMP WITH TIME ZONE, -- When money was actually transferred
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(transaction_id), -- One withdrawal request per transaction
  
  -- Ensure processed_at is set when status changes from pending
  CONSTRAINT check_processed_at CHECK (
    (status = 'pending' AND processed_at IS NULL) OR
    (status != 'pending' AND processed_at IS NOT NULL)
  ),
  
  -- Ensure completed_at is set only when status is completed
  CONSTRAINT check_completed_at CHECK (
    (status != 'completed' AND completed_at IS NULL) OR
    (status = 'completed' AND completed_at IS NOT NULL)
  ),
  
  -- Ensure admin_id is set when status is not pending
  CONSTRAINT check_admin_id CHECK (
    (status = 'pending' AND admin_id IS NULL) OR
    (status != 'pending' AND admin_id IS NOT NULL)
  )
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_user_id ON withdrawal_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_status ON withdrawal_requests(status);
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_requested_at ON withdrawal_requests(requested_at DESC);
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_admin_id ON withdrawal_requests(admin_id);
CREATE INDEX IF NOT EXISTS idx_withdrawal_requests_transaction_id ON withdrawal_requests(transaction_id);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_withdrawal_requests_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  
  -- Auto-set processed_at when status changes from pending
  IF OLD.status = 'pending' AND NEW.status != 'pending' AND NEW.processed_at IS NULL THEN
    NEW.processed_at = NOW();
  END IF;
  
  -- Auto-set completed_at when status changes to completed
  IF OLD.status != 'completed' AND NEW.status = 'completed' AND NEW.completed_at IS NULL THEN
    NEW.completed_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_withdrawal_requests_timestamp
BEFORE UPDATE ON withdrawal_requests
FOR EACH ROW
EXECUTE FUNCTION update_withdrawal_requests_timestamp();

-- Enable Row Level Security
ALTER TABLE withdrawal_requests ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users can only view their own withdrawal requests
CREATE POLICY "Users can view their own withdrawal requests" 
  ON withdrawal_requests FOR SELECT 
  USING (auth.uid() = user_id);

-- Users cannot insert, update, or delete withdrawal requests directly
-- These operations should only be done through the API with proper validation

-- Admins can view all withdrawal requests
CREATE POLICY "Admins can view all withdrawal requests"
  ON withdrawal_requests FOR SELECT
  USING (
    auth.jwt() ->> 'role' = 'admin' OR
    auth.jwt() ->> 'role' = 'superadmin'
  );

-- Admins can update withdrawal requests (for approval/rejection)
CREATE POLICY "Admins can update withdrawal requests"
  ON withdrawal_requests FOR UPDATE
  USING (
    auth.jwt() ->> 'role' = 'admin' OR
    auth.jwt() ->> 'role' = 'superadmin'
  );

-- System can insert withdrawal requests (through API)
CREATE POLICY "System can insert withdrawal requests"
  ON withdrawal_requests FOR INSERT
  WITH CHECK (true); -- Will be controlled by API authentication

-- Create a view for admin dashboard with user details
CREATE OR REPLACE VIEW withdrawal_requests_admin_view AS
SELECT 
  wr.*,
  u.name as user_name,
  u.email as user_email,
  u.phone as user_phone,
  wt.payment_method,
  wt.metadata as transaction_metadata,
  admin_user.name as admin_name
FROM withdrawal_requests wr
JOIN auth.users u ON wr.user_id = u.id
JOIN wallet_transactions wt ON wr.transaction_id = wt.id
LEFT JOIN auth.users admin_user ON wr.admin_id = admin_user.id
ORDER BY wr.requested_at DESC;

-- Grant access to the view for admins
GRANT SELECT ON withdrawal_requests_admin_view TO authenticated;

-- Create RLS policy for the view
CREATE POLICY "Admins can view withdrawal requests admin view"
  ON withdrawal_requests_admin_view FOR SELECT
  USING (
    auth.jwt() ->> 'role' = 'admin' OR
    auth.jwt() ->> 'role' = 'superadmin'
  );

-- Add comment for documentation
COMMENT ON TABLE withdrawal_requests IS 'Stores manual withdrawal requests that require admin approval and processing';
COMMENT ON COLUMN withdrawal_requests.status IS 'pending: awaiting admin review, approved: admin approved for processing, completed: money transferred, rejected: admin rejected request';
COMMENT ON COLUMN withdrawal_requests.account_details IS 'JSON object containing bank account details for the withdrawal';
COMMENT ON COLUMN withdrawal_requests.admin_notes IS 'Notes added by admin during processing (reason for rejection, etc.)';
