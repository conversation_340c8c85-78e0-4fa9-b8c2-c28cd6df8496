/**
 * Minimal Razorpay UI Test
 * This creates a simple test to verify Razorpay payment gateway UI
 */

import 'package:flutter/material.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';

void main() {
  runApp(RazorpayTestApp());
}

class RazorpayTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Razorpay UI Test',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: RazorpayTestScreen(),
    );
  }
}

class RazorpayTestScreen extends StatefulWidget {
  @override
  _RazorpayTestScreenState createState() => _RazorpayTestScreenState();
}

class _RazorpayTestScreenState extends State<RazorpayTestScreen> {
  late Razorpay _razorpay;
  String _status = 'Ready to test';

  @override
  void initState() {
    super.initState();
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  @override
  void dispose() {
    super.dispose();
    _razorpay.clear();
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    setState(() {
      _status = 'Payment Success: ${response.paymentId}';
    });
    print('Payment Success: ${response.paymentId}');
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    setState(() {
      _status = 'Payment Error: ${response.code} - ${response.message}';
    });
    print('Payment Error: ${response.code} - ${response.message}');
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    setState(() {
      _status = 'External Wallet: ${response.walletName}';
    });
    print('External Wallet: ${response.walletName}');
  }

  void _startPayment() {
    var options = {
      'key': 'rzp_test_iVqMylHzm5WcBc', // Your actual test key
      'amount': 10000, // ₹100 in paise
      'currency': 'INR',
      'name': 'WiggyZ Test',
      'description': 'Test Payment',
      'order_id': 'order_test_${DateTime.now().millisecondsSinceEpoch}',
      'prefill': {
        'contact': '**********',
        'email': '<EMAIL>',
        'name': 'Test User',
      },
      'theme': {
        'color': '#FFCC00',
      },
      'method': {
        'card': true,
        'netbanking': true,
        'wallet': true,
        'upi': true,
        'emi': false,
      },
      'config': {
        'display': {
          'blocks': {
            'other': {
              'name': 'Pay with Card',
              'instruments': [
                {
                  'method': 'card',
                  'issuers': ['VISA', 'MASTERCARD']
                }
              ]
            }
          },
          'sequence': ['block.other'],
          'preferences': {
            'show_default_blocks': true
          }
        }
      }
    };

    try {
      setState(() {
        _status = 'Opening Razorpay...';
      });
      _razorpay.open(options);
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
      print('Error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Razorpay UI Test'),
        backgroundColor: Color(0xFFFFA500),
      ),
      body: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.payment,
              size: 80,
              color: Color(0xFFFFA500),
            ),
            SizedBox(height: 20),
            Text(
              'Razorpay Payment Gateway Test',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Status: $_status',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 30),
            ElevatedButton(
              onPressed: _startPayment,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFFFFA500),
                padding: EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Test Payment (₹100)',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            SizedBox(height: 20),
            Text(
              'Test Card: 4111 1111 1111 1111\nCVV: 123 | Expiry: 12/25',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            Text(
              'Instructions:\n'
              '1. Tap "Test Payment"\n'
              '2. Check if card input fields appear\n'
              '3. Try entering test card details\n'
              '4. Report what you see',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
