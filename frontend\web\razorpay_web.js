// Razorpay Web SDK Integration for Flutter Web
// This script provides web-compatible Razorpay payment functionality

// Load Razorpay script dynamically
function loadRazorpayScript() {
  return new Promise((resolve, reject) => {
    if (window.Razorpay) {
      resolve(window.Razorpay);
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.onload = () => {
      if (window.Razorpay) {
        resolve(window.Razorpay);
      } else {
        reject(new Error('Razorpay script loaded but Razorpay object not found'));
      }
    };
    script.onerror = () => reject(new Error('Failed to load Razorpay script'));
    document.head.appendChild(script);
  });
}

// Initialize Razorpay Web Payment
async function initializeRazorpayWeb(options) {
  try {
    const Razorpay = await loadRazorpayScript();
    
    const rzp = new Razorpay({
      key: options.key,
      amount: options.amount,
      currency: options.currency,
      name: options.name,
      description: options.description,
      order_id: options.order_id,
      prefill: options.prefill,
      theme: options.theme,
      modal: {
        ondismiss: function() {
          console.log('Payment modal dismissed');
          if (options.onDismiss) {
            options.onDismiss();
          }
        }
      },
      handler: function(response) {
        console.log('Payment Success:', response);
        if (options.onSuccess) {
          options.onSuccess({
            razorpay_payment_id: response.razorpay_payment_id,
            razorpay_order_id: response.razorpay_order_id,
            razorpay_signature: response.razorpay_signature
          });
        }
      }
    });

    rzp.on('payment.failed', function(response) {
      console.log('Payment Failed:', response);
      if (options.onError) {
        options.onError({
          code: response.error.code,
          description: response.error.description,
          source: response.error.source,
          step: response.error.step,
          reason: response.error.reason,
          order_id: options.order_id
        });
      }
    });

    return rzp;
  } catch (error) {
    console.error('Error initializing Razorpay:', error);
    throw error;
  }
}

// Start payment process
function startRazorpayWebPayment(options) {
  console.log('Starting Razorpay web payment with options:', options);

  // Initialize and start payment asynchronously but return immediately
  initializeRazorpayWeb(options)
    .then(rzp => {
      console.log('Razorpay instance created, opening payment modal...');
      rzp.open();
    })
    .catch(error => {
      console.error('Error starting payment:', error);
      if (options.onError) {
        options.onError({
          code: 'INITIALIZATION_ERROR',
          description: error.message,
          source: 'web_sdk',
          step: 'initialization',
          reason: 'script_load_failed',
          order_id: options.order_id
        });
      }
    });

  // Return true immediately to indicate the process started
  return true;
}

// Test payment with dummy data
function testRazorpayWebPayment() {
  const testOptions = {
    key: 'rzp_test_iVqMylHzm5WcBc', // Your test key
    amount: 10000, // ₹100 in paise
    currency: 'INR',
    name: 'WiggyZ Test',
    description: 'Test Payment - Web SDK',
    order_id: 'test_order_' + Date.now(),
    prefill: {
      name: 'Test User',
      email: '<EMAIL>',
      contact: '9999999999'
    },
    theme: {
      color: '#FFCC00'
    },
    onSuccess: function(response) {
      console.log('Test Payment Success:', response);
      alert('Payment Successful!\nPayment ID: ' + response.razorpay_payment_id);
    },
    onError: function(response) {
      console.log('Test Payment Error:', response);
      alert('Payment Failed!\nError: ' + response.description);
    },
    onDismiss: function() {
      console.log('Test Payment Dismissed');
      alert('Payment was cancelled');
    }
  };

  startRazorpayWebPayment(testOptions);
}

// Make functions available globally
window.razorpayWeb = {
  loadScript: loadRazorpayScript,
  initialize: initializeRazorpayWeb,
  startPayment: startRazorpayWebPayment,
  testPayment: testRazorpayWebPayment
};

// Also expose the main function globally for easier access from Dart
window.startRazorpayWebPayment = startRazorpayWebPayment;

console.log('Razorpay Web SDK integration loaded successfully');
