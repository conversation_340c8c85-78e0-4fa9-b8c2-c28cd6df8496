#!/usr/bin/env node

/**
 * Deployment Status Checker for WiggyZ Backend
 * Provides comprehensive status of deployment system and current environment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DeploymentStatusChecker {
  constructor() {
    this.rootDir = path.resolve(__dirname, '../..');
    this.backendDir = path.join(this.rootDir, 'backend');
    this.configPath = path.join(this.rootDir, 'config', 'environments.json');
    this.currentEnvPath = path.join(this.rootDir, 'config', 'current-environment.json');
  }

  log(message, type = 'info', category = 'general') {
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} [${category.toUpperCase()}] ${message}`);
  }

  checkFileExists(filePath, description) {
    const exists = fs.existsSync(filePath);
    this.log(`${description}: ${exists ? 'EXISTS' : 'MISSING'}`, exists ? 'success' : 'error', 'files');
    return exists;
  }

  checkDeploymentFiles() {
    this.log('Checking deployment system files...', 'info', 'files');
    
    const files = [
      { path: this.configPath, desc: 'Environment Configuration' },
      { path: this.currentEnvPath, desc: 'Current Environment Marker' },
      { path: path.join(this.backendDir, 'vercel.json'), desc: 'Vercel Configuration' },
      { path: path.join(this.backendDir, 'package.json'), desc: 'Backend Package.json' },
      { path: path.join(this.backendDir, 'dist', 'vercel.js'), desc: 'Built Vercel Entry Point' },
      { path: path.join(__dirname, 'deploy.js'), desc: 'Main Deployment Script' },
      { path: path.join(__dirname, 'environment-manager.js'), desc: 'Environment Manager' },
      { path: path.join(__dirname, 'validators.js'), desc: 'Deployment Validators' },
      { path: path.join(__dirname, 'vercel-config.js'), desc: 'Vercel Config Generator' }
    ];

    let existingFiles = 0;
    files.forEach(file => {
      if (this.checkFileExists(file.path, file.desc)) {
        existingFiles++;
      }
    });

    this.log(`Deployment files: ${existingFiles}/${files.length} present`, 
      existingFiles === files.length ? 'success' : 'warn', 'files');
    
    return existingFiles === files.length;
  }

  getCurrentEnvironment() {
    try {
      if (fs.existsSync(this.currentEnvPath)) {
        const currentEnv = JSON.parse(fs.readFileSync(this.currentEnvPath, 'utf8'));
        return currentEnv;
      }
      return null;
    } catch (error) {
      this.log(`Failed to read current environment: ${error.message}`, 'error', 'environment');
      return null;
    }
  }

  getEnvironmentConfigurations() {
    try {
      const environments = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
      return environments;
    } catch (error) {
      this.log(`Failed to read environment configurations: ${error.message}`, 'error', 'environment');
      return null;
    }
  }

  checkEnvironmentStatus() {
    this.log('Checking environment configuration...', 'info', 'environment');
    
    const currentEnv = this.getCurrentEnvironment();
    const environments = this.getEnvironmentConfigurations();
    
    if (!environments) {
      this.log('Environment configurations not available', 'error', 'environment');
      return false;
    }

    if (!currentEnv) {
      this.log('No current environment set (defaulting to development)', 'warn', 'environment');
    } else {
      this.log(`Current environment: ${currentEnv.environment}`, 'info', 'environment');
      this.log(`Switched at: ${currentEnv.switchedAt}`, 'info', 'environment');
      this.log(`Backend URL: ${currentEnv.config.backend.url}`, 'info', 'environment');
    }

    // Check available environments
    const envNames = Object.keys(environments);
    this.log(`Available environments: ${envNames.join(', ')}`, 'info', 'environment');
    
    return true;
  }

  checkBuildStatus() {
    this.log('Checking build status...', 'info', 'build');
    
    const distPath = path.join(this.backendDir, 'dist');
    const serverPath = path.join(distPath, 'server.js');
    const vercelPath = path.join(distPath, 'vercel.js');
    
    if (!fs.existsSync(distPath)) {
      this.log('Build directory not found', 'error', 'build');
      return false;
    }

    const serverExists = fs.existsSync(serverPath);
    const vercelExists = fs.existsSync(vercelPath);
    
    this.log(`Server build: ${serverExists ? 'PRESENT' : 'MISSING'}`, 
      serverExists ? 'success' : 'error', 'build');
    this.log(`Vercel entry point: ${vercelExists ? 'PRESENT' : 'MISSING'}`, 
      vercelExists ? 'success' : 'error', 'build');

    if (vercelExists) {
      const stats = fs.statSync(vercelPath);
      const buildAge = Date.now() - stats.mtime.getTime();
      const ageMinutes = Math.floor(buildAge / (1000 * 60));
      
      this.log(`Build age: ${ageMinutes} minutes`, 
        ageMinutes < 60 ? 'success' : 'warn', 'build');
    }

    return serverExists && vercelExists;
  }

  checkVercelCLI() {
    this.log('Checking Vercel CLI...', 'info', 'vercel');
    
    try {
      const version = execSync('npx vercel --version', { encoding: 'utf8', stdio: 'pipe' });
      this.log(`Vercel CLI version: ${version.trim()}`, 'success', 'vercel');
      return true;
    } catch (error) {
      this.log('Vercel CLI not available', 'error', 'vercel');
      return false;
    }
  }

  checkAppConfigurations() {
    this.log('Checking app configurations...', 'info', 'apps');
    
    // Check Flutter config
    const flutterConfigPath = path.join(this.rootDir, 'frontend', 'lib', 'core', 'api', 'api_config.dart');
    const flutterExists = this.checkFileExists(flutterConfigPath, 'Flutter API Config');
    
    if (flutterExists) {
      try {
        const content = fs.readFileSync(flutterConfigPath, 'utf8');
        const urlMatch = content.match(/return\s+['"`]([^'"`]+)['"`];/);
        if (urlMatch) {
          this.log(`Flutter API URL: ${urlMatch[1]}`, 'info', 'apps');
        }
      } catch (error) {
        this.log('Failed to read Flutter config', 'warn', 'apps');
      }
    }

    // Check Admin config
    const adminConfigPath = path.join(this.rootDir, 'wiggyz_admin', '.env.local');
    const adminExists = this.checkFileExists(adminConfigPath, 'Admin Dashboard Config');
    
    if (adminExists) {
      try {
        const content = fs.readFileSync(adminConfigPath, 'utf8');
        const urlMatch = content.match(/NEXT_PUBLIC_API_URL=(.+)/);
        if (urlMatch) {
          this.log(`Admin API URL: ${urlMatch[1].trim()}`, 'info', 'apps');
        }
      } catch (error) {
        this.log('Failed to read Admin config', 'warn', 'apps');
      }
    }

    return flutterExists && adminExists;
  }

  generateStatusReport() {
    const report = {
      timestamp: new Date().toISOString(),
      deploymentFiles: this.checkDeploymentFiles(),
      environmentStatus: this.checkEnvironmentStatus(),
      buildStatus: this.checkBuildStatus(),
      vercelCLI: this.checkVercelCLI(),
      appConfigurations: this.checkAppConfigurations(),
      currentEnvironment: this.getCurrentEnvironment(),
      environments: this.getEnvironmentConfigurations()
    };

    const allChecks = [
      report.deploymentFiles,
      report.environmentStatus,
      report.buildStatus,
      report.vercelCLI,
      report.appConfigurations
    ];

    const passedChecks = allChecks.filter(Boolean).length;
    const totalChecks = allChecks.length;

    report.overallStatus = {
      passed: passedChecks,
      total: totalChecks,
      percentage: Math.round((passedChecks / totalChecks) * 100),
      ready: passedChecks === totalChecks
    };

    return report;
  }

  displaySummary(report) {
    this.log('\n📊 DEPLOYMENT STATUS SUMMARY', 'info', 'summary');
    this.log(`Overall Status: ${report.overallStatus.passed}/${report.overallStatus.total} checks passed (${report.overallStatus.percentage}%)`, 
      report.overallStatus.ready ? 'success' : 'warn', 'summary');
    
    if (report.overallStatus.ready) {
      this.log('🎉 Deployment system is fully configured and ready!', 'success', 'summary');
    } else if (report.overallStatus.percentage >= 80) {
      this.log('⚠️ Deployment system is mostly ready with minor issues', 'warn', 'summary');
    } else {
      this.log('❌ Deployment system has significant issues that need attention', 'error', 'summary');
    }

    if (report.currentEnvironment) {
      this.log(`\n🌍 Current Environment: ${report.currentEnvironment.environment.toUpperCase()}`, 'info', 'summary');
      this.log(`Backend URL: ${report.currentEnvironment.config.backend.url}`, 'info', 'summary');
    }
  }

  runStatusCheck() {
    this.log('🔍 WiggyZ Deployment Status Check', 'info', 'status');
    this.log('=' * 50, 'info', 'status');
    
    const report = this.generateStatusReport();
    this.displaySummary(report);
    
    return report;
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const checker = new DeploymentStatusChecker();
  
  if (args.includes('--json')) {
    const report = checker.generateStatusReport();
    console.log(JSON.stringify(report, null, 2));
  } else {
    const report = checker.runStatusCheck();
    process.exit(report.overallStatus.ready ? 0 : 1);
  }
}

module.exports = DeploymentStatusChecker;
