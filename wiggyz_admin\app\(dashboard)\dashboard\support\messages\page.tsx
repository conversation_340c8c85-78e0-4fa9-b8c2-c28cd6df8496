"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { 
  MessageSquare, 
  Search, 
  Filter, 
  Eye, 
  Reply, 
  Clock, 
  User, 
  AlertCircle,
  CheckCircle,
  XCircle,
  MoreHorizontal
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface SupportMessage {
  id: string
  user_id: string
  subject: string
  category: string
  message: string
  status: string
  priority: string
  admin_notes?: string
  assigned_to?: string
  created_at: string
  updated_at: string
  resolved_at?: string
  user?: {
    id: string
    name: string
    email: string
  }
  assigned_admin?: {
    id: string
    name: string
    email: string
  }
  replies?: Array<{
    id: string
    reply_text: string
    is_internal: boolean
    created_at: string
    admin: {
      id: string
      name: string
      email: string
    }
  }>
}

const statusColors = {
  new: "bg-blue-100 text-blue-800",
  in_progress: "bg-yellow-100 text-yellow-800", 
  resolved: "bg-green-100 text-green-800",
  closed: "bg-gray-100 text-gray-800"
}

const priorityColors = {
  low: "bg-gray-100 text-gray-800",
  normal: "bg-blue-100 text-blue-800",
  high: "bg-orange-100 text-orange-800",
  urgent: "bg-red-100 text-red-800"
}

const categoryLabels = {
  bug_report: "Bug Report",
  feature_request: "Feature Request", 
  general_inquiry: "General Inquiry",
  account_issue: "Account Issue",
  payment_issue: "Payment Issue",
  technical_support: "Technical Support"
}

export default function SupportMessagesPage() {
  const [messages, setMessages] = useState<SupportMessage[]>([])
  const [filteredMessages, setFilteredMessages] = useState<SupportMessage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [selectedMessage, setSelectedMessage] = useState<SupportMessage | null>(null)
  const [replyText, setReplyText] = useState("")
  const [isInternal, setIsInternal] = useState(false)
  const [isSubmittingReply, setIsSubmittingReply] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchSupportMessages()
  }, [])

  useEffect(() => {
    filterMessages()
  }, [messages, searchTerm, statusFilter, categoryFilter, priorityFilter])

  const fetchSupportMessages = async () => {
    try {
      setIsLoading(true)

      const response = await fetch('/api/support/messages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch support messages')
      }

      const result = await response.json()
      // Backend returns { data: { messages: [...], pagination: {...} } }
      const messagesArray = result.data?.messages || []
      setMessages(Array.isArray(messagesArray) ? messagesArray : [])
    } catch (error) {
      console.error('Error fetching support messages:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load support messages",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const filterMessages = () => {
    // Ensure messages is always an array
    if (!Array.isArray(messages)) {
      console.warn('Messages is not an array:', messages)
      setFilteredMessages([])
      return
    }

    let filtered = messages

    if (searchTerm) {
      filtered = filtered.filter(message =>
        message.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.message?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.user?.email?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(message => message.status === statusFilter)
    }

    if (categoryFilter !== "all") {
      filtered = filtered.filter(message => message.category === categoryFilter)
    }

    if (priorityFilter !== "all") {
      filtered = filtered.filter(message => message.priority === priorityFilter)
    }

    setFilteredMessages(filtered)
  }

  const updateMessageStatus = async (messageId: string, status: string) => {
    try {
      const response = await fetch(`/api/support/messages/${messageId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to update message status')
      }

      await fetchSupportMessages()
      toast({
        title: "Success",
        description: `Message status updated to ${status}`
      })
    } catch (error) {
      console.error('Error updating message status:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update message status",
        variant: "destructive"
      })
    }
  }

  const submitReply = async () => {
    if (!selectedMessage || !replyText.trim()) return

    try {
      setIsSubmittingReply(true)

      const response = await fetch(`/api/support/messages/${selectedMessage.id}/replies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reply_text: replyText,
          is_internal: isInternal
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to send reply')
      }

      // Update message status to in_progress if it's new
      if (selectedMessage.status === 'new') {
        await updateMessageStatus(selectedMessage.id, 'in_progress')
      }

      setReplyText("")
      setIsInternal(false)
      await fetchSupportMessages()

      toast({
        title: "Success",
        description: "Reply sent successfully"
      })
    } catch (error) {
      console.error('Error submitting reply:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send reply",
        variant: "destructive"
      })
    } finally {
      setIsSubmittingReply(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new': return <AlertCircle className="h-4 w-4" />
      case 'in_progress': return <Clock className="h-4 w-4" />
      case 'resolved': return <CheckCircle className="h-4 w-4" />
      case 'closed': return <XCircle className="h-4 w-4" />
      default: return <MessageSquare className="h-4 w-4" />
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-4">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Support Messages</h1>
          <p className="text-muted-foreground">Manage customer support requests and inquiries</p>
        </div>
        <Button onClick={fetchSupportMessages}>
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search messages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="new">New</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="bug_report">Bug Report</SelectItem>
                <SelectItem value="feature_request">Feature Request</SelectItem>
                <SelectItem value="general_inquiry">General Inquiry</SelectItem>
                <SelectItem value="account_issue">Account Issue</SelectItem>
                <SelectItem value="payment_issue">Payment Issue</SelectItem>
                <SelectItem value="technical_support">Technical Support</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Messages List */}
      <div className="grid gap-4">
        {!Array.isArray(filteredMessages) || filteredMessages.length === 0 ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center">
                <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No messages found</h3>
                <p className="text-muted-foreground">
                  {!Array.isArray(filteredMessages)
                    ? "Error loading messages. Please refresh the page."
                    : "No support messages match your current filters."
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredMessages.map((message) => (
            <Card key={message.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusIcon(message.status)}
                      <h3 className="font-semibold">{message.subject}</h3>
                      <Badge className={statusColors[message.status as keyof typeof statusColors]}>
                        {message.status.replace('_', ' ')}
                      </Badge>
                      <Badge className={priorityColors[message.priority as keyof typeof priorityColors]}>
                        {message.priority}
                      </Badge>
                      <Badge variant="outline">
                        {categoryLabels[message.category as keyof typeof categoryLabels]}
                      </Badge>
                    </div>
                    <p className="text-muted-foreground mb-2 line-clamp-2">{message.message}</p>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {message.user?.name || 'Unknown User'}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                      </div>
                      {message.replies && message.replies.length > 0 && (
                        <div className="flex items-center gap-1">
                          <Reply className="h-4 w-4" />
                          {message.replies.length} replies
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setSelectedMessage(message)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle className="flex items-center gap-2">
                            {getStatusIcon(message.status)}
                            {message.subject}
                          </DialogTitle>
                          <DialogDescription>
                            Support message from {message.user?.name} • {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                          </DialogDescription>
                        </DialogHeader>
                        
                        <div className="space-y-6">
                          {/* Message Details */}
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label>Status</Label>
                              <Select 
                                value={message.status} 
                                onValueChange={(value) => updateMessageStatus(message.id, value)}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="new">New</SelectItem>
                                  <SelectItem value="in_progress">In Progress</SelectItem>
                                  <SelectItem value="resolved">Resolved</SelectItem>
                                  <SelectItem value="closed">Closed</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label>Priority</Label>
                              <Badge className={priorityColors[message.priority as keyof typeof priorityColors]}>
                                {message.priority}
                              </Badge>
                            </div>
                          </div>

                          {/* Original Message */}
                          <div>
                            <Label>Original Message</Label>
                            <div className="mt-2 p-4 bg-muted rounded-lg">
                              <p>{message.message}</p>
                            </div>
                          </div>

                          {/* Replies */}
                          {message.replies && message.replies.length > 0 && (
                            <div>
                              <Label>Replies</Label>
                              <div className="mt-2 space-y-3">
                                {message.replies.map((reply) => (
                                  <div key={reply.id} className={`p-4 rounded-lg ${reply.is_internal ? 'bg-yellow-50 border-l-4 border-yellow-400' : 'bg-blue-50 border-l-4 border-blue-400'}`}>
                                    <div className="flex items-center justify-between mb-2">
                                      <div className="flex items-center gap-2">
                                        <span className="font-medium">{reply.admin.name}</span>
                                        {reply.is_internal && (
                                          <Badge variant="outline" className="text-xs">Internal</Badge>
                                        )}
                                      </div>
                                      <span className="text-xs text-muted-foreground">
                                        {formatDistanceToNow(new Date(reply.created_at), { addSuffix: true })}
                                      </span>
                                    </div>
                                    <p>{reply.reply_text}</p>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Reply Form */}
                          <div>
                            <Label>Send Reply</Label>
                            <div className="mt-2 space-y-3">
                              <Textarea
                                placeholder="Type your reply..."
                                value={replyText}
                                onChange={(e) => setReplyText(e.target.value)}
                                rows={4}
                              />
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <input
                                    type="checkbox"
                                    id="internal"
                                    checked={isInternal}
                                    onChange={(e) => setIsInternal(e.target.checked)}
                                  />
                                  <Label htmlFor="internal">Internal note (not visible to user)</Label>
                                </div>
                                <Button 
                                  onClick={submitReply}
                                  disabled={!replyText.trim() || isSubmittingReply}
                                >
                                  {isSubmittingReply ? "Sending..." : "Send Reply"}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
