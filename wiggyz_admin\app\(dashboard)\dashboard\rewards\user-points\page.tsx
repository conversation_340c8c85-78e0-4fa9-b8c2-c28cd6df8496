"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, Plus, ArrowLeft, Edit, Loader2, User } from "lucide-react"
import Link from "next/link"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"

// Types for user loyalty data
interface UserLoyalty {
  user_id: string
  points: number
  tier_id: string
  created_at: string
  updated_at: string
  users: {
    name: string
    email: string
  }
  loyalty_tiers: {
    name: string
    min_points: number
  }
}

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:5000/api/v1'

// Get API token from Supabase session
const getApiToken = async () => {
  const { createClient } = await import('@/lib/supabaseClient')
  const supabase = createClient()
  const { data: { session } } = await supabase.auth.getSession()

  if (!session?.access_token) {
    throw new Error('No authentication session')
  }

  // Use Supabase token directly - backend can handle Supabase JWT tokens
  return session.access_token
}

// API functions
const fetchUserLoyalty = async (): Promise<UserLoyalty[]> => {
  const token = await getApiToken()
  const response = await fetch(`${API_BASE_URL}/rewards/admin/user-loyalty`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  })

  if (!response.ok) {
    throw new Error('Failed to fetch user loyalty data')
  }

  const data = await response.json()
  return data.data || []
}

const updateUserPoints = async (userId: string, points: number, reason: string): Promise<void> => {
  const token = await getApiToken()
  const response = await fetch(`${API_BASE_URL}/rewards/admin/user-points`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      user_id: userId,
      points,
      reason,
    }),
  })

  if (!response.ok) {
    throw new Error('Failed to update user points')
  }
}

export default function UserPointsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAdjustPointsOpen, setIsAdjustPointsOpen] = useState(false)
  const [userLoyalty, setUserLoyalty] = useState<UserLoyalty[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedUser, setSelectedUser] = useState<UserLoyalty | null>(null)
  const [pointsAdjustment, setPointsAdjustment] = useState({
    points: "",
    reason: "",
  })
  const { toast } = useToast()

  // Load user loyalty data on component mount
  useEffect(() => {
    loadUserLoyalty()
  }, [])

  const loadUserLoyalty = async () => {
    try {
      setLoading(true)
      const data = await fetchUserLoyalty()
      setUserLoyalty(data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load user loyalty data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredUsers = userLoyalty.filter((user) => {
    return user.users.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
           user.users.email.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const handleAdjustPoints = (user: UserLoyalty) => {
    setSelectedUser(user)
    setPointsAdjustment({
      points: "",
      reason: "",
    })
    setIsAdjustPointsOpen(true)
  }

  const handleUpdatePoints = async () => {
    try {
      if (!selectedUser || !pointsAdjustment.points || !pointsAdjustment.reason) {
        toast({
          title: "Error",
          description: "Please fill in all fields",
          variant: "destructive",
        })
        return
      }

      await updateUserPoints(
        selectedUser.user_id,
        parseInt(pointsAdjustment.points),
        pointsAdjustment.reason
      )

      toast({
        title: "Success",
        description: "User points updated successfully",
      })

      setIsAdjustPointsOpen(false)
      setSelectedUser(null)
      setPointsAdjustment({
        points: "",
        reason: "",
      })

      // Reload user loyalty data
      loadUserLoyalty()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user points",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <Link href="/dashboard/rewards">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Back</span>
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">User Points Management</h1>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>User Loyalty Points</CardTitle>
              <CardDescription>Manage user loyalty points and tier status</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex w-full items-center gap-2 sm:max-w-sm">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="h-9"
              />
            </div>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Current Points</TableHead>
                    <TableHead>Tier</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                        <p className="mt-2 text-muted-foreground">Loading user data...</p>
                      </TableCell>
                    </TableRow>
                  ) : filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-8">
                        <p className="text-muted-foreground">No users found</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((user) => (
                      <TableRow key={user.user_id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                              <User className="h-4 w-4" />
                            </div>
                            <div>
                              <p className="font-medium">{user.users.name}</p>
                              <p className="text-sm text-muted-foreground">{user.users.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{user.points.toLocaleString()} points</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{user.loyalty_tiers.name}</span>
                            <span className="text-sm text-muted-foreground">
                              ({user.loyalty_tiers.min_points.toLocaleString()} pts required)
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm" onClick={() => handleAdjustPoints(user)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Adjust Points
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Adjust Points Dialog */}
      <Dialog open={isAdjustPointsOpen} onOpenChange={setIsAdjustPointsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adjust User Points</DialogTitle>
            <DialogDescription>
              Adjust loyalty points for {selectedUser?.users.name}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="points">Points Adjustment</Label>
              <Input
                id="points"
                type="number"
                value={pointsAdjustment.points}
                onChange={(e) => setPointsAdjustment({ ...pointsAdjustment, points: e.target.value })}
                placeholder="Enter points (positive to add, negative to subtract)"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                value={pointsAdjustment.reason}
                onChange={(e) => setPointsAdjustment({ ...pointsAdjustment, reason: e.target.value })}
                placeholder="Enter reason for adjustment"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAdjustPointsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdatePoints}>Update Points</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
