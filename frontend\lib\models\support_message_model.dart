class SupportMessage {
  final String id;
  final String userId;
  final String subject;
  final String category;
  final String message;
  final String status;
  final String priority;
  final String? adminNotes;
  final String? assignedTo;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? resolvedAt;
  final User? user;
  final User? assignedAdmin;
  final List<SupportMessageReply>? replies;

  SupportMessage({
    required this.id,
    required this.userId,
    required this.subject,
    required this.category,
    required this.message,
    required this.status,
    required this.priority,
    this.adminNotes,
    this.assignedTo,
    required this.createdAt,
    required this.updatedAt,
    this.resolvedAt,
    this.user,
    this.assignedAdmin,
    this.replies,
  });

  factory SupportMessage.fromJson(Map<String, dynamic> json) {
    return SupportMessage(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      subject: json['subject'] as String,
      category: json['category'] as String,
      message: json['message'] as String,
      status: json['status'] as String,
      priority: json['priority'] as String,
      adminNotes: json['admin_notes'] as String?,
      assignedTo: json['assigned_to'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      resolvedAt: json['resolved_at'] != null 
        ? DateTime.parse(json['resolved_at'] as String)
        : null,
      user: json['user'] != null 
        ? User.fromJson(json['user'] as Map<String, dynamic>)
        : null,
      assignedAdmin: json['assigned_admin'] != null 
        ? User.fromJson(json['assigned_admin'] as Map<String, dynamic>)
        : null,
      replies: json['replies'] != null
        ? (json['replies'] as List)
            .map((reply) => SupportMessageReply.fromJson(reply as Map<String, dynamic>))
            .toList()
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'subject': subject,
      'category': category,
      'message': message,
      'status': status,
      'priority': priority,
      'admin_notes': adminNotes,
      'assigned_to': assignedTo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'resolved_at': resolvedAt?.toIso8601String(),
      if (user != null) 'user': user!.toJson(),
      if (assignedAdmin != null) 'assigned_admin': assignedAdmin!.toJson(),
      if (replies != null) 'replies': replies!.map((reply) => reply.toJson()).toList(),
    };
  }

  String get categoryLabel {
    switch (category) {
      case 'bug_report':
        return 'Bug Report';
      case 'feature_request':
        return 'Feature Request';
      case 'general_inquiry':
        return 'General Inquiry';
      case 'account_issue':
        return 'Account Issue';
      case 'payment_issue':
        return 'Payment Issue';
      case 'technical_support':
        return 'Technical Support';
      default:
        return category;
    }
  }

  String get statusLabel {
    switch (status) {
      case 'new':
        return 'New';
      case 'in_progress':
        return 'In Progress';
      case 'resolved':
        return 'Resolved';
      case 'closed':
        return 'Closed';
      default:
        return status;
    }
  }

  String get priorityLabel {
    switch (priority) {
      case 'low':
        return 'Low';
      case 'normal':
        return 'Normal';
      case 'high':
        return 'High';
      case 'urgent':
        return 'Urgent';
      default:
        return priority;
    }
  }
}

class SupportMessageReply {
  final String id;
  final String supportMessageId;
  final String? adminUserId;
  final String replyText;
  final bool isInternal;
  final DateTime createdAt;
  final User? admin;

  SupportMessageReply({
    required this.id,
    required this.supportMessageId,
    this.adminUserId,
    required this.replyText,
    required this.isInternal,
    required this.createdAt,
    this.admin,
  });

  factory SupportMessageReply.fromJson(Map<String, dynamic> json) {
    return SupportMessageReply(
      id: json['id'] as String,
      supportMessageId: json['support_message_id'] as String,
      adminUserId: json['admin_user_id'] as String?,
      replyText: json['reply_text'] as String,
      isInternal: json['is_internal'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      admin: json['admin'] != null 
        ? User.fromJson(json['admin'] as Map<String, dynamic>)
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'support_message_id': supportMessageId,
      'admin_user_id': adminUserId,
      'reply_text': replyText,
      'is_internal': isInternal,
      'created_at': createdAt.toIso8601String(),
      if (admin != null) 'admin': admin!.toJson(),
    };
  }
}

class User {
  final String id;
  final String name;
  final String email;

  User({
    required this.id,
    required this.name,
    required this.email,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
    };
  }
}
