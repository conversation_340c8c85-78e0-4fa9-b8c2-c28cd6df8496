/// Achievement card widget for displaying individual achievements
/// Shows progress, rewards, and completion status with beautiful animations

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/achievement_models.dart';

class AchievementCard extends StatefulWidget {
  final AchievementModel achievement;
  final bool isDarkMode;
  final VoidCallback? onTap;

  const AchievementCard({
    super.key,
    required this.achievement,
    required this.isDarkMode,
    this.onTap,
  });

  @override
  State<AchievementCard> createState() => _AchievementCardState();
}

class _AchievementCardState extends State<AchievementCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Start glow animation for ready to claim achievements
    if (widget.achievement.isReadyToClaim) {
      _startGlowAnimation();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startGlowAnimation() {
    _animationController.repeat(reverse: true);
  }

  void _stopGlowAnimation() {
    _animationController.stop();
    _animationController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: widget.onTap,
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: widget.isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _getBorderColor(),
                  width: widget.achievement.isReadyToClaim ? 2 : 1,
                ),
                boxShadow: _getBoxShadow(),
              ),
              child: Row(
                children: [
                  _buildAchievementIcon(),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTitleAndStatus(),
                        const SizedBox(height: 4),
                        _buildDescription(),
                        const SizedBox(height: 8),
                        _buildProgressSection(),
                        if (widget.achievement.rewardPoints != null || 
                            widget.achievement.rewardDiamonds != null) ...[
                          const SizedBox(height: 8),
                          _buildRewardsSection(),
                        ],
                      ],
                    ),
                  ),
                  _buildActionIcon(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getBorderColor() {
    if (widget.achievement.isReadyToClaim) {
      return const Color(0xFFFFCC00);
    } else if (widget.achievement.isCompleted) {
      return Colors.green;
    } else {
      return widget.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
    }
  }

  List<BoxShadow> _getBoxShadow() {
    if (widget.achievement.isReadyToClaim) {
      return [
        BoxShadow(
          color: const Color(0xFFFFCC00).withOpacity(0.3 * _glowAnimation.value),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ];
    } else if (widget.achievement.isCompleted) {
      return [
        BoxShadow(
          color: Colors.green.withOpacity(0.2),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    }
    return [
      BoxShadow(
        color: Colors.black.withOpacity(0.1),
        blurRadius: 4,
        offset: const Offset(0, 2),
      ),
    ];
  }

  Widget _buildAchievementIcon() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: widget.achievement.displayColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          Center(
            child: Icon(
              widget.achievement.displayIcon,
              color: widget.achievement.displayColor,
              size: 28,
            ),
          ),
          if (widget.achievement.isCompleted)
            Positioned(
              top: 2,
              right: 2,
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: widget.isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
                    width: 2,
                  ),
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 10,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTitleAndStatus() {
    return Row(
      children: [
        Expanded(
          child: Text(
            widget.achievement.title,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.white : Colors.black,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 8),
        _buildStatusBadge(),
      ],
    );
  }

  Widget _buildStatusBadge() {
    String text;
    Color backgroundColor;
    Color textColor;

    if (widget.achievement.isCompleted) {
      text = 'Completed';
      backgroundColor = Colors.green;
      textColor = Colors.white;
    } else if (widget.achievement.isReadyToClaim) {
      text = 'Ready';
      backgroundColor = const Color(0xFFFFCC00);
      textColor = Colors.black;
    } else {
      text = 'In Progress';
      backgroundColor = widget.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
      textColor = widget.isDarkMode ? Colors.white : Colors.black;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: GoogleFonts.poppins(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildDescription() {
    return Text(
      widget.achievement.description,
      style: GoogleFonts.poppins(
        fontSize: 12,
        color: widget.isDarkMode ? Colors.grey[400] : Colors.grey[600],
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildProgressSection() {
    if (widget.achievement.isCompleted) {
      return Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            'Achievement completed!',
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.green,
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: widget.isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
            Text(
              '${widget.achievement.current}/${widget.achievement.target}',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: widget.achievement.displayColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: widget.achievement.progressPercentage,
          backgroundColor: widget.isDarkMode ? Colors.grey[700] : Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(widget.achievement.displayColor),
          minHeight: 4,
        ),
      ],
    );
  }

  Widget _buildRewardsSection() {
    return Row(
      children: [
        Icon(
          Icons.card_giftcard,
          color: const Color(0xFFFFCC00),
          size: 14,
        ),
        const SizedBox(width: 4),
        if (widget.achievement.rewardPoints != null) ...[
          Text(
            '${widget.achievement.rewardPoints} pts',
            style: GoogleFonts.poppins(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: const Color(0xFFFFCC00),
            ),
          ),
          if (widget.achievement.rewardDiamonds != null)
            Text(
              ' • ',
              style: GoogleFonts.poppins(
                fontSize: 11,
                color: const Color(0xFFFFCC00),
              ),
            ),
        ],
        if (widget.achievement.rewardDiamonds != null)
          Text(
            '${widget.achievement.rewardDiamonds} diamonds',
            style: GoogleFonts.poppins(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: const Color(0xFFFFCC00),
            ),
          ),
      ],
    );
  }

  Widget _buildActionIcon() {
    if (widget.achievement.isReadyToClaim) {
      return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: const Color(0xFFFFCC00).withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.card_giftcard,
          color: const Color(0xFFFFCC00),
          size: 20,
        ),
      );
    } else if (widget.achievement.isCompleted) {
      return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.check,
          color: Colors.green,
          size: 20,
        ),
      );
    } else {
      return Icon(
        Icons.chevron_right,
        color: widget.isDarkMode ? Colors.grey[600] : Colors.grey[400],
        size: 20,
      );
    }
  }
}
