/// Comprehensive error handling and edge case management for the reward system
/// Provides user-friendly error messages and fallback behaviors

import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Enum for different types of reward errors
enum RewardErrorType {
  networkError,
  authenticationError,
  validationError,
  serverError,
  rateLimitError,
  rewardNotFound,
  rewardExpired,
  rewardAlreadyClaimed,
  insufficientProgress,
  dailyRewardAlreadyClaimed,
  streakBroken,
  loyaltyTierNotFound,
  achievementNotFound,
  unknownError,
}

/// Extension for reward error type utilities
extension RewardErrorTypeExtension on RewardErrorType {
  String get userFriendlyMessage {
    switch (this) {
      case RewardErrorType.networkError:
        return 'Network connection issue. Please check your internet and try again.';
      case RewardErrorType.authenticationError:
        return 'Authentication failed. Please log in again.';
      case RewardErrorType.validationError:
        return 'Invalid request. Please try again.';
      case RewardErrorType.serverError:
        return 'Server error. Please try again later.';
      case RewardErrorType.rateLimitError:
        return 'Too many requests. Please wait a moment and try again.';
      case RewardErrorType.rewardNotFound:
        return 'Reward not found. It may have been removed or expired.';
      case RewardErrorType.rewardExpired:
        return 'This reward has expired and is no longer available.';
      case RewardErrorType.rewardAlreadyClaimed:
        return 'You have already claimed this reward.';
      case RewardErrorType.insufficientProgress:
        return 'You haven\'t met the requirements for this reward yet.';
      case RewardErrorType.dailyRewardAlreadyClaimed:
        return 'You have already claimed your daily reward today. Come back tomorrow!';
      case RewardErrorType.streakBroken:
        return 'Your login streak was broken. Start a new streak today!';
      case RewardErrorType.loyaltyTierNotFound:
        return 'Loyalty tier information not found.';
      case RewardErrorType.achievementNotFound:
        return 'Achievement not found.';
      case RewardErrorType.unknownError:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  String get title {
    switch (this) {
      case RewardErrorType.networkError:
        return 'Connection Error';
      case RewardErrorType.authenticationError:
        return 'Authentication Required';
      case RewardErrorType.validationError:
        return 'Invalid Request';
      case RewardErrorType.serverError:
        return 'Server Error';
      case RewardErrorType.rateLimitError:
        return 'Rate Limit Exceeded';
      case RewardErrorType.rewardNotFound:
        return 'Reward Not Found';
      case RewardErrorType.rewardExpired:
        return 'Reward Expired';
      case RewardErrorType.rewardAlreadyClaimed:
        return 'Already Claimed';
      case RewardErrorType.insufficientProgress:
        return 'Requirements Not Met';
      case RewardErrorType.dailyRewardAlreadyClaimed:
        return 'Daily Reward Claimed';
      case RewardErrorType.streakBroken:
        return 'Streak Broken';
      case RewardErrorType.loyaltyTierNotFound:
        return 'Tier Not Found';
      case RewardErrorType.achievementNotFound:
        return 'Achievement Not Found';
      case RewardErrorType.unknownError:
        return 'Unknown Error';
    }
  }

  bool get isRetryable {
    switch (this) {
      case RewardErrorType.networkError:
      case RewardErrorType.serverError:
      case RewardErrorType.rateLimitError:
      case RewardErrorType.unknownError:
        return true;
      default:
        return false;
    }
  }

  bool get requiresReauth {
    return this == RewardErrorType.authenticationError;
  }
}

/// Reward error class with detailed information
class RewardError {
  final RewardErrorType type;
  final String message;
  final String? details;
  final int? statusCode;
  final DateTime timestamp;

  RewardError({
    required this.type,
    required this.message,
    this.details,
    this.statusCode,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Create error from HTTP response
  factory RewardError.fromHttpResponse(int statusCode, String? responseBody) {
    RewardErrorType type;
    String message;

    switch (statusCode) {
      case 400:
        type = RewardErrorType.validationError;
        message = 'Invalid request';
        break;
      case 401:
        type = RewardErrorType.authenticationError;
        message = 'Authentication required';
        break;
      case 404:
        type = RewardErrorType.rewardNotFound;
        message = 'Resource not found';
        break;
      case 409:
        type = RewardErrorType.rewardAlreadyClaimed;
        message = 'Resource conflict';
        break;
      case 429:
        type = RewardErrorType.rateLimitError;
        message = 'Rate limit exceeded';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        type = RewardErrorType.serverError;
        message = 'Server error';
        break;
      default:
        type = RewardErrorType.unknownError;
        message = 'Unknown error';
    }

    return RewardError(
      type: type,
      message: message,
      details: responseBody,
      statusCode: statusCode,
    );
  }

  /// Create error from exception with enhanced detection
  factory RewardError.fromException(dynamic exception) {
    final exceptionString = exception.toString().toLowerCase();

    // Network-related exceptions
    if (exceptionString.contains('socketexception') ||
        exceptionString.contains('timeoutexception') ||
        exceptionString.contains('handshakeexception') ||
        exceptionString.contains('connection refused') ||
        exceptionString.contains('network is unreachable')) {
      return RewardError(
        type: RewardErrorType.networkError,
        message: 'Network connection error',
        details: 'Network exception: ${exception.toString()}',
      );
    }

    // JSON/Format exceptions
    if (exceptionString.contains('formatexception') ||
        exceptionString.contains('json') ||
        exceptionString.contains('unexpected character')) {
      return RewardError(
        type: RewardErrorType.serverError,
        message: 'Server response format error',
        details: 'Parsing exception: ${exception.toString()}',
      );
    }

    // HTTP-related exceptions
    if (exceptionString.contains('http') ||
        exceptionString.contains('response')) {
      return RewardError(
        type: RewardErrorType.serverError,
        message: 'Server communication error',
        details: 'HTTP exception: ${exception.toString()}',
      );
    }

    return RewardError(
      type: RewardErrorType.unknownError,
      message: 'Unexpected error occurred',
      details: 'Unknown exception: ${exception.toString()}',
    );
  }

  /// Create error from API response message
  factory RewardError.fromApiMessage(String apiMessage) {
    RewardErrorType type;
    
    final lowerMessage = apiMessage.toLowerCase();
    
    if (lowerMessage.contains('already claimed') || lowerMessage.contains('duplicate')) {
      type = RewardErrorType.rewardAlreadyClaimed;
    } else if (lowerMessage.contains('expired') || lowerMessage.contains('no longer available')) {
      type = RewardErrorType.rewardExpired;
    } else if (lowerMessage.contains('not found') || lowerMessage.contains('does not exist')) {
      type = RewardErrorType.rewardNotFound;
    } else if (lowerMessage.contains('insufficient') || lowerMessage.contains('requirements not met')) {
      type = RewardErrorType.insufficientProgress;
    } else if (lowerMessage.contains('daily reward') && lowerMessage.contains('claimed')) {
      type = RewardErrorType.dailyRewardAlreadyClaimed;
    } else if (lowerMessage.contains('streak') && lowerMessage.contains('broken')) {
      type = RewardErrorType.streakBroken;
    } else if (lowerMessage.contains('unauthorized') || lowerMessage.contains('authentication')) {
      type = RewardErrorType.authenticationError;
    } else if (lowerMessage.contains('rate limit') || lowerMessage.contains('too many requests')) {
      type = RewardErrorType.rateLimitError;
    } else {
      type = RewardErrorType.unknownError;
    }

    return RewardError(
      type: type,
      message: apiMessage,
    );
  }

  @override
  String toString() {
    return 'RewardError(type: $type, message: $message, statusCode: $statusCode)';
  }
}

/// Reward error handler utility class
class RewardErrorHandler {
  /// Handle and log reward errors
  static RewardError handleError(dynamic error, {String? context}) {
    RewardError rewardError;

    if (error is RewardError) {
      rewardError = error;
    } else if (error is Map<String, dynamic>) {
      // Handle API error response
      final message = error['message'] ?? error['error'] ?? 'Unknown error';
      final statusCode = error['statusCode'] as int?;
      
      if (statusCode != null) {
        rewardError = RewardError.fromHttpResponse(statusCode, message);
      } else {
        rewardError = RewardError.fromApiMessage(message);
      }
    } else if (error is String) {
      rewardError = RewardError.fromApiMessage(error);
    } else {
      rewardError = RewardError.fromException(error);
    }

    // Log error for debugging
    debugPrint('RewardError${context != null ? ' ($context)' : ''}: $rewardError');
    
    return rewardError;
  }

  /// Get user-friendly error message
  static String getUserFriendlyMessage(dynamic error) {
    final rewardError = handleError(error);
    return rewardError.type.userFriendlyMessage;
  }

  /// Check if error is retryable
  static bool isRetryable(dynamic error) {
    final rewardError = handleError(error);
    return rewardError.type.isRetryable;
  }

  /// Check if error requires re-authentication
  static bool requiresReauth(dynamic error) {
    final rewardError = handleError(error);
    return rewardError.type.requiresReauth;
  }

  /// Get suggested retry delay based on error type
  static Duration getRetryDelay(RewardErrorType errorType) {
    switch (errorType) {
      case RewardErrorType.networkError:
        return const Duration(seconds: 2);
      case RewardErrorType.serverError:
        return const Duration(seconds: 5);
      case RewardErrorType.rateLimitError:
        return const Duration(seconds: 30);
      default:
        return const Duration(seconds: 1);
    }
  }

  /// Create error with HTTP response context
  static RewardError createHttpError({
    required int statusCode,
    required String responseBody,
    String? endpoint,
    String? requestDetails,
  }) {
    final errorType = _getErrorTypeFromStatusCode(statusCode);
    final message = _extractMessageFromResponse(responseBody) ??
                   'HTTP $statusCode error';

    final details = [
      'Endpoint: ${endpoint ?? 'unknown'}',
      'Status Code: $statusCode',
      'Response: $responseBody',
      if (requestDetails != null) 'Request: $requestDetails',
    ].join('\n');

    return RewardError(
      type: errorType,
      message: message,
      details: details,
      statusCode: statusCode,
    );
  }

  /// Create error with network context
  static RewardError createNetworkError({
    required dynamic exception,
    String? endpoint,
    String? requestDetails,
  }) {
    final details = [
      'Endpoint: ${endpoint ?? 'unknown'}',
      'Exception: ${exception.toString()}',
      if (requestDetails != null) 'Request: $requestDetails',
    ].join('\n');

    return RewardError(
      type: RewardErrorType.networkError,
      message: 'Network connection failed',
      details: details,
    );
  }

  /// Helper to determine error type from status code
  static RewardErrorType _getErrorTypeFromStatusCode(int statusCode) {
    switch (statusCode) {
      case 400:
        return RewardErrorType.validationError;
      case 401:
      case 403:
        return RewardErrorType.authenticationError;
      case 404:
        return RewardErrorType.rewardNotFound;
      case 429:
        return RewardErrorType.rateLimitError;
      case 500:
      case 502:
      case 503:
      case 504:
        return RewardErrorType.serverError;
      default:
        return RewardErrorType.unknownError;
    }
  }

  /// Helper to extract error message from response body
  static String? _extractMessageFromResponse(String responseBody) {
    try {
      final data = json.decode(responseBody) as Map<String, dynamic>;
      return data['message'] as String? ?? data['error'] as String?;
    } catch (e) {
      return null;
    }
  }
}

/// Edge case validator for reward operations
class RewardValidator {
  /// Validate daily reward claim
  static String? validateDailyRewardClaim({
    required bool hasClaimedToday,
    required bool isStreakActive,
    required DateTime? lastLoginDate,
  }) {
    if (hasClaimedToday) {
      return 'You have already claimed your daily reward today. Come back tomorrow!';
    }

    if (lastLoginDate != null) {
      final daysSinceLastLogin = DateTime.now().difference(lastLoginDate).inDays;
      if (daysSinceLastLogin > 1) {
        return 'Your login streak was broken. You can still claim today\'s reward to start a new streak!';
      }
    }

    return null; // Valid to claim
  }

  /// Validate achievement progress update
  static String? validateAchievementProgress({
    required bool isCompleted,
    required int currentProgress,
    required int targetProgress,
    required int progressIncrement,
  }) {
    if (isCompleted) {
      return 'This achievement is already completed.';
    }

    if (progressIncrement <= 0) {
      return 'Progress increment must be positive.';
    }

    if (currentProgress >= targetProgress) {
      return 'Achievement progress is already at maximum.';
    }

    return null; // Valid to update
  }

  /// Validate reward claim
  static String? validateRewardClaim({
    required bool isActive,
    required DateTime? startDate,
    required DateTime? endDate,
    required bool alreadyClaimed,
  }) {
    if (!isActive) {
      return 'This reward is not currently active.';
    }

    if (alreadyClaimed) {
      return 'You have already claimed this reward.';
    }

    final now = DateTime.now();
    
    if (startDate != null && now.isBefore(startDate)) {
      return 'This reward is not yet available.';
    }

    if (endDate != null && now.isAfter(endDate)) {
      return 'This reward has expired.';
    }

    return null; // Valid to claim
  }

  /// Validate loyalty tier upgrade
  static String? validateLoyaltyTierUpgrade({
    required int currentPoints,
    required int requiredPoints,
  }) {
    if (currentPoints < requiredPoints) {
      final pointsNeeded = requiredPoints - currentPoints;
      return 'You need $pointsNeeded more points to reach this tier.';
    }

    return null; // Valid for upgrade
  }
}
