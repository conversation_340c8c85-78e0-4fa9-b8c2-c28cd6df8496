#!/usr/bin/env node

const axios = require('axios');

const API_BASE_URL = 'http://127.0.0.1:5000/api/v1';

async function testAdminLogin() {
  console.log('🔐 Testing admin login...');
  
  try {
    // Try different admin credentials
    const adminCredentials = [
      { email: '<EMAIL>', password: 'AdminPassword123!' },
      { email: '<EMAIL>', password: 'AdminPassword123!' },
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: 'AdminPassword123!' }
    ];

    for (const creds of adminCredentials) {
      try {
        console.log(`\n🔑 Trying login with ${creds.email}...`);
        
        const response = await axios.post(`${API_BASE_URL}/auth/login`, creds, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });

        if (response.status === 200 && response.data.access_token) {
          console.log('✅ Login successful!');
          console.log(`📋 Token: ${response.data.access_token.substring(0, 50)}...`);
          
          // Test admin API endpoints
          await testAdminEndpoints(response.data.access_token);
          return response.data.access_token;
        }
      } catch (error) {
        console.log(`❌ Login failed for ${creds.email}: ${error.response?.data?.error || error.message}`);
      }
    }
    
    console.log('\n❌ All admin login attempts failed');
    return null;
    
  } catch (error) {
    console.error('❌ Error during admin login test:', error.message);
    return null;
  }
}

async function testAdminEndpoints(token) {
  console.log('\n🧪 Testing admin API endpoints...');
  
  const endpoints = [
    { method: 'GET', url: '/rewards/admin/daily-rewards', name: 'Daily Rewards' },
    { method: 'GET', url: '/rewards/admin/loyalty-tiers', name: 'Loyalty Tiers' },
    { method: 'GET', url: '/rewards/admin/analytics', name: 'Rewards Analytics' }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n📡 Testing ${endpoint.name} (${endpoint.method} ${endpoint.url})...`);
      
      const response = await axios({
        method: endpoint.method,
        url: `${API_BASE_URL}${endpoint.url}`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      console.log(`✅ ${endpoint.name}: Status ${response.status}`);
      console.log(`📊 Data: ${JSON.stringify(response.data, null, 2).substring(0, 200)}...`);
      
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ${error.response?.status || 'Network Error'} - ${error.response?.data?.error || error.message}`);
    }
  }
}

// Run the test
if (require.main === module) {
  testAdminLogin().then(token => {
    if (token) {
      console.log('\n🎉 Admin API testing completed successfully!');
      process.exit(0);
    } else {
      console.log('\n💥 Admin API testing failed - could not authenticate');
      process.exit(1);
    }
  }).catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { testAdminLogin, testAdminEndpoints };
