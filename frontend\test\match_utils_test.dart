import 'package:flutter_test/flutter_test.dart';
import 'package:wiggyz_app/utils/match_utils.dart';

void main() {
  group('MatchUtils Tests', () {
    group('Prize Pool Calculation', () {
      test('should calculate correct prize pool with platform fee', () {
        // Entry fee = 10, Participants = 4
        // Total collected = 10 × 4 = 40
        // Platform fee = 40 × 0.10 = 4
        // Prize pool = 40 - 4 = 36
        final prizePool = MatchUtils.calculatePrizePool(10.0, 4);
        expect(prizePool, equals(36.0));
      });

      test('should return 0 for zero entry fee', () {
        final prizePool = MatchUtils.calculatePrizePool(0.0, 4);
        expect(prizePool, equals(0.0));
      });

      test('should return 0 for zero participants', () {
        final prizePool = MatchUtils.calculatePrizePool(10.0, 0);
        expect(prizePool, equals(0.0));
      });

      test('should handle single participant', () {
        // Entry fee = 100, Participants = 1
        // Total collected = 100 × 1 = 100
        // Platform fee = 100 × 0.10 = 10
        // Prize pool = 100 - 10 = 90
        final prizePool = MatchUtils.calculatePrizePool(100.0, 1);
        expect(prizePool, equals(90.0));
      });
    });

    group('Max Prize Pool Calculation', () {
      test('should calculate correct max prize pool with platform fee', () {
        // Entry fee = 10, Max participants = 10
        // Total collected = 10 × 10 = 100
        // Platform fee = 100 × 0.10 = 10
        // Max prize pool = 100 - 10 = 90
        final maxPrizePool = MatchUtils.calculateMaxPrizePool(10.0, 10);
        expect(maxPrizePool, equals(90.0));
      });

      test('should return 0 for zero entry fee', () {
        final maxPrizePool = MatchUtils.calculateMaxPrizePool(0.0, 10);
        expect(maxPrizePool, equals(0.0));
      });

      test('should return 0 for zero max participants', () {
        final maxPrizePool = MatchUtils.calculateMaxPrizePool(10.0, 0);
        expect(maxPrizePool, equals(0.0));
      });

      test('should handle large max participant count', () {
        // Entry fee = 5, Max participants = 100
        // Total collected = 5 × 100 = 500
        // Platform fee = 500 × 0.10 = 50
        // Max prize pool = 500 - 50 = 450
        final maxPrizePool = MatchUtils.calculateMaxPrizePool(5.0, 100);
        expect(maxPrizePool, equals(450.0));
      });
    });

    group('Prize Pool Formatting', () {
      test('should format prize pool with currency symbol', () {
        final formatted = MatchUtils.formatPrizePool(10.0, 4);
        expect(formatted, equals('₹36'));
      });

      test('should return "Free" for zero entry fee', () {
        final formatted = MatchUtils.formatPrizePool(0.0, 4);
        expect(formatted, equals('Free'));
      });

      test('should return "TBD" for zero participants', () {
        final formatted = MatchUtils.formatPrizePool(10.0, 0);
        expect(formatted, equals('TBD'));
      });

      test('should use custom currency symbol', () {
        final formatted = MatchUtils.formatPrizePool(10.0, 4, currency: '\$');
        expect(formatted, equals('\$36'));
      });
    });

    group('Current and Max Prize Pool Formatting', () {
      test('should format both current and max prize pools', () {
        // Entry fee = 10, Current = 4, Max = 10
        // Current: 10 × 4 = 40 - 4 = 36
        // Max: 10 × 10 = 100 - 10 = 90
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 4, 10);
        expect(formatted, equals('Current: ₹36 | Max: ₹90'));
      });

      test('should return "Free" for zero entry fee', () {
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(0.0, 4, 10);
        expect(formatted, equals('Free'));
      });

      test('should show single value when current equals max participants', () {
        // When current participants = max participants, show only one value
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 10, 10);
        expect(formatted, equals('₹90'));
      });

      test('should show single value when current exceeds max participants', () {
        // Edge case: current > max (shouldn't happen but handle gracefully)
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 12, 10);
        expect(formatted, equals('₹108'));
      });

      test('should use custom currency symbol', () {
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 4, 10, currency: '\$');
        expect(formatted, equals('Current: \$36 | Max: \$90'));
      });

      test('should handle zero current participants', () {
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 0, 10);
        expect(formatted, equals('Current: TBD | Max: ₹90'));
      });

      test('should handle TBD cases gracefully', () {
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 0, 0);
        expect(formatted, equals('Current: TBD | Max: ₹90')); // Uses default max of 10
      });

      test('should fix the zero maxParticipants bug', () {
        // This was the main issue - when maxParticipants is 0, should still show both values
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 4, 0);
        expect(formatted, equals('Current: ₹36 | Max: ₹90')); // Uses default max of 10
      });

      test('should only show single value when match is actually full', () {
        // Only show single value when current equals valid max (> 0)
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 8, 8);
        expect(formatted, equals('₹72'));
      });

      test('should not show single value when maxParticipants is invalid', () {
        // Even if current >= max, if max is 0 or invalid, show both values
        final formatted = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 5, 0);
        expect(formatted, equals('Current: ₹45 | Max: ₹90')); // Uses default max of 10
      });
    });

    group('Match Title Generation', () {
      test('should use provided title when available', () {
        final title = MatchUtils.generateMatchTitle(
          'Epic Battle Royale',
          'Free Fire',
          'match123',
        );
        expect(title, equals('Epic Battle Royale'));
      });

      test('should use game name when title is null', () {
        final title = MatchUtils.generateMatchTitle(
          null,
          'Free Fire',
          'match123',
        );
        expect(title, equals('Free Fire Match'));
      });

      test('should use match ID when both title and game name are null', () {
        final title = MatchUtils.generateMatchTitle(
          null,
          null,
          'match123456789',
        );
        expect(title, equals('Match #match123'));
      });

      test('should handle empty title string', () {
        final title = MatchUtils.generateMatchTitle(
          '',
          'PUBG',
          'match123',
        );
        expect(title, equals('PUBG Match'));
      });
    });

    group('Time Formatting', () {
      test('should format today\'s time correctly', () {
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day, 14, 30); // 2:30 PM today
        final utcTime = today.toUtc();
        
        final formatted = MatchUtils.formatLocalDateTime(utcTime);
        expect(formatted, contains('Today'));
        expect(formatted, contains('2:30 PM'));
      });

      test('should format tomorrow\'s time correctly', () {
        final now = DateTime.now();
        final tomorrow = DateTime(now.year, now.month, now.day + 1, 14, 30);
        final utcTime = tomorrow.toUtc();
        
        final formatted = MatchUtils.formatLocalDateTime(utcTime);
        expect(formatted, contains('Tomorrow'));
        expect(formatted, contains('2:30 PM'));
      });

      test('should return "TBD" for null time', () {
        final formatted = MatchUtils.formatLocalDateTime(null);
        expect(formatted, equals('TBD'));
      });
    });

    group('Relative Time Formatting', () {
      test('should format future minutes correctly', () {
        final future = DateTime.now().add(const Duration(minutes: 30));
        final utcTime = future.toUtc();
        
        final formatted = MatchUtils.formatRelativeTime(utcTime);
        expect(formatted, equals('In 30m'));
      });

      test('should format future hours correctly', () {
        final future = DateTime.now().add(const Duration(hours: 2));
        final utcTime = future.toUtc();
        
        final formatted = MatchUtils.formatRelativeTime(utcTime);
        expect(formatted, equals('In 2h'));
      });

      test('should format past time correctly', () {
        final past = DateTime.now().subtract(const Duration(minutes: 15));
        final utcTime = past.toUtc();
        
        final formatted = MatchUtils.formatRelativeTime(utcTime);
        expect(formatted, equals('15m ago'));
      });
    });

    group('Participant Count', () {
      test('should count only players', () {
        final participants = [
          {'participant_type': 'player', 'user_id': 'user1'},
          {'participant_type': 'player', 'user_id': 'user2'},
          {'participant_type': 'spectator', 'user_id': 'user3'},
        ];
        
        final count = MatchUtils.getParticipantCount(participants);
        expect(count, equals(2));
      });

      test('should return 0 for null participants', () {
        final count = MatchUtils.getParticipantCount(null);
        expect(count, equals(0));
      });

      test('should return 0 for empty participants list', () {
        final count = MatchUtils.getParticipantCount([]);
        expect(count, equals(0));
      });
    });

    group('Currency Formatting', () {
      test('should format currency with default symbol', () {
        final formatted = MatchUtils.formatCurrency(100.0);
        expect(formatted, equals('₹100'));
      });

      test('should return "Free" for zero amount', () {
        final formatted = MatchUtils.formatCurrency(0.0);
        expect(formatted, equals('Free'));
      });

      test('should use custom currency symbol', () {
        final formatted = MatchUtils.formatCurrency(50.0, currency: '\$');
        expect(formatted, equals('\$50'));
      });
    });

    group('Time Remaining Badge', () {
      test('should show minutes for near future', () {
        final future = DateTime.now().add(const Duration(minutes: 45));
        final utcTime = future.toUtc();
        
        final badge = MatchUtils.getTimeRemainingBadge(utcTime);
        expect(badge, equals('45m'));
      });

      test('should show "Started" for past time', () {
        final past = DateTime.now().subtract(const Duration(minutes: 10));
        final utcTime = past.toUtc();
        
        final badge = MatchUtils.getTimeRemainingBadge(utcTime);
        expect(badge, equals('Started'));
      });

      test('should show "Starting soon" for very near future', () {
        final future = DateTime.now().add(const Duration(seconds: 30));
        final utcTime = future.toUtc();
        
        final badge = MatchUtils.getTimeRemainingBadge(utcTime);
        expect(badge, equals('Starting soon'));
      });

      test('should return "TBD" for null time', () {
        final badge = MatchUtils.getTimeRemainingBadge(null);
        expect(badge, equals('TBD'));
      });
    });

    group('Deadline Formatting', () {
      test('should show time left for future deadline', () {
        final future = DateTime.now().add(const Duration(hours: 2));
        final utcTime = future.toUtc();
        
        final formatted = MatchUtils.formatDeadlineTime(utcTime);
        expect(formatted, equals('2h left'));
      });

      test('should show "Expired" for past deadline', () {
        final past = DateTime.now().subtract(const Duration(hours: 1));
        final utcTime = past.toUtc();
        
        final formatted = MatchUtils.formatDeadlineTime(utcTime);
        expect(formatted, equals('Expired'));
      });

      test('should return "No deadline" for null', () {
        final formatted = MatchUtils.formatDeadlineTime(null);
        expect(formatted, equals('No deadline'));
      });
    });
  });
}
