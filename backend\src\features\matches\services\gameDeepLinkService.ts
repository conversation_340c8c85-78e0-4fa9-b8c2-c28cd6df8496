/**
 * Game Deep Link Service - Provides secure deep linking to game applications
 * Only allows confirmed match participants to access game links
 */

import { logger } from '../../../utils/logger';
import { supabase } from '../../../config/supabase';

interface GameLinkConfig {
  name: string;
  packageName: string;
  deepLinkScheme: string;
  playStoreUrl: string;
  appStoreUrl: string;
  customRoomSupport: boolean;
}

interface DeepLinkResponse {
  success: boolean;
  gameLink?: string;
  fallbackUrl?: string;
  error?: string;
  requiresParticipation?: boolean;
}

export class GameDeepLinkService {
  private gameConfigs: Map<number, GameLinkConfig> = new Map();

  constructor() {
    this.initializeGameConfigs();
  }

  /**
   * Initialize game configurations for deep linking
   */
  private initializeGameConfigs(): void {
    // Free Fire configuration
    this.gameConfigs.set(1, {
      name: 'Free Fire',
      packageName: 'com.dts.freefireth',
      deepLinkScheme: 'freefire://',
      playStoreUrl: 'https://play.google.com/store/apps/details?id=com.dts.freefireth',
      appStoreUrl: 'https://apps.apple.com/app/garena-free-fire/id1300146617',
      customRoomSupport: true
    });

    // PUBG Mobile configuration
    this.gameConfigs.set(2, {
      name: 'PUBG Mobile',
      packageName: 'com.tencent.ig',
      deepLinkScheme: 'pubgmobile://',
      playStoreUrl: 'https://play.google.com/store/apps/details?id=com.tencent.ig',
      appStoreUrl: 'https://apps.apple.com/app/pubg-mobile/id1330123889',
      customRoomSupport: true
    });
  }

  /**
   * Generate game deep link for confirmed participants only
   * SECURITY: Verifies user participation before providing game links
   */
  async generateGameLink(
    matchId: string, 
    userId: string, 
    platform: 'android' | 'ios' = 'android'
  ): Promise<DeepLinkResponse> {
    try {
      // Verify user is a confirmed participant
      const isParticipant = await this.verifyParticipation(matchId, userId);
      if (!isParticipant) {
        logger.security(`Non-participant ${userId} attempted to access game link for match ${matchId}`);
        return {
          success: false,
          error: 'Access denied: Only confirmed participants can access game links',
          requiresParticipation: true
        };
      }

      // Get match details
      const { data: match, error: matchError } = await supabase
        .from('matches')
        .select(`
          *,
          games (id, name)
        `)
        .eq('id', matchId)
        .single();

      if (matchError || !match) {
        return {
          success: false,
          error: 'Match not found'
        };
      }

      const gameConfig = this.gameConfigs.get(match.game_id);
      if (!gameConfig) {
        return {
          success: false,
          error: 'Game not supported for deep linking'
        };
      }

      // Generate deep link based on game and platform
      const deepLink = this.buildDeepLink(gameConfig, match, platform);
      const fallbackUrl = platform === 'ios' ? gameConfig.appStoreUrl : gameConfig.playStoreUrl;

      logger.info(`Generated game link for participant ${userId} in match ${matchId}`);
      
      return {
        success: true,
        gameLink: deepLink,
        fallbackUrl: fallbackUrl
      };
    } catch (error) {
      logger.error(`Error generating game link for match ${matchId}, user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      return {
        success: false,
        error: 'Failed to generate game link'
      };
    }
  }

  /**
   * Verify user is a confirmed participant in the match
   */
  private async verifyParticipation(matchId: string, userId: string): Promise<boolean> {
    try {
      const { data: participant, error } = await supabase
        .from('match_participants')
        .select('user_id, participant_type')
        .eq('match_id', matchId)
        .eq('user_id', userId)
        .single();

      return !error && !!participant;
    } catch (error) {
      logger.error(`Error verifying participation for user ${userId} in match ${matchId}: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Build deep link URL based on game configuration and match details
   */
  private buildDeepLink(
    gameConfig: GameLinkConfig, 
    match: any, 
    platform: 'android' | 'ios'
  ): string {
    if (!gameConfig.customRoomSupport) {
      // Simple app launch for games without custom room support
      return gameConfig.deepLinkScheme;
    }

    // Build custom room deep link
    const baseLink = gameConfig.deepLinkScheme;
    const roomId = match.room_id;
    const roomPassword = match.room_password;

    if (!roomId) {
      return baseLink;
    }

    // Game-specific deep link formats
    switch (gameConfig.name) {
      case 'Free Fire':
        return `${baseLink}room?id=${encodeURIComponent(roomId)}&password=${encodeURIComponent(roomPassword || '')}`;
      
      case 'PUBG Mobile':
        return `${baseLink}room/${encodeURIComponent(roomId)}?password=${encodeURIComponent(roomPassword || '')}`;
      
      default:
        return `${baseLink}?room=${encodeURIComponent(roomId)}&pass=${encodeURIComponent(roomPassword || '')}`;
    }
  }

  /**
   * Get game instructions for manual setup
   */
  async getGameInstructions(matchId: string, userId: string): Promise<{
    success: boolean;
    instructions?: string[];
    error?: string;
  }> {
    try {
      // Verify participation
      const isParticipant = await this.verifyParticipation(matchId, userId);
      if (!isParticipant) {
        return {
          success: false,
          error: 'Access denied: Only confirmed participants can access game instructions'
        };
      }

      // Get match and game details
      const { data: match, error } = await supabase
        .from('matches')
        .select(`
          *,
          games (id, name)
        `)
        .eq('id', matchId)
        .single();

      if (error || !match) {
        return {
          success: false,
          error: 'Match not found'
        };
      }

      const gameConfig = this.gameConfigs.get(match.game_id);
      if (!gameConfig) {
        return {
          success: false,
          error: 'Game instructions not available'
        };
      }

      const instructions = this.generateInstructions(gameConfig, match);
      
      return {
        success: true,
        instructions: instructions
      };
    } catch (error) {
      logger.error(`Error getting game instructions for match ${matchId}, user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      return {
        success: false,
        error: 'Failed to get game instructions'
      };
    }
  }

  /**
   * Generate step-by-step instructions for manual game setup
   */
  private generateInstructions(gameConfig: GameLinkConfig, match: any): string[] {
    const baseInstructions = [
      `Open ${gameConfig.name} app`,
      'Navigate to Custom Room section',
    ];

    if (match.room_id) {
      baseInstructions.push(`Enter Room ID: ${match.room_id}`);
    }

    if (match.room_password) {
      baseInstructions.push(`Enter Room Password: ${match.room_password}`);
    }

    baseInstructions.push(
      'Wait for all players to join',
      'Match will start according to schedule'
    );

    return baseInstructions;
  }

  /**
   * Get supported games list
   */
  getSupportedGames(): Array<{id: number, name: string, supported: boolean}> {
    const games: Array<{id: number, name: string, supported: boolean}> = [];
    
    this.gameConfigs.forEach((config, gameId) => {
      games.push({
        id: gameId,
        name: config.name,
        supported: true
      });
    });

    return games;
  }
}

export const gameDeepLinkService = new GameDeepLinkService();
