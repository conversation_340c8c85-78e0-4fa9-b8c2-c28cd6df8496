/**
 * Validation script for the enhanced referral system
 * Checks database schema, functions, and system integrity
 */

import { supabase } from '../config/supabase';
import { logger } from '../utils/logger';

interface ValidationResult {
  component: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

class ReferralSystemValidator {
  private results: ValidationResult[] = [];

  private addResult(component: string, status: 'pass' | 'fail' | 'warning', message: string, details?: any) {
    this.results.push({ component, status, message, details });
    const emoji = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    logger.info(`${emoji} ${component}: ${message}`);
  }

  async validateDatabaseSchema(): Promise<void> {
    logger.info('🔍 Validating database schema...');

    const requiredTables = [
      'referral_points',
      'referral_point_transactions',
      'referral_fraud_detection',
      'device_fingerprints',
      'referral_ip_tracking',
      'referral_settings'
    ];

    for (const table of requiredTables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          this.addResult('Database Schema', 'fail', `Table ${table} not accessible: ${error.message}`);
        } else {
          this.addResult('Database Schema', 'pass', `Table ${table} exists and accessible`);
        }
      } catch (error) {
        this.addResult('Database Schema', 'fail', `Error checking table ${table}: ${error}`);
      }
    }
  }

  async validateDatabaseFunctions(): Promise<void> {
    logger.info('🔍 Validating database functions...');

    const requiredFunctions = [
      'award_referral_points',
      'use_referral_points',
      'get_referral_points_balance',
      'calculate_referral_fraud_score',
      'check_referral_eligibility',
      'admin_review_referral'
    ];

    for (const func of requiredFunctions) {
      try {
        // Test function existence by calling with dummy parameters
        const { error } = await supabase.rpc(func, {});
        
        if (error && !error.message.includes('required')) {
          this.addResult('Database Functions', 'fail', `Function ${func} not found: ${error.message}`);
        } else {
          this.addResult('Database Functions', 'pass', `Function ${func} exists`);
        }
      } catch (error) {
        this.addResult('Database Functions', 'warning', `Could not test function ${func}: ${error}`);
      }
    }
  }

  async validateReferralSettings(): Promise<void> {
    logger.info('🔍 Validating referral settings...');

    const requiredSettings = [
      'referral_reward_points',
      'verification_required',
      'minimum_activity_days',
      'cooldown_period_hours',
      'max_referrals_per_ip_daily',
      'max_referrals_per_device_daily',
      'fraud_threshold_score',
      'admin_review_threshold'
    ];

    try {
      const { data, error } = await supabase
        .from('referral_settings')
        .select('setting_key, setting_value');

      if (error) {
        this.addResult('Referral Settings', 'fail', `Could not fetch settings: ${error.message}`);
        return;
      }

      const existingSettings = data.map((s: any) => s.setting_key);
      
      for (const setting of requiredSettings) {
        if (existingSettings.includes(setting)) {
          this.addResult('Referral Settings', 'pass', `Setting ${setting} exists`);
        } else {
          this.addResult('Referral Settings', 'fail', `Missing required setting: ${setting}`);
        }
      }
    } catch (error) {
      this.addResult('Referral Settings', 'fail', `Error validating settings: ${error}`);
    }
  }

  async validateIndexes(): Promise<void> {
    logger.info('🔍 Validating database indexes...');

    const requiredIndexes = [
      'idx_referral_points_user_id',
      'idx_referral_point_transactions_user_id',
      'idx_referral_fraud_detection_referral_id',
      'idx_device_fingerprints_hash',
      'idx_referral_ip_tracking_ip',
      'idx_user_referrals_verification_status'
    ];

    try {
      const { data, error } = await supabase
        .rpc('exec_sql', {
          sql: `
            SELECT indexname 
            FROM pg_indexes 
            WHERE schemaname = 'public' 
            AND indexname IN (${requiredIndexes.map((idx: string) => `'${idx}'`).join(',')})
          `
        });

      if (error) {
        this.addResult('Database Indexes', 'warning', `Could not check indexes: ${error.message}`);
        return;
      }

      const existingIndexes = data.map((row: any) => row.indexname);
      
      for (const index of requiredIndexes) {
        if (existingIndexes.includes(index)) {
          this.addResult('Database Indexes', 'pass', `Index ${index} exists`);
        } else {
          this.addResult('Database Indexes', 'warning', `Index ${index} may be missing`);
        }
      }
    } catch (error) {
      this.addResult('Database Indexes', 'warning', `Could not validate indexes: ${error}`);
    }
  }

  async validateRLS(): Promise<void> {
    logger.info('🔍 Validating Row Level Security...');

    const tablesWithRLS = [
      'referral_points',
      'referral_point_transactions',
      'referral_fraud_detection',
      'referral_settings'
    ];

    try {
      const { data, error } = await supabase
        .rpc('exec_sql', {
          sql: `
            SELECT tablename, rowsecurity 
            FROM pg_tables 
            WHERE schemaname = 'public' 
            AND tablename IN (${tablesWithRLS.map((table: string) => `'${table}'`).join(',')})
          `
        });

      if (error) {
        this.addResult('Row Level Security', 'warning', `Could not check RLS: ${error.message}`);
        return;
      }

      for (const row of data) {
        if (row.rowsecurity) {
          this.addResult('Row Level Security', 'pass', `RLS enabled on ${row.tablename}`);
        } else {
          this.addResult('Row Level Security', 'warning', `RLS not enabled on ${row.tablename}`);
        }
      }
    } catch (error) {
      this.addResult('Row Level Security', 'warning', `Could not validate RLS: ${error}`);
    }
  }

  async validateServiceIntegration(): Promise<void> {
    logger.info('🔍 Validating service integration...');

    try {
      // Test if services can be imported
      const referralPointsService = await import('../features/rewards/services/referralPointsService');
      const referralFraudService = await import('../features/rewards/services/referralFraudService');
      const referralService = await import('../features/rewards/services/referralService');

      this.addResult('Service Integration', 'pass', 'All referral services can be imported');

      // Test basic service methods exist
      const requiredMethods = [
        { service: 'referralPointsService', methods: ['getUserBalance', 'awardPoints', 'usePointsForMatch'] },
        { service: 'referralFraudService', methods: ['detectReferralFraud', 'checkVerificationStatus'] },
        { service: 'referralService', methods: ['processReferralWithVerification', 'awardReferralPoints'] }
      ];

      for (const { service, methods } of requiredMethods) {
        for (const method of methods) {
          const serviceModule = service === 'referralPointsService' ? referralPointsService :
                               service === 'referralFraudService' ? referralFraudService :
                               referralService;
          
          if (typeof (serviceModule.default as any)[method] === 'function') {
            this.addResult('Service Integration', 'pass', `${service}.${method} exists`);
          } else {
            this.addResult('Service Integration', 'fail', `${service}.${method} missing`);
          }
        }
      }
    } catch (error) {
      this.addResult('Service Integration', 'fail', `Service integration error: ${error}`);
    }
  }

  async validateAPIEndpoints(): Promise<void> {
    logger.info('🔍 Validating API endpoints...');

    // This would require a running server to test properly
    // For now, we'll just check if the route files exist and are properly structured
    try {
      const routes = await import('../features/rewards/routes');
      this.addResult('API Endpoints', 'pass', 'Rewards routes module loads successfully');
      
      const matchRoutes = await import('../features/matches/routes');
      this.addResult('API Endpoints', 'pass', 'Match routes module loads successfully');
    } catch (error) {
      this.addResult('API Endpoints', 'fail', `Route modules error: ${error}`);
    }
  }

  async runValidation(): Promise<ValidationResult[]> {
    logger.info('🚀 Starting referral system validation...');

    await this.validateDatabaseSchema();
    await this.validateDatabaseFunctions();
    await this.validateReferralSettings();
    await this.validateIndexes();
    await this.validateRLS();
    await this.validateServiceIntegration();
    await this.validateAPIEndpoints();

    // Summary
    const passed = this.results.filter(r => r.status === 'pass').length;
    const failed = this.results.filter(r => r.status === 'fail').length;
    const warnings = this.results.filter(r => r.status === 'warning').length;

    logger.info(`\n📊 Validation Summary:`);
    logger.info(`✅ Passed: ${passed}`);
    logger.info(`❌ Failed: ${failed}`);
    logger.info(`⚠️  Warnings: ${warnings}`);
    logger.info(`📝 Total checks: ${this.results.length}`);

    if (failed > 0) {
      logger.error('❌ Validation failed! Please fix the issues above before using the referral system.');
    } else if (warnings > 0) {
      logger.warn('⚠️  Validation completed with warnings. System should work but may have performance or security issues.');
    } else {
      logger.info('✅ All validations passed! Referral system is ready for use.');
    }

    return this.results;
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new ReferralSystemValidator();
  validator.runValidation()
    .then(() => process.exit(0))
    .catch((error) => {
      logger.error(`Validation script failed: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    });
}

export default ReferralSystemValidator;
