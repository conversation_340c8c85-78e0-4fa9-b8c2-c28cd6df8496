import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:wiggyz_app/core/api/api_config.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:wiggyz_app/features/tournament_models.dart';

/// Enhanced service for managing user's match history
class MatchHistoryService {
  final AuthProvider _authProvider;
  final http.Client _client = http.Client();

  // Cache for match history data
  static final Map<String, CachedData<MatchHistoryResponse>> _cache = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // Cache for statistics
  static CachedData<MatchStatistics>? _statisticsCache;

  MatchHistoryService(this._authProvider);

  /// Get headers for authenticated requests
  Future<Map<String, String>> _getHeaders({bool requiresAuth = true}) async {
    final headers = <String, String>{
      'Content-Type': 'application/json',
    };

    if (requiresAuth) {
      final token = await _authProvider.token;
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      } else {
        throw Exception('Authentication token not available');
      }
    }

    return headers;
  }

  /// Get comprehensive match history for the current user
  /// Includes both created and participated matches with full details
  Future<MatchHistoryResponse> getMatchHistory({
    int limit = 20,
    int offset = 0,
    String? status,
    String? matchType,
    bool forceRefresh = false,
  }) async {
    try {
      if (!_authProvider.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      // Create cache key
      final cacheKey = 'match_history_${limit}_${offset}_${status ?? 'all'}_${matchType ?? 'all'}';

      // Check cache first (only for first page and if not forcing refresh)
      if (!forceRefresh && offset == 0) {
        final cachedData = _cache[cacheKey];
        if (cachedData != null && !cachedData.isExpired) {
          debugPrint('Returning cached match history data');
          return cachedData.data;
        }
      }

      final headers = await _getHeaders(requiresAuth: true);

      // Build query parameters
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (status != null && status != 'all') {
        queryParams['status'] = status;
      }

      if (matchType != null) {
        queryParams['match_type'] = matchType;
      }

      final uri = Uri.parse('${ApiConfig.baseUrl}/matches/user/joined-matches')
          .replace(queryParameters: queryParams);

      debugPrint('Fetching match history from: $uri');

      final response = await _client.get(uri, headers: headers);

      debugPrint('Match history response status: ${response.statusCode}');
      debugPrint('Match history response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);

        if (jsonData['success'] == true) {
          final List<dynamic> matchData = jsonData['data'] ?? [];
          final List<MatchHistoryItem> matches = matchData
              .map((item) => MatchHistoryItem.fromJson(item))
              .toList();

          final response = MatchHistoryResponse(
            matches: matches,
            hasMore: matches.length >= limit,
            total: jsonData['count'] ?? matches.length,
          );

          // Cache the response (only for first page)
          if (offset == 0) {
            _cache[cacheKey] = CachedData(response, DateTime.now().add(_cacheExpiry));
          }

          return response;
        } else {
          throw Exception(jsonData['error'] ?? 'Failed to fetch match history');
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['error'] ?? 'HTTP ${response.statusCode}: Failed to fetch match history');
      }
    } catch (e) {
      debugPrint('Error fetching match history: $e');
      rethrow;
    }
  }

  /// Get user's active matches (ongoing/pending)
  Future<List<MatchHistoryItem>> getActiveMatches() async {
    try {
      if (!_authProvider.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      final headers = await _getHeaders(requiresAuth: true);
      final uri = Uri.parse('${ApiConfig.baseUrl}/matches/user/active-matches');

      final response = await _client.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        
        if (jsonData['success'] == true) {
          final List<dynamic> matchData = jsonData['data'] ?? [];
          return matchData
              .map((item) => MatchHistoryItem.fromJson(item))
              .toList();
        } else {
          throw Exception(jsonData['error'] ?? 'Failed to fetch active matches');
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to fetch active matches');
      }
    } catch (e) {
      debugPrint('Error fetching active matches: $e');
      rethrow;
    }
  }

  /// Get match statistics for the user
  Future<MatchStatistics> getMatchStatistics({bool forceRefresh = false}) async {
    try {
      if (!_authProvider.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      // Check cache first
      if (!forceRefresh && _statisticsCache != null && !_statisticsCache!.isExpired) {
        debugPrint('Returning cached statistics data');
        return _statisticsCache!.data;
      }

      // For now, we'll calculate statistics from the match history
      // In the future, this could be a dedicated endpoint
      final historyResponse = await getMatchHistory(limit: 1000, forceRefresh: forceRefresh);
      final matches = historyResponse.matches;

      int totalMatches = matches.length;
      int wonMatches = matches.where((m) => m.isWinner).length;
      int lostMatches = matches.where((m) => m.isCompleted && !m.isWinner).length;
      int pendingMatches = matches.where((m) => !m.isCompleted).length;

      double winRate = totalMatches > 0 ? (wonMatches / totalMatches) * 100 : 0.0;

      final statistics = MatchStatistics(
        totalMatches: totalMatches,
        wonMatches: wonMatches,
        lostMatches: lostMatches,
        pendingMatches: pendingMatches,
        winRate: winRate,
      );

      // Cache the statistics
      _statisticsCache = CachedData(statistics, DateTime.now().add(_cacheExpiry));

      return statistics;
    } catch (e) {
      debugPrint('Error calculating match statistics: $e');
      rethrow;
    }
  }

  /// Clear all cached data
  static void clearCache() {
    _cache.clear();
    _statisticsCache = null;
    debugPrint('Match history cache cleared');
  }

  void dispose() {
    _client.close();
  }
}

/// Response model for match history API
class MatchHistoryResponse {
  final List<MatchHistoryItem> matches;
  final bool hasMore;
  final int total;

  MatchHistoryResponse({
    required this.matches,
    required this.hasMore,
    required this.total,
  });
}

/// Enhanced match history item with comprehensive details
class MatchHistoryItem {
  final String id;
  final String matchId;
  final String userId;
  final String participantType;
  final String? teamName;
  final int? teamPosition;
  final DateTime joinedAt;
  final int? resultScore;
  final int? resultPosition;
  final int? resultKills;
  final String? resultScreenshotUrl;
  final DateTime? resultSubmittedAt;
  final bool isWinner;
  final String userMatchStatus;

  // Match details
  final String? matchTitle;
  final String matchStatus;
  final String? gameFormat;
  final String? matchMode;
  final String matchType;
  final double? entryFee;
  final double? prizePool;
  final DateTime? matchTime;
  final DateTime? resultSubmissionDeadline;
  final DateTime createdAt;

  // Game details
  final int? gameId;
  final String? gameName;
  final String? gameImage;

  // Tournament details (if applicable)
  final String? tournamentId;
  final String? tournamentName;
  final String? tournamentStatus;

  MatchHistoryItem({
    required this.id,
    required this.matchId,
    required this.userId,
    required this.participantType,
    this.teamName,
    this.teamPosition,
    required this.joinedAt,
    this.resultScore,
    this.resultPosition,
    this.resultKills,
    this.resultScreenshotUrl,
    this.resultSubmittedAt,
    required this.isWinner,
    required this.userMatchStatus,
    this.matchTitle,
    required this.matchStatus,
    this.gameFormat,
    this.matchMode,
    required this.matchType,
    this.entryFee,
    this.prizePool,
    this.matchTime,
    this.resultSubmissionDeadline,
    required this.createdAt,
    this.gameId,
    this.gameName,
    this.gameImage,
    this.tournamentId,
    this.tournamentName,
    this.tournamentStatus,
  });

  /// Check if the match is completed
  bool get isCompleted => ['completed', 'cancelled'].contains(matchStatus.toLowerCase());

  /// Check if the match is active/ongoing
  bool get isActive => ['live', 'in_progress', 'active'].contains(matchStatus.toLowerCase());

  /// Check if the match is pending
  bool get isPending => ['scheduled', 'pending_verification'].contains(matchStatus.toLowerCase());

  /// Get display title for the match
  String get displayTitle {
    if (matchTitle != null && matchTitle!.isNotEmpty) {
      return matchTitle!;
    }
    if (tournamentName != null && tournamentName!.isNotEmpty) {
      return tournamentName!;
    }
    if (gameName != null && gameName!.isNotEmpty) {
      return '$gameName Match';
    }
    return 'Match';
  }

  /// Get formatted prize pool
  String get formattedPrizePool {
    if (prizePool == null || prizePool == 0) return 'Free';
    return '₹${prizePool!.toStringAsFixed(0)}';
  }

  /// Get formatted entry fee
  String get formattedEntryFee {
    if (entryFee == null || entryFee == 0) return 'Free';
    return '₹${entryFee!.toStringAsFixed(0)}';
  }

  /// Get user's result status
  String get resultStatus {
    if (!isCompleted) {
      if (isActive) return 'Playing';
      if (isPending) return 'Pending';
      return matchStatus;
    }

    if (isWinner) return 'Won';
    if (matchStatus.toLowerCase() == 'cancelled') return 'Cancelled';
    return 'Lost';
  }

  factory MatchHistoryItem.fromJson(Map<String, dynamic> json) {
    try {
      final matchData = json['matches'] as Map<String, dynamic>? ?? {};
      final gameData = matchData['games'] as Map<String, dynamic>? ?? {};
      final tournamentData = matchData['tournaments'] as Map<String, dynamic>? ?? {};

      return MatchHistoryItem(
        id: json['id']?.toString() ?? '',
        matchId: json['match_id']?.toString() ?? '',
        userId: json['user_id']?.toString() ?? '',
        participantType: json['participant_type']?.toString() ?? 'player',
        teamName: json['team_name']?.toString(),
        teamPosition: json['team_position'] as int?,
        joinedAt: DateTime.tryParse(json['joined_at']?.toString() ?? '') ?? DateTime.now(),
        resultScore: json['result_score'] as int?,
        resultPosition: json['result_position'] as int?,
        resultKills: json['result_kills'] as int?,
        resultScreenshotUrl: json['result_screenshot_url']?.toString(),
        resultSubmittedAt: json['result_submitted_at'] != null
            ? DateTime.tryParse(json['result_submitted_at'].toString())
            : null,
        isWinner: json['is_winner'] as bool? ?? false,
        userMatchStatus: json['user_match_status']?.toString() ?? 'joined',

        // Match details
        matchTitle: matchData['match_description']?.toString(),
        matchStatus: matchData['status']?.toString() ?? 'unknown',
        gameFormat: matchData['game_format']?.toString(),
        matchMode: matchData['match_mode']?.toString(),
        matchType: matchData['match_type']?.toString() ?? 'standalone',
        entryFee: matchData['entry_fee'] != null
            ? double.tryParse(matchData['entry_fee'].toString())
            : null,
        prizePool: matchData['prize_pool'] != null
            ? double.tryParse(matchData['prize_pool'].toString())
            : null,
        matchTime: matchData['match_time'] != null
            ? DateTime.tryParse(matchData['match_time'].toString())
            : null,
        resultSubmissionDeadline: matchData['result_submission_deadline'] != null
            ? DateTime.tryParse(matchData['result_submission_deadline'].toString())
            : null,
        createdAt: DateTime.tryParse(matchData['created_at']?.toString() ?? '') ?? DateTime.now(),

        // Game details
        gameId: gameData['id'] as int?,
        gameName: gameData['name']?.toString(),
        gameImage: gameData['image']?.toString(),

        // Tournament details
        tournamentId: tournamentData['id']?.toString(),
        tournamentName: tournamentData['name']?.toString(),
        tournamentStatus: tournamentData['status']?.toString(),
      );
    } catch (e) {
      debugPrint('Error parsing MatchHistoryItem: $e');
      // Return a safe fallback object
      return MatchHistoryItem(
        id: json['id']?.toString() ?? 'unknown',
        matchId: json['match_id']?.toString() ?? 'unknown',
        userId: json['user_id']?.toString() ?? 'unknown',
        participantType: 'player',
        joinedAt: DateTime.now(),
        isWinner: false,
        userMatchStatus: 'joined',
        matchStatus: 'unknown',
        matchType: 'standalone',
        createdAt: DateTime.now(),
      );
    }
  }
}

/// Statistics model for user's match performance
class MatchStatistics {
  final int totalMatches;
  final int wonMatches;
  final int lostMatches;
  final int pendingMatches;
  final double winRate;

  MatchStatistics({
    required this.totalMatches,
    required this.wonMatches,
    required this.lostMatches,
    required this.pendingMatches,
    required this.winRate,
  });
}

/// Cache data wrapper with expiry
class CachedData<T> {
  final T data;
  final DateTime expiryTime;

  CachedData(this.data, this.expiryTime);

  bool get isExpired => DateTime.now().isAfter(expiryTime);
}
