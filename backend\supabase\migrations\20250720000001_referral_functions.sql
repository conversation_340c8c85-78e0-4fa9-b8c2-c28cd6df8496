-- Referral System Functions and Procedures
-- Date: 2025-07-20
-- Description: Database functions for referral fraud detection and point management

-- ==================== REFERRAL POINTS MANAGEMENT FUNCTIONS ====================

-- Function to award referral points
CREATE OR REPLACE FUNCTION award_referral_points(
  p_user_id UUID,
  p_points INTEGER,
  p_source_type VARCHAR(30),
  p_source_id UUID DEFAULT NULL,
  p_description TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  v_current_points INTEGER;
BEGIN
  -- Get or create referral points record
  INSERT INTO referral_points (user_id, total_points, earned_from_referrals)
  VALUES (p_user_id, p_points, p_points)
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    total_points = referral_points.total_points + p_points,
    earned_from_referrals = referral_points.earned_from_referrals + p_points,
    updated_at = NOW();

  -- Record transaction
  INSERT INTO referral_point_transactions (
    user_id, transaction_type, points, source_type, source_id, description
  ) VALUES (
    p_user_id, 'earned', p_points, p_source_type, p_source_id, p_description
  );

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to use referral points
CREATE OR REPLACE FUNCTION use_referral_points(
  p_user_id UUID,
  p_points INTEGER,
  p_source_type VARCHAR(30),
  p_source_id UUID DEFAULT NULL,
  p_description TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  v_available_points INTEGER;
BEGIN
  -- Check available points
  SELECT available_points INTO v_available_points
  FROM referral_points
  WHERE user_id = p_user_id;

  IF v_available_points IS NULL OR v_available_points < p_points THEN
    RETURN FALSE;
  END IF;

  -- Update used points
  UPDATE referral_points 
  SET 
    used_for_matches = used_for_matches + p_points,
    updated_at = NOW()
  WHERE user_id = p_user_id;

  -- Record transaction
  INSERT INTO referral_point_transactions (
    user_id, transaction_type, points, source_type, source_id, description
  ) VALUES (
    p_user_id, 'used', p_points, p_source_type, p_source_id, p_description
  );

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's referral points balance
CREATE OR REPLACE FUNCTION get_referral_points_balance(p_user_id UUID)
RETURNS TABLE(
  total_points INTEGER,
  available_points INTEGER,
  used_points INTEGER,
  earned_from_referrals INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(rp.total_points, 0) as total_points,
    COALESCE(rp.available_points, 0) as available_points,
    COALESCE(rp.used_for_matches, 0) as used_points,
    COALESCE(rp.earned_from_referrals, 0) as earned_from_referrals
  FROM referral_points rp
  WHERE rp.user_id = p_user_id
  UNION ALL
  SELECT 0, 0, 0, 0
  WHERE NOT EXISTS (SELECT 1 FROM referral_points WHERE user_id = p_user_id)
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ==================== FRAUD DETECTION FUNCTIONS ====================

-- Function to calculate fraud score based on multiple factors
CREATE OR REPLACE FUNCTION calculate_referral_fraud_score(
  p_device_fingerprint VARCHAR(255),
  p_ip_address INET,
  p_referrer_id UUID,
  p_referred_id UUID
)
RETURNS DECIMAL(5,2) AS $$
DECLARE
  v_fraud_score DECIMAL(5,2) := 0.0;
  v_device_count INTEGER;
  v_ip_count INTEGER;
  v_referrer_recent_count INTEGER;
  v_time_pattern_score DECIMAL(3,2);
BEGIN
  -- Check device fingerprint usage
  SELECT user_count INTO v_device_count
  FROM device_fingerprints
  WHERE fingerprint_hash = p_device_fingerprint;
  
  IF v_device_count > 3 THEN
    v_fraud_score := v_fraud_score + 0.3;
  ELSIF v_device_count > 1 THEN
    v_fraud_score := v_fraud_score + 0.1;
  END IF;

  -- Check IP address usage
  SELECT referral_count INTO v_ip_count
  FROM referral_ip_tracking
  WHERE ip_address = p_ip_address
  AND last_referral > NOW() - INTERVAL '24 hours';
  
  IF v_ip_count > 5 THEN
    v_fraud_score := v_fraud_score + 0.4;
  ELSIF v_ip_count > 2 THEN
    v_fraud_score := v_fraud_score + 0.2;
  END IF;

  -- Check referrer's recent activity
  SELECT COUNT(*) INTO v_referrer_recent_count
  FROM user_referrals
  WHERE referrer_id = p_referrer_id
  AND signup_date > NOW() - INTERVAL '24 hours';
  
  IF v_referrer_recent_count > 3 THEN
    v_fraud_score := v_fraud_score + 0.3;
  ELSIF v_referrer_recent_count > 1 THEN
    v_fraud_score := v_fraud_score + 0.1;
  END IF;

  -- Check if users are related (same device, similar timing)
  IF EXISTS (
    SELECT 1 FROM user_referrals ur
    JOIN referral_fraud_detection rfd ON ur.id = rfd.referral_id
    WHERE (ur.referrer_id = p_referred_id OR ur.referred_id = p_referrer_id)
    AND rfd.device_fingerprint = p_device_fingerprint
  ) THEN
    v_fraud_score := v_fraud_score + 0.5;
  END IF;

  -- Cap the score at 1.0
  IF v_fraud_score > 1.0 THEN
    v_fraud_score := 1.0;
  END IF;

  RETURN v_fraud_score;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update device fingerprint tracking
CREATE OR REPLACE FUNCTION update_device_fingerprint_tracking(
  p_fingerprint_hash VARCHAR(255),
  p_metadata JSONB DEFAULT '{}'
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO device_fingerprints (fingerprint_hash, user_count, metadata)
  VALUES (p_fingerprint_hash, 1, p_metadata)
  ON CONFLICT (fingerprint_hash)
  DO UPDATE SET
    user_count = device_fingerprints.user_count + 1,
    last_seen = NOW(),
    is_suspicious = CASE 
      WHEN device_fingerprints.user_count + 1 > 3 THEN TRUE 
      ELSE device_fingerprints.is_suspicious 
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update IP tracking for referrals
CREATE OR REPLACE FUNCTION update_referral_ip_tracking(
  p_ip_address INET,
  p_metadata JSONB DEFAULT '{}'
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO referral_ip_tracking (ip_address, referral_count, user_count, metadata)
  VALUES (p_ip_address, 1, 1, p_metadata)
  ON CONFLICT (ip_address)
  DO UPDATE SET
    referral_count = referral_ip_tracking.referral_count + 1,
    last_referral = NOW(),
    risk_score = CASE 
      WHEN referral_ip_tracking.referral_count + 1 > 10 THEN 1.0
      WHEN referral_ip_tracking.referral_count + 1 > 5 THEN 0.7
      WHEN referral_ip_tracking.referral_count + 1 > 2 THEN 0.3
      ELSE 0.0
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ==================== VERIFICATION FUNCTIONS ====================

-- Function to check if user meets verification requirements
CREATE OR REPLACE FUNCTION check_referral_eligibility(p_user_id UUID)
RETURNS TABLE(
  is_eligible BOOLEAN,
  phone_verified BOOLEAN,
  email_verified BOOLEAN,
  activity_score INTEGER,
  days_since_registration INTEGER,
  missing_requirements TEXT[]
) AS $$
DECLARE
  v_user_record RECORD;
  v_missing_requirements TEXT[] := '{}';
  v_is_eligible BOOLEAN := TRUE;
  v_days_since_reg INTEGER;
BEGIN
  -- Get user verification status
  SELECT 
    u.phone_verified,
    u.email_verified,
    u.account_activity_score,
    u.created_at,
    u.referral_eligible
  INTO v_user_record
  FROM users u
  WHERE u.id = p_user_id;

  -- Calculate days since registration
  v_days_since_reg := EXTRACT(DAY FROM NOW() - v_user_record.created_at);

  -- Check phone verification
  IF NOT COALESCE(v_user_record.phone_verified, FALSE) THEN
    v_missing_requirements := array_append(v_missing_requirements, 'phone_verification');
    v_is_eligible := FALSE;
  END IF;

  -- Check email verification
  IF NOT COALESCE(v_user_record.email_verified, FALSE) THEN
    v_missing_requirements := array_append(v_missing_requirements, 'email_verification');
    v_is_eligible := FALSE;
  END IF;

  -- Check minimum activity (configurable)
  IF COALESCE(v_user_record.account_activity_score, 0) < 10 THEN
    v_missing_requirements := array_append(v_missing_requirements, 'minimum_activity');
    v_is_eligible := FALSE;
  END IF;

  -- Check minimum days since registration
  IF v_days_since_reg < 1 THEN
    v_missing_requirements := array_append(v_missing_requirements, 'minimum_age');
    v_is_eligible := FALSE;
  END IF;

  RETURN QUERY SELECT 
    v_is_eligible,
    COALESCE(v_user_record.phone_verified, FALSE),
    COALESCE(v_user_record.email_verified, FALSE),
    COALESCE(v_user_record.account_activity_score, 0),
    v_days_since_reg,
    v_missing_requirements;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ==================== ADMIN FUNCTIONS ====================

-- Function to get referral fraud analytics
CREATE OR REPLACE FUNCTION get_referral_fraud_analytics(
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
  p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE(
  total_referrals INTEGER,
  flagged_referrals INTEGER,
  approved_referrals INTEGER,
  rejected_referrals INTEGER,
  pending_review INTEGER,
  avg_fraud_score DECIMAL(5,2),
  high_risk_ips INTEGER,
  suspicious_devices INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_referrals,
    COUNT(CASE WHEN rfd.fraud_score > 0.7 THEN 1 END)::INTEGER as flagged_referrals,
    COUNT(CASE WHEN rfd.status = 'approved' THEN 1 END)::INTEGER as approved_referrals,
    COUNT(CASE WHEN rfd.status = 'rejected' THEN 1 END)::INTEGER as rejected_referrals,
    COUNT(CASE WHEN rfd.status = 'pending' AND rfd.fraud_score > 0.5 THEN 1 END)::INTEGER as pending_review,
    COALESCE(AVG(rfd.fraud_score), 0.0)::DECIMAL(5,2) as avg_fraud_score,
    (SELECT COUNT(*) FROM referral_ip_tracking WHERE risk_score > 0.7)::INTEGER as high_risk_ips,
    (SELECT COUNT(*) FROM device_fingerprints WHERE is_suspicious = TRUE)::INTEGER as suspicious_devices
  FROM user_referrals ur
  LEFT JOIN referral_fraud_detection rfd ON ur.fraud_detection_id = rfd.id
  WHERE ur.signup_date BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to approve/reject referral
CREATE OR REPLACE FUNCTION admin_review_referral(
  p_referral_id UUID,
  p_admin_id UUID,
  p_action VARCHAR(10), -- 'approve' or 'reject'
  p_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  v_fraud_detection_id UUID;
  v_referrer_id UUID;
  v_referred_id UUID;
  v_reward_points INTEGER;
BEGIN
  -- Get referral details
  SELECT fraud_detection_id, referrer_id, referred_id
  INTO v_fraud_detection_id, v_referrer_id, v_referred_id
  FROM user_referrals
  WHERE id = p_referral_id;

  -- Get reward points from settings
  SELECT (setting_value::TEXT)::INTEGER INTO v_reward_points
  FROM referral_settings
  WHERE setting_key = 'referral_reward_points';

  -- Update fraud detection record
  UPDATE referral_fraud_detection
  SET 
    status = p_action || 'd', -- 'approved' or 'rejected'
    admin_reviewed = TRUE,
    admin_notes = p_notes,
    updated_at = NOW()
  WHERE id = v_fraud_detection_id;

  -- Update user_referrals
  UPDATE user_referrals
  SET 
    admin_approved = CASE WHEN p_action = 'approve' THEN TRUE ELSE FALSE END,
    status = CASE WHEN p_action = 'approve' THEN 'approved' ELSE 'rejected' END
  WHERE id = p_referral_id;

  -- If approved, award points
  IF p_action = 'approve' THEN
    -- Award points to referrer
    PERFORM award_referral_points(
      v_referrer_id, 
      v_reward_points, 
      'referral_reward', 
      p_referral_id, 
      'Referral reward - referrer'
    );
    
    -- Award points to referee
    PERFORM award_referral_points(
      v_referred_id, 
      v_reward_points, 
      'referral_reward', 
      p_referral_id, 
      'Referral reward - referee'
    );

    -- Update awarded points tracking
    UPDATE user_referrals
    SET 
      reward_points_awarded = v_reward_points,
      referrer_points_awarded = v_reward_points
    WHERE id = p_referral_id;
  END IF;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
