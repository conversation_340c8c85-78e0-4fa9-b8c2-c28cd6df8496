-- Tournament Performance Optimization Migration
-- Date: 2025-07-24
-- Description: Add database indexes and optimize queries for tournament system performance

-- ==================== TOURNAMENT AUDIT LOG TABLE ====================

-- Create tournament audit log table for comprehensive audit logging
CREATE TABLE IF NOT EXISTS tournament_audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tournament_id UUID NOT NULL REFERENCES tournaments(id) ON DELETE CASCADE,
  action_type VARCHAR(50) NOT NULL CHECK (action_type IN (
    'tournament_created',
    'tournament_updated', 
    'tournament_deleted',
    'participant_joined',
    'participant_left',
    'participant_removed',
    'result_submitted',
    'result_verified',
    'result_rejected',
    'winner_assigned',
    'tournament_completed',
    'tournament_cancelled',
    'entry_fee_processed',
    'prize_distributed',
    'admin_override'
  )),
  triggered_by VARCHAR(20) DEFAULT 'user' CHECK (triggered_by IN ('user', 'admin', 'system', 'cron_job')),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  admin_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Action details
  affected_participants JSONB DEFAULT '[]', -- Array of participant user IDs
  action_details JSONB NOT NULL DEFAULT '{}', -- Detailed information about the action
  reason TEXT,
  
  -- Request metadata
  ip_address INET,
  user_agent TEXT,
  
  -- Timing information
  action_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- System information
  system_version VARCHAR(20) DEFAULT '1.0'
);

-- ==================== PERFORMANCE INDEXES ====================

-- Tournament table indexes for common queries
CREATE INDEX IF NOT EXISTS idx_tournaments_status ON tournaments(status);
CREATE INDEX IF NOT EXISTS idx_tournaments_game_id ON tournaments(game_id);
CREATE INDEX IF NOT EXISTS idx_tournaments_created_by ON tournaments(created_by);
CREATE INDEX IF NOT EXISTS idx_tournaments_start_date ON tournaments(start_date);
CREATE INDEX IF NOT EXISTS idx_tournaments_end_date ON tournaments(end_date);
CREATE INDEX IF NOT EXISTS idx_tournaments_entry_fee ON tournaments(entry_fee);
CREATE INDEX IF NOT EXISTS idx_tournaments_status_game ON tournaments(status, game_id);
CREATE INDEX IF NOT EXISTS idx_tournaments_status_start_date ON tournaments(status, start_date);
CREATE INDEX IF NOT EXISTS idx_tournaments_public_status ON tournaments(status) WHERE status IN ('upcoming', 'active');

-- Tournament participants indexes for performance
CREATE INDEX IF NOT EXISTS idx_tournament_participants_tournament_id ON tournament_participants(tournament_id);
CREATE INDEX IF NOT EXISTS idx_tournament_participants_user_id ON tournament_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_tournament_participants_status ON tournament_participants(status);
CREATE INDEX IF NOT EXISTS idx_tournament_participants_type ON tournament_participants(participant_type);
CREATE INDEX IF NOT EXISTS idx_tournament_participants_winner ON tournament_participants(is_winner) WHERE is_winner = true;
CREATE INDEX IF NOT EXISTS idx_tournament_participants_tournament_status ON tournament_participants(tournament_id, status);
CREATE INDEX IF NOT EXISTS idx_tournament_participants_tournament_type ON tournament_participants(tournament_id, participant_type);
CREATE INDEX IF NOT EXISTS idx_tournament_participants_user_tournament ON tournament_participants(user_id, tournament_id);
CREATE INDEX IF NOT EXISTS idx_tournament_participants_joined_at ON tournament_participants(joined_at);

-- Tournament results indexes for verification and queries
CREATE INDEX IF NOT EXISTS idx_tournament_results_tournament_id ON tournament_results(tournament_id);
CREATE INDEX IF NOT EXISTS idx_tournament_results_user_id ON tournament_results(user_id);
CREATE INDEX IF NOT EXISTS idx_tournament_results_verification_status ON tournament_results(verification_status);
CREATE INDEX IF NOT EXISTS idx_tournament_results_verified_by ON tournament_results(verified_by);
CREATE INDEX IF NOT EXISTS idx_tournament_results_submitted_at ON tournament_results(submitted_at);
CREATE INDEX IF NOT EXISTS idx_tournament_results_verified_at ON tournament_results(verified_at);
CREATE INDEX IF NOT EXISTS idx_tournament_results_tournament_verification ON tournament_results(tournament_id, verification_status);
CREATE INDEX IF NOT EXISTS idx_tournament_results_pending ON tournament_results(verification_status, submitted_at) WHERE verification_status = 'pending';
CREATE INDEX IF NOT EXISTS idx_tournament_results_position ON tournament_results(final_position);

-- Tournament audit log indexes for monitoring and reporting
CREATE INDEX IF NOT EXISTS idx_tournament_audit_log_tournament_id ON tournament_audit_log(tournament_id);
CREATE INDEX IF NOT EXISTS idx_tournament_audit_log_action_type ON tournament_audit_log(action_type);
CREATE INDEX IF NOT EXISTS idx_tournament_audit_log_triggered_by ON tournament_audit_log(triggered_by);
CREATE INDEX IF NOT EXISTS idx_tournament_audit_log_user_id ON tournament_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_tournament_audit_log_admin_user_id ON tournament_audit_log(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_tournament_audit_log_timestamp ON tournament_audit_log(action_timestamp);
CREATE INDEX IF NOT EXISTS idx_tournament_audit_log_tournament_action ON tournament_audit_log(tournament_id, action_type);
CREATE INDEX IF NOT EXISTS idx_tournament_audit_log_recent ON tournament_audit_log(action_timestamp DESC);

-- Composite indexes for complex queries
CREATE INDEX IF NOT EXISTS idx_tournaments_active_game_entry ON tournaments(game_id, entry_fee) WHERE status IN ('upcoming', 'active');
CREATE INDEX IF NOT EXISTS idx_tournament_participants_active_tournaments ON tournament_participants(user_id, tournament_id) 
  WHERE tournament_id IN (SELECT id FROM tournaments WHERE status IN ('upcoming', 'active'));

-- ==================== ROW LEVEL SECURITY ====================

-- Enable RLS on tournament audit log table
ALTER TABLE tournament_audit_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies for tournament_audit_log
CREATE POLICY "Admins can view all tournament audit logs" 
  ON tournament_audit_log FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'super admin', 'manager', 'moderator')
    )
  );

CREATE POLICY "Users can view their own tournament audit logs" 
  ON tournament_audit_log FOR SELECT 
  USING (user_id = auth.uid());

CREATE POLICY "System can insert tournament audit logs" 
  ON tournament_audit_log FOR INSERT 
  WITH CHECK (triggered_by IN ('system', 'cron_job', 'user', 'admin'));

-- ==================== QUERY OPTIMIZATION VIEWS ====================

-- Create materialized view for tournament statistics (optional, for heavy reporting)
-- This can be refreshed periodically for better performance on dashboard queries
CREATE OR REPLACE VIEW tournament_statistics AS
SELECT 
  t.id,
  t.name,
  t.status,
  t.game_id,
  t.entry_fee,
  t.prize_pool,
  t.start_date,
  t.end_date,
  COUNT(tp.id) as total_participants,
  COUNT(CASE WHEN tp.participant_type = 'participant' THEN 1 END) as active_participants,
  COUNT(CASE WHEN tp.participant_type = 'spectator' THEN 1 END) as spectators,
  COUNT(CASE WHEN tp.is_winner = true THEN 1 END) as winners_count,
  COUNT(tr.id) as results_submitted,
  COUNT(CASE WHEN tr.verification_status = 'pending' THEN 1 END) as pending_results,
  COUNT(CASE WHEN tr.verification_status = 'verified' THEN 1 END) as verified_results,
  COUNT(CASE WHEN tr.verification_status = 'rejected' THEN 1 END) as rejected_results
FROM tournaments t
LEFT JOIN tournament_participants tp ON t.id = tp.tournament_id
LEFT JOIN tournament_results tr ON t.id = tr.tournament_id
GROUP BY t.id, t.name, t.status, t.game_id, t.entry_fee, t.prize_pool, t.start_date, t.end_date;

-- Create view for active tournament participants (frequently queried)
CREATE OR REPLACE VIEW active_tournament_participants AS
SELECT 
  tp.*,
  t.name as tournament_name,
  t.status as tournament_status,
  t.start_date,
  t.end_date,
  u.name as user_name,
  u.email as user_email
FROM tournament_participants tp
JOIN tournaments t ON tp.tournament_id = t.id
JOIN users u ON tp.user_id = u.id
WHERE t.status IN ('upcoming', 'active');

-- ==================== PERFORMANCE MONITORING ====================

-- Add comments for monitoring query performance
COMMENT ON INDEX idx_tournaments_status IS 'Primary index for filtering tournaments by status';
COMMENT ON INDEX idx_tournament_participants_tournament_status IS 'Composite index for participant queries by tournament and status';
COMMENT ON INDEX idx_tournament_results_pending IS 'Partial index for admin verification workflows';
COMMENT ON INDEX idx_tournament_audit_log_recent IS 'Optimized for recent audit log queries';

-- ==================== GRANTS AND PERMISSIONS ====================

-- Grant necessary permissions for the audit log table
GRANT SELECT ON tournament_audit_log TO authenticated;
GRANT INSERT ON tournament_audit_log TO authenticated;
GRANT SELECT ON tournament_statistics TO authenticated;
GRANT SELECT ON active_tournament_participants TO authenticated;
