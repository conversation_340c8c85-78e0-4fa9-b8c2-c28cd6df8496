/**
 * Chat Service
 * Business logic for real-time chat operations
 */

import { supabase } from '../../../config/supabase';
import { logger } from '../../../utils/logger';
import { ApiError } from '../../../utils/errorHandler';

export interface CreateChatSessionData {
  userId: string;
  sessionType: string;
}

export interface SendChatMessageData {
  sessionId: string;
  senderId: string;
  senderType: string;
  messageText: string;
  messageType: string;
  metadata: any;
}

export interface ChatSessionFilters {
  status?: string;
  session_type?: string;
  admin_user_id?: string;
  started_after?: string;
  started_before?: string;
  page?: number;
  limit?: number;
}

export const chatService = {
  /**
   * Create a new chat session
   */
  async createChatSession(data: CreateChatSessionData) {
    try {
      // Check if user already has an active session
      const { data: existingSession, error: checkError } = await supabase
        .from('chat_sessions')
        .select('id')
        .eq('user_id', data.userId)
        .eq('status', 'active')
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        logger.error(`Error checking existing chat session: ${checkError.message}`);
        throw new ApiError(500, 'Failed to check existing session');
      }

      if (existingSession) {
        // Return existing active session
        const { data: session, error } = await supabase
          .from('chat_sessions')
          .select(`
            *,
            user:users(id, name, email),
            admin:users!admin_user_id(id, name, email)
          `)
          .eq('id', existingSession.id)
          .single();

        if (error) {
          logger.error(`Error getting existing chat session: ${error.message}`);
          throw new ApiError(500, 'Failed to retrieve existing session');
        }

        return session;
      }

      // Create new session
      const { data: chatSession, error } = await supabase
        .from('chat_sessions')
        .insert({
          user_id: data.userId,
          session_type: data.sessionType,
          status: 'waiting'
        })
        .select(`
          *,
          user:users(id, name, email),
          admin:users!admin_user_id(id, name, email)
        `)
        .single();

      if (error) {
        logger.error(`Error creating chat session: ${error.message}`);
        throw new ApiError(500, 'Failed to create chat session');
      }

      // Send initial system message
      await this.sendSystemMessage(chatSession.id, 'Chat session started. Please wait for an admin to join.');

      // Notify admins of new chat session
      await this.notifyAdminsOfNewChatSession(chatSession);

      return chatSession;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Chat service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get user's chat sessions
   */
  async getUserChatSessions(userId: string, filters: ChatSessionFilters) {
    try {
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const offset = (page - 1) * limit;

      let query = supabase
        .from('chat_sessions')
        .select(`
          *,
          user:users(id, name, email),
          admin:users!admin_user_id(id, name, email),
          message_count:chat_messages(count)
        `, { count: 'exact' })
        .eq('user_id', userId)
        .order('last_activity', { ascending: false });

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.session_type) {
        query = query.eq('session_type', filters.session_type);
      }
      if (filters.started_after) {
        query = query.gte('started_at', filters.started_after);
      }
      if (filters.started_before) {
        query = query.lte('started_at', filters.started_before);
      }

      const { data, error, count } = await query
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error(`Error getting user chat sessions: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve chat sessions');
      }

      return {
        sessions: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Chat service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get all chat sessions (admin only)
   */
  async getAllChatSessions(filters: ChatSessionFilters) {
    try {
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const offset = (page - 1) * limit;

      let query = supabase
        .from('chat_sessions')
        .select(`
          *,
          user:users(id, name, email),
          admin:users!admin_user_id(id, name, email),
          message_count:chat_messages(count)
        `, { count: 'exact' })
        .order('last_activity', { ascending: false });

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.session_type) {
        query = query.eq('session_type', filters.session_type);
      }
      if (filters.admin_user_id) {
        query = query.eq('admin_user_id', filters.admin_user_id);
      }
      if (filters.started_after) {
        query = query.gte('started_at', filters.started_after);
      }
      if (filters.started_before) {
        query = query.lte('started_at', filters.started_before);
      }

      const { data, error, count } = await query
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error(`Error getting all chat sessions: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve chat sessions');
      }

      return {
        sessions: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Chat service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get chat session by ID
   */
  async getChatSessionById(sessionId: string, userId: string, userRole?: string) {
    try {
      let query = supabase
        .from('chat_sessions')
        .select(`
          *,
          user:users(id, name, email),
          admin:users!admin_user_id(id, name, email)
        `)
        .eq('id', sessionId);

      // Non-admin users can only see their own sessions
      if (!userRole || !['admin', 'manager', 'spectator'].includes(userRole)) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query.single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Session not found
        }
        logger.error(`Error getting chat session by ID: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve chat session');
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Chat service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Send chat message
   */
  async sendChatMessage(data: SendChatMessageData) {
    try {
      // Verify session access
      const session = await this.getChatSessionById(
        data.sessionId,
        data.senderId,
        data.senderType === 'admin' ? 'admin' : undefined
      );

      if (!session) {
        throw new ApiError(404, 'Chat session not found');
      }

      if (session.status === 'closed') {
        throw new ApiError(400, 'Cannot send message to closed session');
      }

      const { data: message, error } = await supabase
        .from('chat_messages')
        .insert({
          chat_session_id: data.sessionId,
          sender_id: data.senderId,
          sender_type: data.senderType,
          message_text: data.messageText,
          message_type: data.messageType,
          metadata: data.metadata
        })
        .select(`
          *,
          sender:users(id, name, email)
        `)
        .single();

      if (error) {
        logger.error(`Error sending chat message: ${error.message}`);
        throw new ApiError(500, 'Failed to send message');
      }

      // Update session last activity
      await supabase
        .from('chat_sessions')
        .update({
          last_activity: new Date().toISOString(),
          status: session.status === 'waiting' ? 'active' : session.status
        })
        .eq('id', data.sessionId);

      return message;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Chat service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get chat messages
   */
  async getChatMessages(sessionId: string, userId: string, userRole?: string) {
    try {
      // Verify session access
      const session = await this.getChatSessionById(sessionId, userId, userRole);
      if (!session) {
        throw new ApiError(404, 'Chat session not found');
      }

      const { data, error } = await supabase
        .from('chat_messages')
        .select(`
          *,
          sender:users(id, name, email)
        `)
        .eq('chat_session_id', sessionId)
        .order('created_at', { ascending: true });

      if (error) {
        logger.error(`Error getting chat messages: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve messages');
      }

      // Mark messages as read for the current user
      if (data && data.length > 0) {
        await supabase
          .from('chat_messages')
          .update({
            is_read: true,
            read_at: new Date().toISOString()
          })
          .eq('chat_session_id', sessionId)
          .neq('sender_id', userId)
          .eq('is_read', false);
      }

      return data || [];
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Chat service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Close chat session
   */
  async closeChatSession(sessionId: string, userId: string, userRole?: string) {
    try {
      // Verify session access
      const session = await this.getChatSessionById(sessionId, userId, userRole);
      if (!session) {
        return null;
      }

      const { data, error } = await supabase
        .from('chat_sessions')
        .update({
          status: 'closed',
          ended_at: new Date().toISOString()
        })
        .eq('id', sessionId)
        .select(`
          *,
          user:users(id, name, email),
          admin:users!admin_user_id(id, name, email)
        `)
        .single();

      if (error) {
        logger.error(`Error closing chat session: ${error.message}`);
        throw new ApiError(500, 'Failed to close session');
      }

      // Send system message
      await this.sendSystemMessage(sessionId, 'Chat session has been closed.');

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Chat service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Assign chat session to admin
   */
  async assignChatSession(sessionId: string, adminUserId: string, assignedBy: string) {
    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .update({
          admin_user_id: adminUserId,
          status: 'active'
        })
        .eq('id', sessionId)
        .select(`
          *,
          user:users(id, name, email),
          admin:users!admin_user_id(id, name, email)
        `)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null;
        }
        logger.error(`Error assigning chat session: ${error.message}`);
        throw new ApiError(500, 'Failed to assign session');
      }

      // Send system message
      await this.sendSystemMessage(sessionId, `Admin ${data.admin?.name || 'Support'} has joined the chat.`);

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Chat service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Transfer chat session to another admin
   */
  async transferChatSession(sessionId: string, newAdminId: string, transferredBy: string) {
    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .update({
          admin_user_id: newAdminId,
          status: 'transferred'
        })
        .eq('id', sessionId)
        .select(`
          *,
          user:users(id, name, email),
          admin:users!admin_user_id(id, name, email)
        `)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null;
        }
        logger.error(`Error transferring chat session: ${error.message}`);
        throw new ApiError(500, 'Failed to transfer session');
      }

      // Send system message
      await this.sendSystemMessage(sessionId, `Chat has been transferred to ${data.admin?.name || 'another admin'}.`);

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Chat service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Send system message
   */
  async sendSystemMessage(sessionId: string, messageText: string) {
    try {
      await supabase
        .from('chat_messages')
        .insert({
          chat_session_id: sessionId,
          sender_id: null,
          sender_type: 'system',
          message_text: messageText,
          message_type: 'system',
          metadata: {}
        });
    } catch (error) {
      logger.error(`Error sending system message: ${error}`);
    }
  },

  /**
   * Notify admins of new chat session
   */
  async notifyAdminsOfNewChatSession(chatSession: any) {
    try {
      // Get all admin users
      const { data: admins, error } = await supabase
        .from('users')
        .select('id')
        .in('role', ['admin', 'manager', 'spectator']);

      if (error || !admins) {
        logger.error(`Error getting admin users for chat notification: ${error?.message}`);
        return;
      }

      // Create notifications for all admins
      const notifications = admins.map((admin: any) => ({
        user_id: admin.id,
        title: 'New Chat Session',
        message: `New chat session started by ${chatSession.user?.name || 'User'}`,
        type: 'chat_session',
        data: {
          chat_session_id: chatSession.id,
          session_type: chatSession.session_type
        },
        created_at: new Date().toISOString(),
        read: false
      }));

      await supabase
        .from('notifications')
        .insert(notifications);

    } catch (error) {
      logger.error(`Error notifying admins of new chat session: ${error}`);
    }
  }
};
