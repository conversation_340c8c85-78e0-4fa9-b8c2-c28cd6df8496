/**
 * Input Sanitization Utilities
 * Provides comprehensive input sanitization and validation for support system
 */

import DOMPurify from 'isomorphic-dompurify';
import { logger } from './logger';

export interface SanitizationOptions {
  allowHtml?: boolean;
  maxLength?: number;
  trimWhitespace?: boolean;
  removeEmojis?: boolean;
  allowedTags?: string[];
  allowedAttributes?: string[];
}

export class InputSanitizer {
  /**
   * Sanitize text input with various options
   */
  static sanitizeText(
    input: string,
    options: SanitizationOptions = {}
  ): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }

    let sanitized = input;

    // Trim whitespace if requested
    if (options.trimWhitespace !== false) {
      sanitized = sanitized.trim();
    }

    // Remove or limit length
    if (options.maxLength && sanitized.length > options.maxLength) {
      sanitized = sanitized.substring(0, options.maxLength);
    }

    // Remove emojis if requested
    if (options.removeEmojis) {
      sanitized = this.removeEmojis(sanitized);
    }

    // Handle HTML content
    if (options.allowHtml) {
      sanitized = this.sanitizeHtml(sanitized, options);
    } else {
      // Escape HTML entities
      sanitized = this.escapeHtml(sanitized);
    }

    // Remove potential SQL injection patterns
    sanitized = this.removeSqlInjectionPatterns(sanitized);

    // Remove potential XSS patterns
    sanitized = this.removeXssPatterns(sanitized);

    return sanitized;
  }

  /**
   * Sanitize HTML content using DOMPurify
   */
  static sanitizeHtml(
    html: string,
    options: SanitizationOptions = {}
  ): string {
    const config: any = {
      ALLOWED_TAGS: options.allowedTags || ['p', 'br', 'strong', 'em', 'u'],
      ALLOWED_ATTR: options.allowedAttributes || [],
      KEEP_CONTENT: true,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_DOM_IMPORT: false,
    };

    return DOMPurify.sanitize(html, config) as unknown as string;
  }

  /**
   * Escape HTML entities
   */
  static escapeHtml(text: string): string {
    const htmlEscapes: { [key: string]: string } = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;',
    };

    return text.replace(/[&<>"'/]/g, (match) => htmlEscapes[match]);
  }

  /**
   * Remove emoji characters
   */
  static removeEmojis(text: string): string {
    return text.replace(
      /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu,
      ''
    );
  }

  /**
   * Remove potential SQL injection patterns
   */
  static removeSqlInjectionPatterns(text: string): string {
    // More targeted SQL injection prevention - only remove obvious injection attempts
    const sqlPatterns = [
      /(\bSELECT\b.*\bFROM\b.*\bWHERE\b)/gi, // Full SQL SELECT statements
      /(\bINSERT\b.*\bINTO\b.*\bVALUES\b)/gi, // Full SQL INSERT statements
      /(\bUPDATE\b.*\bSET\b.*\bWHERE\b)/gi, // Full SQL UPDATE statements
      /(\bDELETE\b.*\bFROM\b.*\bWHERE\b)/gi, // Full SQL DELETE statements
      /(\bDROP\b.*\bTABLE\b)/gi, // DROP TABLE statements
      /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi, // Classic injection patterns
      // Removed single quotes and other characters that might be legitimate
    ];

    let sanitized = text;
    sqlPatterns.forEach((pattern) => {
      sanitized = sanitized.replace(pattern, '[FILTERED]');
    });

    return sanitized;
  }

  /**
   * Remove potential XSS patterns
   */
  static removeXssPatterns(text: string): string {
    // Only remove obvious XSS attempts, keep legitimate content
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      // Removed event handlers that might be legitimate text
    ];

    let sanitized = text;
    xssPatterns.forEach((pattern) => {
      sanitized = sanitized.replace(pattern, '[FILTERED]');
    });

    return sanitized;
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format
   */
  static isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Check for profanity and inappropriate content
   */
  static containsProfanity(text: string): boolean {
    // More restrictive profanity filter - only block truly inappropriate content
    // Removed common tech terms that might appear in legitimate support messages
    const profanityWords = [
      'f*ck', 'sh*t', 'b*tch', 'a**hole', 'damn*t',
      // Only include actual profanity, not tech terms
    ];

    const lowerText = text.toLowerCase();
    return profanityWords.some(word => lowerText.includes(word.replace('*', '')));
  }

  /**
   * Detect potential spam content
   */
  static isSpamContent(text: string): boolean {
    const spamIndicators = [
      /(.)\1{10,}/g, // Only flag excessive repeated characters (10+ in a row)
      /\b(CLICK HERE NOW|MAKE MONEY FAST|GET RICH QUICK|VIAGRA|CASINO)\b/gi, // More specific spam phrases
      /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, // Credit card patterns
      // Removed URL detection and money amounts as they might be legitimate in support messages
    ];

    return spamIndicators.some(pattern => pattern.test(text));
  }

  /**
   * Comprehensive content validation
   */
  static validateContent(
    content: string,
    type: 'message' | 'subject' | 'reply' = 'message'
  ): {
    isValid: boolean;
    errors: string[];
    sanitized: string;
  } {
    const errors: string[] = [];
    let sanitized = content;

    try {
      // Basic validation
      if (!content || typeof content !== 'string') {
        errors.push('Content is required and must be a string');
        return { isValid: false, errors, sanitized: '' };
      }

      // Length validation based on type
      const maxLengths = {
        subject: 200,
        message: 2000,
        reply: 2000,
      };

      const maxLength = maxLengths[type];
      if (content.length > maxLength) {
        errors.push(`Content exceeds maximum length of ${maxLength} characters`);
      }

      // Minimum length validation
      const minLengths = {
        subject: 5,
        message: 10,
        reply: 5,
      };

      const minLength = minLengths[type];
      if (content.trim().length < minLength) {
        errors.push(`Content must be at least ${minLength} characters long`);
      }

      // Sanitize content
      sanitized = this.sanitizeText(content, {
        maxLength,
        trimWhitespace: true,
        allowHtml: false,
      });

      // Check for spam (only for obvious spam patterns)
      if (this.isSpamContent(sanitized)) {
        errors.push('Content appears to be spam');
      }

      // Check for profanity (only for actual inappropriate language)
      if (this.containsProfanity(sanitized)) {
        errors.push('Content contains inappropriate language');
      }

      // More lenient check for content modification - only flag if severely modified
      if (sanitized.length < content.trim().length * 0.2) {
        errors.push('Content contains too many invalid characters');
      }

      return {
        isValid: errors.length === 0,
        errors,
        sanitized,
      };
    } catch (error) {
      logger.error(`Content validation error: ${error}`);
      return {
        isValid: false,
        errors: ['Content validation failed'],
        sanitized: '',
      };
    }
  }

  /**
   * Sanitize support message data
   */
  static sanitizeSupportMessage(data: {
    subject: string;
    category: string;
    message: string;
  }): {
    isValid: boolean;
    errors: string[];
    sanitized: {
      subject: string;
      category: string;
      message: string;
    };
  } {
    const errors: string[] = [];
    const sanitized = {
      subject: '',
      category: '',
      message: '',
    };

    try {
      // Validate subject
      if (data.subject) {
        const subjectValidation = this.validateContent(data.subject, 'subject');
        if (!subjectValidation.isValid) {
          errors.push(...subjectValidation.errors.map(e => `Subject: ${e}`));
        }
        sanitized.subject = subjectValidation.sanitized;
      } else {
        errors.push('Subject: Content is required and must be a string');
      }

      // Validate category
      const allowedCategories = [
        'bug_report',
        'feature_request',
        'general_inquiry',
        'account_issue',
        'payment_issue',
        'technical_support',
      ];

      if (!data.category || !allowedCategories.includes(data.category)) {
        errors.push('Invalid category selected');
      } else {
        sanitized.category = data.category;
      }

      // Validate message
      if (data.message) {
        const messageValidation = this.validateContent(data.message, 'message');
        if (!messageValidation.isValid) {
          errors.push(...messageValidation.errors.map(e => `Message: ${e}`));
        }
        sanitized.message = messageValidation.sanitized;
      } else {
        errors.push('Message: Content is required and must be a string');
      }

      return {
        isValid: errors.length === 0,
        errors,
        sanitized,
      };
    } catch (error) {
      logger.error(`Support message sanitization error: ${error}`);
      return {
        isValid: false,
        errors: [`Sanitization failed: ${error}`],
        sanitized,
      };
    }
  }

  /**
   * Sanitize chat message data
   */
  static sanitizeChatMessage(data: {
    messageText: string;
    messageType?: string;
  }): {
    isValid: boolean;
    errors: string[];
    sanitized: {
      messageText: string;
      messageType: string;
    };
  } {
    const errors: string[] = [];
    const sanitized = {
      messageText: '',
      messageType: data.messageType || 'text',
    };

    // Validate message text
    const messageValidation = this.validateContent(data.messageText, 'message');
    if (!messageValidation.isValid) {
      errors.push(...messageValidation.errors);
    }
    sanitized.messageText = messageValidation.sanitized;

    // Validate message type
    const allowedTypes = ['text', 'image', 'file', 'system'];
    if (!allowedTypes.includes(sanitized.messageType)) {
      errors.push('Invalid message type');
      sanitized.messageType = 'text';
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitized,
    };
  }
}
