import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'

const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8080/api/v1'

// Helper function to get admin token from Supabase session
async function getAdminToken(request: NextRequest) {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set() {
          // Not needed for server-side operations
        },
        remove() {
          // Not needed for server-side operations
        },
      },
    }
  )

  try {
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Supabase auth error:', error)
      throw new Error(`Authentication error: ${error.message}`)
    }

    if (!session?.access_token) {
      console.log('No session found. Available cookies:', request.cookies.getAll().map(c => c.name))
      throw new Error('No valid authentication session')
    }

    console.log('Session found for user:', session.user.email)
    return `Bearer ${session.access_token}`
  } catch (err) {
    console.error('Error getting session:', err)
    throw new Error('Failed to get authentication session')
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ messageId: string }> }
) {
  try {
    const token = await getAdminToken(request)
    const { messageId } = await params
    const body = await request.json()

    const response = await fetch(`${BACKEND_URL}/support/admin/messages/${messageId}/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token,
      },
      body: JSON.stringify(body),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `Backend responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error updating support message status:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update support message status' },
      { status: 500 }
    )
  }
}
