# Design Document

## Overview

The Vercel Deployment System provides a comprehensive solution for deploying the WiggyZ backend to Vercel while managing environment configurations across all applications. The system includes automated deployment scripts, environment switching capabilities, and validation mechanisms to ensure smooth transitions between development and production environments.

## Architecture

### System Components

```mermaid
graph TB
    A[Developer] --> B[Deployment Scripts]
    B --> C[Vercel CLI]
    B --> D[Environment Manager]
    C --> E[Vercel Platform]
    D --> F[Flutter App Config]
    D --> G[Admin Dashboard Config]
    D --> H[Backend Config]
    E --> I[Production Backend]
    
    subgraph "Local Development"
        J[Local Backend]
        K[Local Flutter]
        L[Local Admin]
    end
    
    subgraph "Production Environment"
        I
        M[Flutter App - Prod]
        N[Admin Dashboard - Prod]
    end
```

### Deployment Flow

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant Script as Deployment Script
    participant Vercel as Vercel CLI
    participant Env as Environment Manager
    participant Apps as Applications
    
    Dev->>Script: Run deployment command
    Script->>Script: Validate environment
    Script->>Script: Build TypeScript backend
    Script->>Vercel: Deploy to Vercel
    Vercel-->>Script: Return deployment URL
    Script->>Env: Update environment configs
    Env->>Apps: Apply new configurations
    Script-->>Dev: Deployment complete
```

## Components and Interfaces

### 1. Deployment Manager

**Purpose**: Orchestrates the entire deployment process

**Key Methods**:
- `deployBackend()`: Main deployment function
- `validateEnvironment()`: Pre-deployment validation
- `buildProject()`: TypeScript compilation and preparation
- `updateConfigurations()`: Post-deployment config updates

**Configuration**:
```typescript
interface DeploymentConfig {
  projectName: string;
  buildCommand: string;
  outputDirectory: string;
  environmentVariables: Record<string, string>;
  vercelConfig: VercelConfig;
}
```

### 2. Environment Manager

**Purpose**: Manages environment-specific configurations across all applications

**Key Methods**:
- `switchEnvironment(mode: 'development' | 'production')`: Switch between environments
- `updateApiUrls(backendUrl: string)`: Update API endpoints
- `validateConfiguration()`: Ensure all configs are valid

**Configuration Structure**:
```typescript
interface EnvironmentConfig {
  mode: 'development' | 'production';
  backend: {
    url: string;
    port?: number;
  };
  frontend: {
    apiBaseUrl: string;
  };
  admin: {
    apiBaseUrl: string;
  };
}
```

### 3. Vercel Configuration Manager

**Purpose**: Handles Vercel-specific configuration and deployment settings

**Configuration Files**:
- `vercel.json`: Vercel deployment configuration
- `package.json`: Build scripts and dependencies
- Environment variable management

### 4. Application Configuration Updater

**Purpose**: Updates configuration files across Flutter and admin applications

**Target Files**:
- `frontend/lib/core/api/api_config.dart`: Flutter API configuration
- `wiggyz_admin/src/config/api.ts`: Admin dashboard API configuration
- Environment-specific configuration files

## Data Models

### Deployment Configuration

```typescript
interface DeploymentResult {
  success: boolean;
  deploymentUrl?: string;
  error?: string;
  timestamp: Date;
  environment: 'development' | 'production';
}

interface VercelConfig {
  name: string;
  version: 2;
  builds: BuildConfig[];
  routes: RouteConfig[];
  env: Record<string, string>;
  functions?: Record<string, FunctionConfig>;
}

interface BuildConfig {
  src: string;
  use: string;
  config?: Record<string, any>;
}
```

### Environment Configuration

```typescript
interface ApiConfiguration {
  baseUrl: string;
  endpoints: {
    auth: string;
    matches: string;
    tournaments: string;
    wallet: string;
  };
  timeout: number;
}

interface FlutterConfig {
  apiConfig: ApiConfiguration;
  developmentMode: boolean;
}

interface AdminConfig {
  apiBaseUrl: string;
  environment: string;
}
```

## Error Handling

### Deployment Errors

1. **Build Failures**: TypeScript compilation errors, missing dependencies
2. **Vercel Deployment Errors**: Authentication issues, quota limits, configuration errors
3. **Environment Variable Issues**: Missing or invalid environment variables
4. **Network Connectivity**: Connection timeouts, DNS resolution failures

### Error Recovery Strategies

- **Automatic Retry**: Retry failed deployments with exponential backoff
- **Rollback Mechanism**: Revert to previous deployment if current deployment fails
- **Configuration Validation**: Pre-deployment checks to prevent common issues
- **Detailed Logging**: Comprehensive error reporting and debugging information

### Error Response Format

```typescript
interface DeploymentError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  recoveryActions: string[];
}
```

## Testing Strategy

### Unit Tests

1. **Deployment Manager Tests**
   - Test deployment flow with mocked Vercel CLI
   - Validate environment variable handling
   - Test error scenarios and recovery

2. **Environment Manager Tests**
   - Test configuration switching
   - Validate file updates
   - Test configuration validation

3. **Configuration Parser Tests**
   - Test parsing of various configuration formats
   - Validate configuration merging
   - Test error handling for invalid configurations

### Integration Tests

1. **End-to-End Deployment Tests**
   - Deploy to staging environment
   - Validate all applications can connect to deployed backend
   - Test environment switching functionality

2. **Cross-Platform Configuration Tests**
   - Test Flutter app with production backend
   - Test admin dashboard with production backend
   - Validate API endpoint consistency

### Validation Tests

1. **Pre-Deployment Validation**
   - Environment variable completeness
   - Database connectivity
   - External service availability (Razorpay, Supabase)

2. **Post-Deployment Validation**
   - Health check endpoints
   - Authentication flow validation
   - Critical API endpoint testing

## Implementation Details

### File Structure

```
scripts/
├── deployment/
│   ├── deploy.js                 # Main deployment script
│   ├── environment-manager.js    # Environment configuration manager
│   ├── vercel-config.js         # Vercel configuration generator
│   └── validators.js            # Pre/post deployment validators
├── config/
│   ├── environments.json        # Environment definitions
│   ├── vercel.json             # Vercel deployment config
│   └── deployment.config.js    # Deployment settings
└── utils/
    ├── file-updater.js         # Configuration file updater
    ├── logger.js               # Deployment logging
    └── error-handler.js        # Error handling utilities
```

### Environment Configuration Files

1. **Global Environment Config** (`config/environments.json`):
```json
{
  "development": {
    "backend": {
      "url": "http://127.0.0.1:8080",
      "port": 8080
    },
    "frontend": {
      "apiBaseUrl": "http://127.0.0.1:8080/api/v1"
    },
    "admin": {
      "apiBaseUrl": "http://127.0.0.1:8080/api/v1"
    }
  },
  "production": {
    "backend": {
      "url": "https://wiggyz-backend.vercel.app"
    },
    "frontend": {
      "apiBaseUrl": "https://wiggyz-backend.vercel.app/api/v1"
    },
    "admin": {
      "apiBaseUrl": "https://wiggyz-backend.vercel.app/api/v1"
    }
  }
}
```

2. **Vercel Configuration** (`backend/vercel.json`):
```json
{
  "version": 2,
  "name": "wiggyz-backend",
  "builds": [
    {
      "src": "dist/server.js",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/dist/server.js"
    }
  ],
  "env": {
    "NODE_ENV": "production"
  }
}
```

### Command Line Interface

```bash
# Deploy backend to Vercel
npm run deploy

# Deploy with specific environment
npm run deploy -- --env production

# Switch environment mode
npm run env:switch -- --mode production

# Validate current configuration
npm run env:validate

# Rollback to previous deployment
npm run deploy:rollback
```

### Integration Points

1. **Flutter App Integration**
   - Update `frontend/lib/core/api/api_config.dart`
   - Modify `baseUrl` getter based on environment mode
   - Add environment detection logic

2. **Admin Dashboard Integration**
   - Update API configuration files
   - Modify build process to include environment variables
   - Add environment switching capabilities

3. **Backend Integration**
   - Ensure compatibility with Vercel serverless functions
   - Configure environment variables for production
   - Set up health check endpoints for monitoring