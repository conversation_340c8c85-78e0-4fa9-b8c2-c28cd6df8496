/**
 * Tournament Audit Service
 * Comprehensive audit logging for tournament operations
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { supabase as sb } from '../../../config/supabase';
import { logger } from '../../../utils/logger';

export interface TournamentAuditLogEntry {
  tournament_id: string;
  action_type: 'tournament_created' | 'tournament_updated' | 'tournament_deleted' | 
               'participant_joined' | 'participant_left' | 'participant_removed' |
               'result_submitted' | 'result_verified' | 'result_rejected' |
               'winner_assigned' | 'tournament_completed' | 'tournament_cancelled' |
               'entry_fee_processed' | 'prize_distributed' | 'admin_override';
  triggered_by: 'user' | 'admin' | 'system' | 'cron_job';
  user_id?: string;
  admin_user_id?: string;
  affected_participants?: string[];
  action_details: any;
  reason?: string;
  ip_address?: string;
  user_agent?: string;
  system_version?: string;
}

export class TournamentAuditService {
  private supabase: SupabaseClient;

  constructor(supabaseClient?: SupabaseClient) {
    this.supabase = supabaseClient || sb;
  }

  /**
   * Log a tournament audit entry
   */
  async logAction(entry: TournamentAuditLogEntry): Promise<void> {
    try {
      const auditEntry = {
        tournament_id: entry.tournament_id,
        action_type: entry.action_type,
        triggered_by: entry.triggered_by,
        user_id: entry.user_id,
        admin_user_id: entry.admin_user_id,
        affected_participants: entry.affected_participants || [],
        action_details: entry.action_details || {},
        reason: entry.reason,
        ip_address: entry.ip_address,
        user_agent: entry.user_agent,
        system_version: entry.system_version || '1.0',
        action_timestamp: new Date().toISOString()
      };

      const { error } = await this.supabase
        .from('tournament_audit_log')
        .insert(auditEntry);

      if (error) {
        throw new Error(`Failed to log tournament audit entry: ${error.message}`);
      }

      logger.info(`Tournament audit logged: ${entry.action_type} for tournament ${entry.tournament_id} by ${entry.triggered_by}`);

    } catch (error) {
      logger.error('Error logging tournament audit entry: ' + (error instanceof Error ? error.message : String(error)));
      // Don't throw here to avoid breaking the main operation
    }
  }

  /**
   * Log tournament creation
   */
  async logTournamentCreation(
    tournamentId: string,
    creatorId: string,
    tournamentData: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logAction({
      tournament_id: tournamentId,
      action_type: 'tournament_created',
      triggered_by: 'user',
      user_id: creatorId,
      action_details: {
        tournament_name: tournamentData.name,
        game_id: tournamentData.game_id,
        entry_fee: tournamentData.entry_fee,
        max_participants: tournamentData.max_participants,
        tournament_type: tournamentData.type,
        is_public: tournamentData.is_public
      },
      ip_address: ipAddress,
      user_agent: userAgent
    });
  }

  /**
   * Log participant joining
   */
  async logParticipantJoined(
    tournamentId: string,
    userId: string,
    participantType: string = 'participant',
    entryFeeAmount?: number,
    ipAddress?: string
  ): Promise<void> {
    await this.logAction({
      tournament_id: tournamentId,
      action_type: 'participant_joined',
      triggered_by: 'user',
      user_id: userId,
      action_details: {
        participant_type: participantType,
        entry_fee_paid: entryFeeAmount || 0,
        joined_at: new Date().toISOString()
      },
      ip_address: ipAddress
    });
  }

  /**
   * Log result submission
   */
  async logResultSubmission(
    tournamentId: string,
    userId: string,
    resultData: any,
    ipAddress?: string
  ): Promise<void> {
    await this.logAction({
      tournament_id: tournamentId,
      action_type: 'result_submitted',
      triggered_by: 'user',
      user_id: userId,
      action_details: {
        final_position: resultData.final_position,
        final_score: resultData.final_score,
        kills_count: resultData.kills_count,
        screenshot_count: resultData.screenshot_urls?.length || (resultData.screenshot_url ? 1 : 0),
        submitted_at: new Date().toISOString()
      },
      ip_address: ipAddress
    });
  }

  /**
   * Log result verification by admin
   */
  async logResultVerification(
    tournamentId: string,
    resultId: string,
    adminUserId: string,
    action: 'verified' | 'rejected',
    participantUserId: string,
    adminNotes?: string,
    ipAddress?: string
  ): Promise<void> {
    await this.logAction({
      tournament_id: tournamentId,
      action_type: action === 'verified' ? 'result_verified' : 'result_rejected',
      triggered_by: 'admin',
      admin_user_id: adminUserId,
      affected_participants: [participantUserId],
      action_details: {
        result_id: resultId,
        verification_action: action,
        admin_notes: adminNotes,
        verified_at: new Date().toISOString()
      },
      reason: adminNotes,
      ip_address: ipAddress
    });
  }

  /**
   * Log winner assignment
   */
  async logWinnerAssignment(
    tournamentId: string,
    adminUserId: string,
    winners: Array<{ user_id: string; position: number; prize_amount: number }>,
    ipAddress?: string
  ): Promise<void> {
    await this.logAction({
      tournament_id: tournamentId,
      action_type: 'winner_assigned',
      triggered_by: 'admin',
      admin_user_id: adminUserId,
      affected_participants: winners.map(w => w.user_id),
      action_details: {
        winners: winners,
        total_prize_distributed: winners.reduce((sum, w) => sum + w.prize_amount, 0),
        assigned_at: new Date().toISOString()
      },
      ip_address: ipAddress
    });
  }

  /**
   * Log tournament completion
   */
  async logTournamentCompletion(
    tournamentId: string,
    triggeredBy: 'admin' | 'system',
    adminUserId?: string,
    reason?: string
  ): Promise<void> {
    await this.logAction({
      tournament_id: tournamentId,
      action_type: 'tournament_completed',
      triggered_by: triggeredBy,
      admin_user_id: adminUserId,
      action_details: {
        completion_method: triggeredBy,
        completed_at: new Date().toISOString()
      },
      reason: reason
    });
  }

  /**
   * Log entry fee processing
   */
  async logEntryFeeProcessing(
    tournamentId: string,
    userId: string,
    amount: number,
    currency: string,
    transactionType: 'registration' | 'creation' | 'refund',
    transactionId?: string
  ): Promise<void> {
    await this.logAction({
      tournament_id: tournamentId,
      action_type: 'entry_fee_processed',
      triggered_by: 'system',
      user_id: userId,
      action_details: {
        amount: amount,
        currency: currency,
        transaction_type: transactionType,
        transaction_id: transactionId,
        processed_at: new Date().toISOString()
      }
    });
  }

  /**
   * Get recent audit logs for a tournament
   */
  async getTournamentAuditLogs(
    tournamentId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<TournamentAuditLogEntry[]> {
    try {
      const { data, error } = await this.supabase
        .from('tournament_audit_log')
        .select('*')
        .eq('tournament_id', tournamentId)
        .order('action_timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new Error(`Failed to get tournament audit logs: ${error.message}`);
      }

      return data || [];

    } catch (error) {
      logger.error('Error getting tournament audit logs: ' + (error instanceof Error ? error.message : String(error)));
      throw error;
    }
  }

  /**
   * Get recent audit logs across all tournaments
   */
  async getRecentAuditLogs(limit: number = 50, offset: number = 0): Promise<TournamentAuditLogEntry[]> {
    try {
      const { data, error } = await this.supabase
        .from('tournament_audit_log')
        .select('*')
        .order('action_timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new Error(`Failed to get recent tournament audit logs: ${error.message}`);
      }

      return data || [];

    } catch (error) {
      logger.error('Error getting recent tournament audit logs: ' + (error instanceof Error ? error.message : String(error)));
      throw error;
    }
  }
}

// Export singleton instance
export const tournamentAuditService = new TournamentAuditService();
