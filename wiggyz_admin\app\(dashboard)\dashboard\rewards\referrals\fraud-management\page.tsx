"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { AlertTriangle, CheckCircle, XCircle, Eye, Search, Filter, TrendingUp, Users, Shield, AlertCircle } from "lucide-react"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"

interface FlaggedReferral {
  id: string
  referrerId: string
  referredId: string
  fraudScore: number
  riskFactors: string[]
  status: string
  createdAt: string
  referrerName?: string
  referredName?: string
}

interface FraudAnalytics {
  totalReferrals: number
  flaggedReferrals: number
  approvedReferrals: number
  rejectedReferrals: number
  pendingReview: number
  avgFraudScore: number
  highRiskIps: number
  suspiciousDevices: number
}

export default function FraudManagementPage() {
  const [flaggedReferrals, setFlaggedReferrals] = useState<FlaggedReferral[]>([])
  const [analytics, setAnalytics] = useState<FraudAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedReferral, setSelectedReferral] = useState<FlaggedReferral | null>(null)
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false)
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve')
  const [reviewNotes, setReviewNotes] = useState("")
  const { toast } = useToast()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [analyticsResponse, flaggedResponse] = await Promise.all([
        fetch('/api/rewards/admin/referral-fraud-analytics'),
        fetch('/api/rewards/admin/flagged-referrals')
      ])

      if (analyticsResponse.ok) {
        const analyticsData = await analyticsResponse.json()
        setAnalytics(analyticsData.data)
      }

      if (flaggedResponse.ok) {
        const flaggedData = await flaggedResponse.json()
        setFlaggedReferrals(flaggedData.data)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load fraud management data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleReviewReferral = (referral: FlaggedReferral, action: 'approve' | 'reject') => {
    setSelectedReferral(referral)
    setReviewAction(action)
    setReviewNotes("")
    setReviewDialogOpen(true)
  }

  const submitReview = async () => {
    if (!selectedReferral) return

    try {
      const response = await fetch(`/api/rewards/admin/review-referral/${selectedReferral.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: reviewAction,
          notes: reviewNotes
        })
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: `Referral ${reviewAction}d successfully`,
        })
        setReviewDialogOpen(false)
        loadData() // Reload data
      } else {
        throw new Error('Failed to review referral')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to review referral",
        variant: "destructive",
      })
    }
  }

  const getRiskBadgeColor = (score: number) => {
    if (score >= 0.8) return "destructive"
    if (score >= 0.5) return "secondary"
    return "default"
  }

  const getRiskLabel = (score: number) => {
    if (score >= 0.8) return "High Risk"
    if (score >= 0.5) return "Medium Risk"
    return "Low Risk"
  }

  const filteredReferrals = flaggedReferrals.filter(referral =>
    referral.referrerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    referral.referredName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    referral.id.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading...</div>
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <Link href="/dashboard/rewards/referrals">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Back</span>
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Referral Fraud Management</h1>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                Pending Review
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.pendingReview}</div>
              <p className="text-xs text-muted-foreground">Require manual review</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Shield className="h-4 w-4 text-red-500" />
                Flagged Referrals
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.flaggedReferrals}</div>
              <p className="text-xs text-muted-foreground">High fraud score</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-blue-500" />
                Avg Fraud Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(analytics.avgFraudScore * 100).toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Across all referrals</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-yellow-500" />
                Suspicious Devices
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.suspiciousDevices}</div>
              <p className="text-xs text-muted-foreground">Flagged devices</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Flagged Referrals Table */}
      <Card>
        <CardHeader>
          <CardTitle>Flagged Referrals</CardTitle>
          <CardDescription>Referrals requiring manual review due to fraud detection</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex w-full items-center gap-2 sm:max-w-sm">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search referrals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="h-9"
              />
            </div>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Referral ID</TableHead>
                    <TableHead>Referrer</TableHead>
                    <TableHead>Referred User</TableHead>
                    <TableHead>Fraud Score</TableHead>
                    <TableHead>Risk Factors</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredReferrals.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        No flagged referrals found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredReferrals.map((referral) => (
                      <TableRow key={referral.id}>
                        <TableCell className="font-mono text-sm">
                          {referral.id.substring(0, 8)}...
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{referral.referrerName || 'Unknown'}</div>
                            <div className="text-sm text-muted-foreground font-mono">
                              {referral.referrerId.substring(0, 8)}...
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{referral.referredName || 'Unknown'}</div>
                            <div className="text-sm text-muted-foreground font-mono">
                              {referral.referredId.substring(0, 8)}...
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getRiskBadgeColor(referral.fraudScore)}>
                            {(referral.fraudScore * 100).toFixed(1)}% - {getRiskLabel(referral.fraudScore)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {referral.riskFactors.slice(0, 2).map((factor, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {factor.replace(/_/g, ' ')}
                              </Badge>
                            ))}
                            {referral.riskFactors.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{referral.riskFactors.length - 2} more
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={referral.status === 'pending' ? 'secondary' : 'default'}>
                            {referral.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(referral.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex gap-2 justify-end">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleReviewReferral(referral, 'approve')}
                              disabled={referral.status !== 'pending'}
                            >
                              <CheckCircle className="mr-1 h-3 w-3" />
                              Approve
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleReviewReferral(referral, 'reject')}
                              disabled={referral.status !== 'pending'}
                            >
                              <XCircle className="mr-1 h-3 w-3" />
                              Reject
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onOpenChange={setReviewDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {reviewAction === 'approve' ? 'Approve' : 'Reject'} Referral
            </DialogTitle>
            <DialogDescription>
              {reviewAction === 'approve' 
                ? 'This will approve the referral and award points to both users.'
                : 'This will reject the referral and no points will be awarded.'
              }
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {selectedReferral && (
              <div className="space-y-2">
                <div><strong>Referral ID:</strong> {selectedReferral.id}</div>
                <div><strong>Fraud Score:</strong> {(selectedReferral.fraudScore * 100).toFixed(1)}%</div>
                <div><strong>Risk Factors:</strong> {selectedReferral.riskFactors.join(', ')}</div>
              </div>
            )}
            <div className="space-y-2">
              <label htmlFor="notes" className="text-sm font-medium">
                Review Notes (Optional)
              </label>
              <Textarea
                id="notes"
                placeholder="Add any notes about this review decision..."
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setReviewDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={submitReview}
              variant={reviewAction === 'approve' ? 'default' : 'destructive'}
            >
              {reviewAction === 'approve' ? 'Approve Referral' : 'Reject Referral'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
