{"timestamp": "2025-07-31T07:34:05.026Z", "logs": [{"timestamp": "2025-07-31T07:33:19.915Z", "message": "Starting deployment to staging...", "type": "info"}, {"timestamp": "2025-07-31T07:33:19.918Z", "message": "Validating deployment prerequisites...", "type": "info"}, {"timestamp": "2025-07-31T07:33:22.442Z", "message": "Vercel CLI is installed", "type": "success"}, {"timestamp": "2025-07-31T07:33:22.443Z", "message": "All prerequisites validated", "type": "success"}, {"timestamp": "2025-07-31T07:33:22.444Z", "message": "Building TypeScript backend...", "type": "info"}, {"timestamp": "2025-07-31T07:33:55.181Z", "message": "Backend build completed successfully", "type": "success"}, {"timestamp": "2025-07-31T07:33:55.185Z", "message": "Deploying to Vercel (staging)...", "type": "info"}, {"timestamp": "2025-07-31T07:33:55.191Z", "message": "Running: vercel --yes", "type": "info"}, {"timestamp": "2025-07-31T07:34:05.023Z", "message": "Vercel deployment failed: Command failed: vercel --yes\nVercel CLI 44.6.5\nLoading scopes…\nSearching for existing projects…\nLinked to tausifraja977-gmailcoms-projects/wiggyz-backend (created .vercel and added it to .gitignore)\nThe `name` property in vercel.json is deprecated (https://vercel.link/name-prop)\nDeploying tausifraja977-gmailcoms-projects/wiggyz-backend\nError: The `functions` property cannot be used in conjunction with the `builds` property. Please remove one of them.\nLearn More: https://vercel.link/functions-and-builds\n", "type": "error"}, {"timestamp": "2025-07-31T07:34:05.024Z", "message": "Deployment failed: Command failed: vercel --yes\nVercel CLI 44.6.5\nLoading scopes…\nSearching for existing projects…\nLinked to tausifraja977-gmailcoms-projects/wiggyz-backend (created .vercel and added it to .gitignore)\nThe `name` property in vercel.json is deprecated (https://vercel.link/name-prop)\nDeploying tausifraja977-gmailcoms-projects/wiggyz-backend\nError: The `functions` property cannot be used in conjunction with the `builds` property. Please remove one of them.\nLearn More: https://vercel.link/functions-and-builds\n", "type": "error"}]}