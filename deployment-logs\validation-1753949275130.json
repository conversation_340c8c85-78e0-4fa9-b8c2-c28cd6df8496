{"timestamp": "2025-07-31T08:07:55.131Z", "results": [{"timestamp": "2025-07-31T08:07:54.673Z", "message": "Loaded environment variables from backend/.env", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:54.686Z", "message": "🔍 Running post-deployment validation...", "type": "info", "category": "validation"}, {"timestamp": "2025-07-31T08:07:54.686Z", "message": "Validating deployed endpoint: https://wiggyz-backend-cas6rsy6m-tausifraja977-gmailcoms-projects.vercel.app", "type": "info", "category": "deployment"}, {"timestamp": "2025-07-31T08:07:55.129Z", "message": "Health endpoint failed: undefined", "type": "error", "category": "deployment"}, {"timestamp": "2025-07-31T08:07:55.130Z", "message": "❌ Post-deployment validation failed", "type": "error", "category": "validation"}]}