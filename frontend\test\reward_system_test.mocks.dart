// Mocks generated by Mockito 5.4.4 from annotations
// in wiggyz_app/test/reward_system_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:wiggyz_app/models/achievement_models.dart' as _i6;
import 'package:wiggyz_app/models/daily_reward_models.dart' as _i5;
import 'package:wiggyz_app/models/loyalty_models.dart' as _i7;
import 'package:wiggyz_app/models/reward_models.dart' as _i4;
import 'package:wiggyz_app/services/auth_service.dart' as _i8;
import 'package:wiggyz_app/services/reward_service.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [RewardService].
///
/// See the documentation for Mockito's code generation for more information.
class MockRewardService extends _i1.Mock implements _i2.RewardService {
  MockRewardService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i5.DailyRewardStatusModel?> getDailyRewardStatus() =>
      (super.noSuchMethod(
        Invocation.method(
          #getDailyRewardStatus,
          [],
        ),
        returnValue: _i3.Future<_i5.DailyRewardStatusModel?>.value(),
      ) as _i3.Future<_i5.DailyRewardStatusModel?>);

  @override
  _i3.Future<_i5.DailyRewardClaimResult> claimDailyReward({String? idempotencyKey}) =>
      (super.noSuchMethod(
        Invocation.method(
          #claimDailyReward,
          [],
          {#idempotencyKey: idempotencyKey},
        ),
        returnValue: _i3.Future<_i5.DailyRewardClaimResult>.value(_i5.DailyRewardClaimResult(success: false, message: 'Mock')),
      ) as _i3.Future<_i5.DailyRewardClaimResult>);

  @override
  _i3.Future<List<_i4.UserRewardModel>> getUserRewards() => (super.noSuchMethod(
        Invocation.method(
          #getUserRewards,
          [],
        ),
        returnValue: _i3.Future<List<_i4.UserRewardModel>>.value(<_i4.UserRewardModel>[]),
      ) as _i3.Future<List<_i4.UserRewardModel>>);

  @override
  _i3.Future<List<_i6.AchievementModel>> getUserAchievements() =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserAchievements,
          [],
        ),
        returnValue: _i3.Future<List<_i6.AchievementModel>>.value(<_i6.AchievementModel>[]),
      ) as _i3.Future<List<_i6.AchievementModel>>);

  @override
  _i3.Future<_i7.UserLoyaltyModel?> getUserLoyaltyStatus() =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserLoyaltyStatus,
          [],
        ),
        returnValue: _i3.Future<_i7.UserLoyaltyModel?>.value(),
      ) as _i3.Future<_i7.UserLoyaltyModel?>);

  @override
  _i3.Future<List<_i7.LoyaltyTierModel>> getLoyaltyTiers() =>
      (super.noSuchMethod(
        Invocation.method(
          #getLoyaltyTiers,
          [],
        ),
        returnValue: _i3.Future<List<_i7.LoyaltyTierModel>>.value(<_i7.LoyaltyTierModel>[]),
      ) as _i3.Future<List<_i7.LoyaltyTierModel>>);
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i8.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String? get currentUserId => (super.noSuchMethod(Invocation.getter(#currentUserId)) as String?);

  @override
  bool get isAuthenticated => (super.noSuchMethod(
        Invocation.getter(#isAuthenticated),
        returnValue: false,
      ) as bool);

  @override
  _i3.Future<Map<String, String>> getAuthHeaders() => (super.noSuchMethod(
        Invocation.method(
          #getAuthHeaders,
          [],
        ),
        returnValue: _i3.Future<Map<String, String>>.value(<String, String>{}),
      ) as _i3.Future<Map<String, String>>);
}
