/**
 * Tests for Loyalty System Integration
 * Verifies that daily rewards properly integrate with loyalty points
 */

import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../config/supabase');
jest.mock('../../../utils/supabaseHelpers');
jest.mock('../services/rewardsService');
jest.mock('../../wallet/services/walletService');

const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  lte: jest.fn().mockReturnThis(),
  gt: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  rpc: jest.fn(),
};

describe('Loyalty System Integration', () => {
  let loyaltyService: any;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock the supabase import
    jest.doMock('../../../config/supabase', () => ({
      supabase: mockSupabase
    }));
    
    const { loyaltyService: service } = require('../services/loyaltyService');
    loyaltyService = service;
  });

  describe('awardLoyaltyPoints', () => {
    it('should award points and maintain current tier', async () => {
      // Mock existing user loyalty
      mockSupabase.single.mockResolvedValueOnce({
        data: {
          id: 'loyalty1',
          points: 50,
          tier_id: 'bronze_tier'
        },
        error: null
      });

      // Mock tier lookup (user stays in bronze)
      mockSupabase.single.mockResolvedValueOnce({
        data: {
          id: 'bronze_tier',
          name: 'Bronze',
          min_points: 0
        },
        error: null
      });

      // Mock successful updates
      mockSupabase.update.mockResolvedValue({ error: null });
      mockSupabase.insert.mockResolvedValue({ error: null });

      const result = await loyaltyService.awardLoyaltyPoints(
        'user123',
        25,
        'daily_login',
        'Daily login reward',
        'test-key'
      );

      expect(result.points).toBe(75); // 50 + 25
      expect(result.tier_changed).toBe(false);
    });

    it('should award points and upgrade tier', async () => {
      // Mock existing user loyalty
      mockSupabase.single
        .mockResolvedValueOnce({
          data: {
            id: 'loyalty1',
            points: 80,
            tier_id: 'bronze_tier'
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            id: 'silver_tier',
            name: 'Silver',
            min_points: 100
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            name: 'Silver',
            benefits: {
              tier_up_reward: 100
            }
          },
          error: null
        });

      // Mock successful updates
      mockSupabase.update.mockResolvedValue({ error: null });
      mockSupabase.insert.mockResolvedValue({ error: null });

      // Mock wallet service
      const walletService = require('../../wallet/services/walletService').walletService;
      walletService.addDiamonds = jest.fn().mockResolvedValue({});

      const result = await loyaltyService.awardLoyaltyPoints(
        'user123',
        25,
        'daily_login',
        'Daily login reward',
        'test-key'
      );

      expect(result.points).toBe(105); // 80 + 25
      expect(result.tier_changed).toBe(true);
      expect(walletService.addDiamonds).toHaveBeenCalledWith(
        'user123',
        100,
        'loyalty_tier_up',
        'Tier upgrade to Silver',
        'test-key_tier_reward'
      );
    });

    it('should handle idempotency correctly', async () => {
      // Mock existing transaction with same idempotency key
      mockSupabase.single.mockResolvedValueOnce({
        data: { id: 'existing_tx' },
        error: null
      });

      const result = await loyaltyService.awardLoyaltyPoints(
        'user123',
        25,
        'daily_login',
        'Daily login reward',
        'duplicate-key'
      );

      expect(result.message).toContain('already processed');
      expect(result.points).toBe(0);
    });

    it('should create new loyalty record for new user', async () => {
      // Mock no existing loyalty record
      mockSupabase.single
        .mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116' }
        })
        .mockResolvedValueOnce({
          data: {
            id: 'bronze_tier',
            name: 'Bronze',
            min_points: 0
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            id: 'new_loyalty',
            points: 0,
            tier_id: 'bronze_tier'
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            id: 'bronze_tier',
            name: 'Bronze',
            min_points: 0
          },
          error: null
        });

      // Mock successful operations
      mockSupabase.insert.mockResolvedValue({ error: null });
      mockSupabase.update.mockResolvedValue({ error: null });

      const result = await loyaltyService.awardLoyaltyPoints(
        'new_user',
        50,
        'daily_login',
        'First daily login',
        'test-key'
      );

      expect(result.points).toBe(50);
      expect(result.tier_changed).toBe(false);
    });
  });

  describe('getLoyaltyStatus', () => {
    it('should return correct status with next tier info', async () => {
      // Mock user loyalty data
      mockSupabase.single
        .mockResolvedValueOnce({
          data: {
            points: 75,
            loyalty_tiers: {
              id: 'bronze_tier',
              name: 'Bronze',
              min_points: 0,
              benefits: { daily_bonus: 5 }
            }
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            id: 'silver_tier',
            name: 'Silver',
            min_points: 100,
            benefits: { daily_bonus: 10 }
          },
          error: null
        });

      const result = await loyaltyService.getLoyaltyStatus('user123');

      expect(result.points).toBe(75);
      expect(result.tier.name).toBe('Bronze');
      expect(result.next_tier.name).toBe('Silver');
      expect(result.points_to_next_tier).toBe(25); // 100 - 75
    });

    it('should handle max tier user correctly', async () => {
      // Mock user at max tier
      mockSupabase.single
        .mockResolvedValueOnce({
          data: {
            points: 2000,
            loyalty_tiers: {
              id: 'platinum_tier',
              name: 'Platinum',
              min_points: 1000,
              benefits: { daily_bonus: 25 }
            }
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116' } // No higher tier found
        });

      const result = await loyaltyService.getLoyaltyStatus('user123');

      expect(result.points).toBe(2000);
      expect(result.tier.name).toBe('Platinum');
      expect(result.next_tier).toBe(null);
      expect(result.points_to_next_tier).toBe(null);
    });

    it('should create default loyalty record for new user', async () => {
      // Mock no existing loyalty record
      mockSupabase.single
        .mockResolvedValueOnce({
          data: null,
          error: { code: 'PGRST116' }
        })
        .mockResolvedValueOnce({
          data: {
            id: 'bronze_tier',
            name: 'Bronze',
            min_points: 0,
            benefits: { daily_bonus: 5 }
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            id: 'new_loyalty',
            points: 0,
            loyalty_tiers: {
              id: 'bronze_tier',
              name: 'Bronze',
              min_points: 0,
              benefits: { daily_bonus: 5 }
            }
          },
          error: null
        });

      // Mock successful insert
      mockSupabase.insert.mockResolvedValue({ error: null });

      const result = await loyaltyService.getLoyaltyStatus('new_user');

      expect(result.points).toBe(0);
      expect(result.tier.name).toBe('Bronze');
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock database error
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Database connection failed' }
      });

      await expect(
        loyaltyService.awardLoyaltyPoints('user123', 25, 'daily_login', 'Test', 'key')
      ).rejects.toThrow('Error fetching loyalty record: Database connection failed');
    });

    it('should handle tier lookup errors', async () => {
      // Mock successful loyalty fetch but failed tier lookup
      mockSupabase.single
        .mockResolvedValueOnce({
          data: {
            id: 'loyalty1',
            points: 50,
            tier_id: 'bronze_tier'
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: null,
          error: { message: 'Tier not found' }
        });

      await expect(
        loyaltyService.awardLoyaltyPoints('user123', 25, 'daily_login', 'Test', 'key')
      ).rejects.toThrow('Error finding eligible tier: Tier not found');
    });
  });
});
