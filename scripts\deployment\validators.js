#!/usr/bin/env node

/**
 * Pre and Post Deployment Validators for WiggyZ Backend
 * Validates environment variables, database connectivity, and external services
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

class DeploymentValidator {
  constructor() {
    this.rootDir = path.resolve(__dirname, '../..');
    this.backendDir = path.join(this.rootDir, 'backend');
    this.validationResults = [];

    // Load environment variables from backend/.env
    this.loadEnvironmentVariables();
  }

  loadEnvironmentVariables() {
    const envPath = path.join(this.backendDir, '.env');
    if (fs.existsSync(envPath)) {
      const envFile = fs.readFileSync(envPath, 'utf8');
      envFile.split('\n').forEach(line => {
        if (line.trim() && !line.startsWith('#')) {
          const [key, ...valueParts] = line.split('=');
          if (key && valueParts.length > 0) {
            process.env[key.trim()] = valueParts.join('=').trim();
          }
        }
      });
      this.log('Loaded environment variables from backend/.env', 'success', 'env');
    } else {
      this.log('No .env file found in backend directory', 'warn', 'env');
    }
  }

  log(message, type = 'info', category = 'general') {
    const result = {
      timestamp: new Date().toISOString(),
      message,
      type,
      category
    };
    this.validationResults.push(result);
    
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} [${category.toUpperCase()}] ${message}`);
  }

  validateEnvironmentVariables() {
    this.log('Validating environment variables...', 'info', 'env');
    
    const requiredEnvVars = [
      'SUPABASE_URL',
      'SUPABASE_KEY',
      'JWT_REFRESH_SECRET',
      'RAZORPAY_KEY_ID',
      'RAZORPAY_KEY_SECRET'
    ];
    
    const optionalEnvVars = [
      'JWT_ACCESS_EXPIRY',
      'JWT_REFRESH_EXPIRY',
      'PAYMENT_TIMEOUT_MINUTES',
      'DISABLE_FRAUD_DETECTION'
    ];
    
    let missingRequired = [];
    let missingOptional = [];
    
    // Check required variables
    requiredEnvVars.forEach(varName => {
      if (!process.env[varName]) {
        missingRequired.push(varName);
        this.log(`Missing required environment variable: ${varName}`, 'error', 'env');
      } else {
        this.log(`Found required variable: ${varName}`, 'success', 'env');
      }
    });
    
    // Check optional variables
    optionalEnvVars.forEach(varName => {
      if (!process.env[varName]) {
        missingOptional.push(varName);
        this.log(`Missing optional environment variable: ${varName} (using default)`, 'warn', 'env');
      } else {
        this.log(`Found optional variable: ${varName}`, 'success', 'env');
      }
    });
    
    if (missingRequired.length > 0) {
      this.log(`❌ Missing ${missingRequired.length} required environment variables`, 'error', 'env');
      return false;
    }
    
    this.log('✅ All required environment variables are present', 'success', 'env');
    return true;
  }

  async validateSupabaseConnection() {
    this.log('Validating Supabase connection...', 'info', 'database');
    
    if (!process.env.SUPABASE_URL || !process.env.SUPABASE_KEY) {
      this.log('Supabase credentials not found', 'error', 'database');
      return false;
    }
    
    try {
      // Test Supabase connection by making a simple request
      const url = `${process.env.SUPABASE_URL}/rest/v1/`;
      const headers = {
        'apikey': process.env.SUPABASE_KEY,
        'Authorization': `Bearer ${process.env.SUPABASE_KEY}`
      };
      
      const result = await this.makeHttpRequest(url, { headers });
      
      if (result.success) {
        this.log('Supabase connection successful', 'success', 'database');
        return true;
      } else {
        this.log(`Supabase connection failed: ${result.error}`, 'error', 'database');
        return false;
      }
    } catch (error) {
      this.log(`Supabase validation error: ${error.message}`, 'error', 'database');
      return false;
    }
  }

  async validateRazorpayCredentials() {
    this.log('Validating Razorpay credentials...', 'info', 'payment');
    
    if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
      this.log('Razorpay credentials not found', 'error', 'payment');
      return false;
    }
    
    // Basic validation - check if credentials are not placeholder values
    const keyId = process.env.RAZORPAY_KEY_ID;
    const keySecret = process.env.RAZORPAY_KEY_SECRET;
    
    if (keyId.includes('your_') || keySecret.includes('your_')) {
      this.log('Razorpay credentials appear to be placeholder values', 'error', 'payment');
      return false;
    }
    
    if (!keyId.startsWith('rzp_')) {
      this.log('Razorpay Key ID format appears invalid (should start with rzp_)', 'warn', 'payment');
    }
    
    this.log('Razorpay credentials format validation passed', 'success', 'payment');
    return true;
  }

  validateBuildOutput() {
    this.log('Validating build output...', 'info', 'build');
    
    const distPath = path.join(this.backendDir, 'dist');
    const serverPath = path.join(distPath, 'server.js');
    
    if (!fs.existsSync(distPath)) {
      this.log('Build output directory not found', 'error', 'build');
      return false;
    }
    
    if (!fs.existsSync(serverPath)) {
      this.log('Server build output not found', 'error', 'build');
      return false;
    }
    
    // Check if build is recent (within last hour)
    const stats = fs.statSync(serverPath);
    const buildAge = Date.now() - stats.mtime.getTime();
    const oneHour = 60 * 60 * 1000;
    
    if (buildAge > oneHour) {
      this.log('Build output is older than 1 hour, consider rebuilding', 'warn', 'build');
    } else {
      this.log('Build output is recent', 'success', 'build');
    }
    
    this.log('Build output validation passed', 'success', 'build');
    return true;
  }

  async validateDeployedEndpoint(url) {
    this.log(`Validating deployed endpoint: ${url}`, 'info', 'deployment');
    
    try {
      // Test health endpoint
      const healthUrl = `${url}/health`;
      const healthResult = await this.makeHttpRequest(healthUrl);
      
      if (healthResult.success) {
        this.log('Health endpoint is responding', 'success', 'deployment');
      } else {
        this.log(`Health endpoint failed: ${healthResult.error}`, 'error', 'deployment');
        return false;
      }
      
      // Test API endpoint
      const apiUrl = `${url}/api/v1/auth/verify-token`;
      const apiResult = await this.makeHttpRequest(apiUrl);
      
      // We expect this to fail with 401 (no token), but it should respond
      if (apiResult.statusCode === 401 || apiResult.statusCode === 400) {
        this.log('API endpoint is responding correctly', 'success', 'deployment');
      } else if (apiResult.success) {
        this.log('API endpoint is responding', 'success', 'deployment');
      } else {
        this.log(`API endpoint test failed: ${apiResult.error}`, 'warn', 'deployment');
      }
      
      return true;
    } catch (error) {
      this.log(`Endpoint validation error: ${error.message}`, 'error', 'deployment');
      return false;
    }
  }

  makeHttpRequest(url, options = {}) {
    return new Promise((resolve) => {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: options.headers || {},
        timeout: 10000
      };
      
      const req = client.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            success: res.statusCode >= 200 && res.statusCode < 400,
            statusCode: res.statusCode,
            data: data
          });
        });
      });
      
      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message
        });
      });
      
      req.on('timeout', () => {
        req.destroy();
        resolve({
          success: false,
          error: 'Request timeout'
        });
      });
      
      req.end();
    });
  }

  async runPreDeploymentValidation() {
    this.log('🔍 Running pre-deployment validation...', 'info', 'validation');
    
    const results = {
      envVars: this.validateEnvironmentVariables(),
      supabase: await this.validateSupabaseConnection(),
      razorpay: await this.validateRazorpayCredentials(),
      build: this.validateBuildOutput()
    };
    
    const allPassed = Object.values(results).every(result => result === true);
    
    if (allPassed) {
      this.log('✅ All pre-deployment validations passed', 'success', 'validation');
    } else {
      this.log('❌ Some pre-deployment validations failed', 'error', 'validation');
    }
    
    return { success: allPassed, results };
  }

  async runPostDeploymentValidation(deploymentUrl) {
    this.log('🔍 Running post-deployment validation...', 'info', 'validation');
    
    if (!deploymentUrl) {
      this.log('No deployment URL provided for validation', 'error', 'validation');
      return { success: false };
    }
    
    const endpointValid = await this.validateDeployedEndpoint(deploymentUrl);
    
    if (endpointValid) {
      this.log('✅ Post-deployment validation passed', 'success', 'validation');
    } else {
      this.log('❌ Post-deployment validation failed', 'error', 'validation');
    }
    
    return { success: endpointValid };
  }

  saveValidationReport() {
    const reportPath = path.join(this.rootDir, 'deployment-logs', `validation-${Date.now()}.json`);
    const reportDir = path.dirname(reportPath);
    
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      results: this.validationResults
    }, null, 2));
    
    this.log(`Validation report saved to: ${reportPath}`, 'info', 'validation');
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const validator = new DeploymentValidator();
  
  const command = args[0] || 'pre';
  const url = args[1];
  
  console.log('🔍 WiggyZ Deployment Validator');
  
  if (command === 'pre') {
    validator.runPreDeploymentValidation()
      .then(result => {
        validator.saveValidationReport();
        process.exit(result.success ? 0 : 1);
      })
      .catch(error => {
        console.error('Validation error:', error.message);
        process.exit(1);
      });
  } else if (command === 'post') {
    if (!url) {
      console.error('❌ Please provide deployment URL for post-deployment validation');
      process.exit(1);
    }
    
    validator.runPostDeploymentValidation(url)
      .then(result => {
        validator.saveValidationReport();
        process.exit(result.success ? 0 : 1);
      })
      .catch(error => {
        console.error('Validation error:', error.message);
        process.exit(1);
      });
  } else {
    console.log('Usage:');
    console.log('  node validators.js pre                    # Run pre-deployment validation');
    console.log('  node validators.js post <deployment-url> # Run post-deployment validation');
  }
}

module.exports = DeploymentValidator;
