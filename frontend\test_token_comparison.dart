import 'dart:convert';
import 'package:http/http.dart' as http;

/// Test to compare tokens between working and failing requests
void main() async {
  print('🧪 Testing Token Comparison...');
  
  // Test configuration
  const apiBaseUrl = 'http://127.0.0.1:5000/api/v1';
  const statusEndpoint = '$apiBaseUrl/rewards/daily-status';
  const claimEndpoint = '$apiBaseUrl/rewards/claim-daily';
  
  // You'll need to get a real token from the Flutter app
  // This is just a placeholder - replace with actual token
  const testToken = 'REPLACE_WITH_REAL_TOKEN_FROM_FLUTTER_APP';
  
  if (testToken == 'REPLACE_WITH_REAL_TOKEN_FROM_FLUTTER_APP') {
    print('❌ Please replace testToken with a real token from the Flutter app');
    print('ℹ️ You can get this from the Flutter app logs when it makes a successful daily-status request');
    return;
  }
  
  print('🔑 Using token: ${testToken.substring(0, 50)}...');
  
  // Test 1: Daily Status (should work)
  print('\n📡 Test 1: Daily Status Request (should work)...');
  try {
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $testToken',
    };
    
    print('🔑 Headers: $headers');
    
    final response = await http.get(
      Uri.parse(statusEndpoint),
      headers: headers,
    );
    
    print('✅ Status request completed!');
    print('📊 Status Code: ${response.statusCode}');
    print('📄 Response Body Length: ${response.body.length}');
    
    if (response.statusCode == 200) {
      print('✅ Daily status request successful');
    } else {
      print('❌ Daily status request failed: ${response.statusCode}');
      print('📄 Response: ${response.body}');
    }
    
  } catch (e) {
    print('❌ Daily status error: $e');
  }
  
  // Test 2: Claim Request (fails)
  print('\n📡 Test 2: Claim Request (currently fails)...');
  try {
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $testToken',
      'X-Idempotency-Key': 'test-${DateTime.now().millisecondsSinceEpoch}',
    };
    
    print('🔑 Headers: $headers');
    
    final response = await http.post(
      Uri.parse(claimEndpoint),
      headers: headers,
    );
    
    print('✅ Claim request completed!');
    print('📊 Status Code: ${response.statusCode}');
    print('📄 Response Body: ${response.body}');
    
    if (response.statusCode == 200) {
      print('✅ Claim request successful');
    } else if (response.statusCode == 401) {
      print('❌ Claim request failed with authentication error');
      print('🔍 This confirms the token authentication issue');
    } else {
      print('❌ Claim request failed: ${response.statusCode}');
    }
    
  } catch (e) {
    print('❌ Claim request error: $e');
  }
  
  // Test 3: Token validation
  print('\n🔍 Test 3: Token Validation...');
  try {
    // Basic JWT structure validation
    final parts = testToken.split('.');
    print('📊 JWT parts count: ${parts.length} (should be 3)');
    
    if (parts.length == 3) {
      print('✅ Token has correct JWT structure');
      
      // Try to decode header and payload (without verification)
      try {
        final header = utf8.decode(base64Url.decode(base64Url.normalize(parts[0])));
        final payload = utf8.decode(base64Url.decode(base64Url.normalize(parts[1])));
        
        print('📋 Header: $header');
        print('📋 Payload: $payload');
        
        final payloadJson = jsonDecode(payload);
        final exp = payloadJson['exp'];
        if (exp != null) {
          final expirationDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
          final now = DateTime.now();
          final isExpired = now.isAfter(expirationDate);
          
          print('⏰ Token expires at: $expirationDate');
          print('⏰ Current time: $now');
          print('⏰ Is expired: $isExpired');
          
          if (isExpired) {
            print('❌ Token is expired - this could be the issue!');
          } else {
            print('✅ Token is not expired');
          }
        }
        
      } catch (decodeError) {
        print('❌ Error decoding token: $decodeError');
      }
      
    } else {
      print('❌ Token does not have correct JWT structure');
    }
    
  } catch (e) {
    print('❌ Token validation error: $e');
  }
  
  print('\n🏁 Token comparison test completed!');
  print('ℹ️ If both requests use the same token but only claim fails,');
  print('   the issue is likely in the backend route or middleware specific to POST requests.');
}
