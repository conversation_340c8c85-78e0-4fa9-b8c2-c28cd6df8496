/// Comprehensive reward system models for the WiggyZ app
/// These models match the backend API structure for rewards, achievements, and loyalty

import 'package:flutter/foundation.dart';

/// Enum for different reward types
enum RewardType {
  dailyLogin,
  achievement,
  loyaltyBonus,
  referral,
  tournamentPrize,
  specialOffer,
  manualAdjustment,
}

/// Extension to convert reward type to string and vice versa
extension RewardTypeExtension on RewardType {
  String get value {
    switch (this) {
      case RewardType.dailyLogin:
        return 'daily_login';
      case RewardType.achievement:
        return 'achievement';
      case RewardType.loyaltyBonus:
        return 'loyalty_bonus';
      case RewardType.referral:
        return 'referral';
      case RewardType.tournamentPrize:
        return 'tournament_prize';
      case RewardType.specialOffer:
        return 'special_offer';
      case RewardType.manualAdjustment:
        return 'manual_adjustment';
    }
  }

  static RewardType fromString(String value) {
    switch (value) {
      case 'daily_login':
        return RewardType.dailyLogin;
      case 'achievement':
        return RewardType.achievement;
      case 'loyalty_bonus':
        return RewardType.loyaltyBonus;
      case 'referral':
        return RewardType.referral;
      case 'tournament_prize':
        return RewardType.tournamentPrize;
      case 'special_offer':
        return RewardType.specialOffer;
      case 'manual_adjustment':
        return RewardType.manualAdjustment;
      default:
        return RewardType.dailyLogin;
    }
  }
}

/// Model for reward type metadata
class RewardTypeModel {
  final String id;
  final String name;
  final String? description;
  final String? icon;
  final DateTime createdAt;

  const RewardTypeModel({
    required this.id,
    required this.name,
    this.description,
    this.icon,
    required this.createdAt,
  });

  factory RewardTypeModel.fromJson(Map<String, dynamic> json) {
    try {
      return RewardTypeModel(
        id: json['id']?.toString() ?? '',
        name: json['name']?.toString() ?? 'Unknown Type',
        description: json['description']?.toString(),
        icon: json['icon']?.toString(),
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString()) ?? DateTime.now()
            : DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error parsing RewardTypeModel from JSON: $e');
      return RewardTypeModel(
        id: 'unknown',
        name: 'Unknown Type',
        description: null,
        icon: null,
        createdAt: DateTime.now(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// Model for individual rewards
class RewardModel {
  final String id;
  final String typeId;
  final String title;
  final String? description;
  final int points;
  final int diamondValue;
  final Map<String, dynamic>? requirements;
  final bool isActive;
  final DateTime? startDate;
  final DateTime? endDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final RewardTypeModel? rewardType;

  const RewardModel({
    required this.id,
    required this.typeId,
    required this.title,
    this.description,
    required this.points,
    required this.diamondValue,
    this.requirements,
    required this.isActive,
    this.startDate,
    this.endDate,
    required this.createdAt,
    required this.updatedAt,
    this.rewardType,
  });

  factory RewardModel.fromJson(Map<String, dynamic> json) {
    try {
      return RewardModel(
        id: json['id']?.toString() ?? 'unknown-${DateTime.now().millisecondsSinceEpoch}',
        typeId: json['type_id']?.toString() ?? 'unknown-type',
        title: json['title']?.toString() ?? 'Unknown Reward',
        description: json['description']?.toString(),
        points: (json['points'] as num?)?.toInt() ?? 0,
        diamondValue: (json['diamond_value'] as num?)?.toInt() ?? 0,
        requirements: json['requirements'] as Map<String, dynamic>?,
        isActive: json['is_active'] as bool? ?? true,
        startDate: json['start_date'] != null
            ? DateTime.tryParse(json['start_date'].toString())
            : null,
        endDate: json['end_date'] != null
            ? DateTime.tryParse(json['end_date'].toString())
            : null,
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString()) ?? DateTime.now()
            : DateTime.now(),
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'].toString()) ?? DateTime.now()
            : DateTime.now(),
        rewardType: json['reward_types'] != null
            ? RewardTypeModel.fromJson(json['reward_types'] as Map<String, dynamic>)
            : null,
      );
    } catch (e) {
      debugPrint('Error parsing RewardModel from JSON: $e');
      debugPrint('JSON data: $json');
      // Return a default reward model to prevent crashes
      return RewardModel(
        id: json['id']?.toString() ?? 'unknown',
        typeId: json['type_id']?.toString() ?? 'unknown',
        title: 'Error Loading Reward',
        description: 'Failed to load reward data',
        points: 0,
        diamondValue: 0,
        requirements: null,
        isActive: false,
        startDate: null,
        endDate: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        rewardType: null,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type_id': typeId,
      'title': title,
      'description': description,
      'points': points,
      'diamond_value': diamondValue,
      'requirements': requirements,
      'is_active': isActive,
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'reward_types': rewardType?.toJson(),
    };
  }

  /// Get the day requirement for daily rewards
  int? get dayRequirement {
    return requirements?['day'] as int?;
  }

  /// Check if this reward is currently available
  bool get isAvailable {
    if (!isActive) return false;
    
    final now = DateTime.now();
    if (startDate != null && now.isBefore(startDate!)) return false;
    if (endDate != null && now.isAfter(endDate!)) return false;
    
    return true;
  }
}

/// Model for user claimed rewards
class UserRewardModel {
  final String id;
  final String userId;
  final String rewardId;
  final DateTime claimedAt;
  final DateTime? expiryDate;
  final RewardModel? reward;

  const UserRewardModel({
    required this.id,
    required this.userId,
    required this.rewardId,
    required this.claimedAt,
    this.expiryDate,
    this.reward,
  });

  factory UserRewardModel.fromJson(Map<String, dynamic> json) {
    return UserRewardModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      rewardId: json['reward_id'] as String,
      claimedAt: DateTime.parse(json['claimed_at'] as String),
      expiryDate: json['expiry_date'] != null 
          ? DateTime.parse(json['expiry_date'] as String) 
          : null,
      reward: json['rewards'] != null 
          ? RewardModel.fromJson(json['rewards'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'reward_id': rewardId,
      'claimed_at': claimedAt.toIso8601String(),
      'expiry_date': expiryDate?.toIso8601String(),
      'rewards': reward?.toJson(),
    };
  }

  /// Check if this reward has expired
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }
}

/// Model for reward transactions
class RewardTransactionModel {
  final String id;
  final String userId;
  final String? rewardId;
  final int points;
  final String action;
  final String? description;
  final DateTime createdAt;
  final RewardModel? reward;

  const RewardTransactionModel({
    required this.id,
    required this.userId,
    this.rewardId,
    required this.points,
    required this.action,
    this.description,
    required this.createdAt,
    this.reward,
  });

  factory RewardTransactionModel.fromJson(Map<String, dynamic> json) {
    return RewardTransactionModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      rewardId: json['reward_id'] as String?,
      points: json['points'] as int,
      action: json['action'] as String,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      reward: json['rewards'] != null 
          ? RewardModel.fromJson(json['rewards'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'reward_id': rewardId,
      'points': points,
      'action': action,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'rewards': reward?.toJson(),
    };
  }

  /// Get the reward type from action
  RewardType get rewardType => RewardTypeExtension.fromString(action);
}

/// Model for login streaks
class LoginStreakModel {
  final String id;
  final String userId;
  final int currentStreak;
  final int longestStreak;
  final DateTime? lastLoginDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LoginStreakModel({
    required this.id,
    required this.userId,
    required this.currentStreak,
    required this.longestStreak,
    this.lastLoginDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LoginStreakModel.fromJson(Map<String, dynamic> json) {
    try {
      return LoginStreakModel(
        id: json['id']?.toString() ?? 'streak-${DateTime.now().millisecondsSinceEpoch}',
        userId: json['user_id']?.toString() ?? 'current-user',
        currentStreak: (json['current_streak'] as num?)?.toInt() ?? 0,
        longestStreak: (json['longest_streak'] as num?)?.toInt() ?? 0,
        lastLoginDate: json['last_login_date'] != null
            ? DateTime.tryParse(json['last_login_date'].toString())
            : null,
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'].toString()) ?? DateTime.now()
            : DateTime.now(),
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'].toString()) ?? DateTime.now()
            : DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error parsing LoginStreakModel from JSON: $e');
      debugPrint('JSON data: $json');
      // Return a default streak model to prevent crashes
      return LoginStreakModel(
        id: 'default-streak',
        userId: 'current-user',
        currentStreak: (json['current_streak'] as num?)?.toInt() ?? 0,
        longestStreak: (json['longest_streak'] as num?)?.toInt() ?? 0,
        lastLoginDate: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'current_streak': currentStreak,
      'longest_streak': longestStreak,
      'last_login_date': lastLoginDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Check if the user logged in today
  bool get hasLoggedInToday {
    if (lastLoginDate == null) return false;
    final today = DateTime.now();
    final loginDate = lastLoginDate!;
    return today.year == loginDate.year &&
           today.month == loginDate.month &&
           today.day == loginDate.day;
  }

  /// Check if the streak is broken (missed yesterday)
  bool get isStreakBroken {
    if (lastLoginDate == null) return true;
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    final loginDate = lastLoginDate!;
    return !(yesterday.year == loginDate.year &&
             yesterday.month == loginDate.month &&
             yesterday.day == loginDate.day) && !hasLoggedInToday;
  }
}
