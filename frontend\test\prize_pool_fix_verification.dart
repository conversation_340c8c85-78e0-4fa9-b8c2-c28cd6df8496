import 'package:flutter_test/flutter_test.dart';
import 'package:wiggyz_app/utils/match_utils.dart';

/// Test to verify the prize pool display fix
void main() {
  group('Prize Pool Display Fix Verification', () {
    
    test('Should show Current and Max format when maxParticipants is valid', () {
      // Normal scenario with valid maxParticipants
      final result = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 4, 10);
      expect(result, equals('Current: ₹36 | Max: ₹90'));
      print('✅ Normal scenario: "$result"');
    });
    
    test('Should show Current and Max format when maxParticipants is 0 (FIXED)', () {
      // This was the bug - when maxParticipants is 0, it should still show both values
      final result = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 4, 0);
      expect(result, equals('Current: ₹36 | Max: ₹90')); // Uses default max of 10
      print('✅ Zero maxParticipants (FIXED): "$result"');
    });
    
    test('Should show single value only when current equals valid max', () {
      // Only show single value when match is actually full
      final result = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 8, 8);
      expect(result, equals('₹72'));
      print('✅ Full match: "$result"');
    });
    
    test('Should handle zero current participants correctly', () {
      final result = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 0, 6);
      expect(result, equals('Current: TBD | Max: ₹54'));
      print('✅ Zero current participants: "$result"');
    });
    
    test('Should show Free for zero entry fee', () {
      final result = MatchUtils.formatCurrentAndMaxPrizePool(0.0, 4, 10);
      expect(result, equals('Free'));
      print('✅ Free match: "$result"');
    });
    
    test('Simulate the screenshot scenario (₹2 entry, 2 participants)', () {
      // This simulates what might be happening in the screenshot
      print('\n🎯 Screenshot Scenario Simulation:');
      
      // Scenario A: maxParticipants is 0 (the likely bug)
      final resultA = MatchUtils.formatCurrentAndMaxPrizePool(2.0, 2, 0);
      print('Scenario A (maxParticipants = 0): "$resultA"');
      
      // Scenario B: maxParticipants is valid
      final resultB = MatchUtils.formatCurrentAndMaxPrizePool(2.0, 2, 4);
      print('Scenario B (maxParticipants = 4): "$resultB"');
      
      // Scenario C: Using old function (for comparison)
      final resultC = MatchUtils.formatPrizePool(2.0, 2);
      print('Scenario C (old function): "$resultC"');
      
      // The fix should make Scenario A show "Current: ₹4 | Max: ₹18" instead of just "₹4"
      expect(resultA, equals('Current: ₹4 | Max: ₹18')); // Uses default max of 10
      expect(resultB, equals('Current: ₹4 | Max: ₹7'));
    });
    
    test('Test participant counting with mock data', () {
      final mockParticipants = [
        {'participant_type': 'player', 'user_id': 'user1'},
        {'participant_type': 'player', 'user_id': 'user2'},
        {'participant_type': 'spectator', 'user_id': 'user3'},
      ];
      
      final count = MatchUtils.getParticipantCount(mockParticipants);
      expect(count, equals(2)); // Only players count
      print('✅ Participant counting: $count players (excluding spectators)');
    });
    
    test('Test with null participants (common API issue)', () {
      final count = MatchUtils.getParticipantCount(null);
      expect(count, equals(0));
      
      final result = MatchUtils.formatCurrentAndMaxPrizePool(10.0, count, 0);
      expect(result, equals('Current: TBD | Max: ₹90')); // Uses default max of 10
      print('✅ Null participants: "$result"');
    });
  });
  
  group('Edge Cases and Error Handling', () {
    test('Should handle negative values gracefully', () {
      final result = MatchUtils.formatCurrentAndMaxPrizePool(-5.0, 2, 4);
      expect(result, equals('Free')); // Negative entry fee treated as free
    });
    
    test('Should handle very large numbers', () {
      final result = MatchUtils.formatCurrentAndMaxPrizePool(1000.0, 50, 100);
      expect(result, equals('Current: ₹45000 | Max: ₹90000'));
    });
    
    test('Should use custom currency symbol', () {
      final result = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 4, 10, currency: '\$');
      expect(result, equals('Current: \$36 | Max: \$90'));
    });
  });
}

/// Manual test function to run specific scenarios
void runManualTest() {
  print('\n🧪 MANUAL PRIZE POOL FIX TEST');
  print('=============================\n');
  
  // Test the exact scenario that was causing issues
  print('Testing the bug scenario:');
  print('Entry Fee: ₹2, Current Participants: 2, Max Participants: 0');
  
  final bugResult = MatchUtils.formatCurrentAndMaxPrizePool(2.0, 2, 0);
  print('Result: "$bugResult"');
  print('Expected: "Current: ₹4 | Max: ₹18" (using default max of 10)');
  print('Status: ${bugResult.contains("Current:") && bugResult.contains("Max:") ? "✅ FIXED" : "❌ STILL BROKEN"}');
  print('');
  
  // Test normal scenario
  print('Testing normal scenario:');
  print('Entry Fee: ₹10, Current Participants: 4, Max Participants: 10');
  
  final normalResult = MatchUtils.formatCurrentAndMaxPrizePool(10.0, 4, 10);
  print('Result: "$normalResult"');
  print('Expected: "Current: ₹36 | Max: ₹90"');
  print('Status: ${normalResult == "Current: ₹36 | Max: ₹90" ? "✅ WORKING" : "❌ BROKEN"}');
}
