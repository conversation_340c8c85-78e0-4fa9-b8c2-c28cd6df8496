import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wiggyz_app/widgets/shared/golden_app_bar.dart';
import 'package:wiggyz_app/widgets/shared/standard_form_components.dart';

class LanguageRegionSettingsScreen extends StatefulWidget {
  const LanguageRegionSettingsScreen({super.key});

  @override
  State<LanguageRegionSettingsScreen> createState() => _LanguageRegionSettingsScreenState();
}

class _LanguageRegionSettingsScreenState extends State<LanguageRegionSettingsScreen> {
  String _selectedLanguage = 'English';
  String _selectedCountry = 'United States';
  String _selectedTimezone = 'UTC-5 (Eastern Time)';
  String _selectedCurrency = 'USD (\$)';
  String _dateFormat = 'MM/DD/YYYY';
  String _timeFormat = '12-hour';
  String _numberFormat = '1,234.56';

  bool _isLoading = false;
  bool _isSaving = false;

  // Available options
  final List<String> _languageStrings = [
    'English',
    'Spanish',
    'French',
    'German',
    'Italian',
    'Portuguese',
    'Russian',
    'Chinese (Simplified)',
    'Chinese (Traditional)',
    'Japanese',
    'Korean',
    'Arabic',
    'Hindi',
    'Bengali',
    'Turkish',
    'Dutch',
    'Swedish',
    'Norwegian',
    'Danish',
    'Finnish',
  ];

  List<DropdownMenuItem<String>> get _languages => _languageStrings
      .map((language) => DropdownMenuItem<String>(
            value: language,
            child: Text(language),
          ))
      .toList();

  final List<String> _countryStrings = [
    'United States',
    'United Kingdom',
    'Canada',
    'Australia',
    'Germany',
    'France',
    'Spain',
    'Italy',
    'Netherlands',
    'Sweden',
    'Norway',
    'Denmark',
    'Finland',
    'Brazil',
    'Mexico',
    'Argentina',
    'India',
    'China',
    'Japan',
    'South Korea',
    'Singapore',
    'Malaysia',
    'Thailand',
    'Philippines',
    'Indonesia',
    'Turkey',
    'Russia',
    'Ukraine',
    'Poland',
    'Czech Republic',
  ];

  List<DropdownMenuItem<String>> get _countries => _countryStrings
      .map((country) => DropdownMenuItem<String>(
            value: country,
            child: Text(country),
          ))
      .toList();

  final List<String> _timezoneStrings = [
    'UTC-12 (Baker Island)',
    'UTC-11 (American Samoa)',
    'UTC-10 (Hawaii)',
    'UTC-9 (Alaska)',
    'UTC-8 (Pacific Time)',
    'UTC-7 (Mountain Time)',
    'UTC-6 (Central Time)',
    'UTC-5 (Eastern Time)',
    'UTC-4 (Atlantic Time)',
    'UTC-3 (Argentina)',
    'UTC-2 (South Georgia)',
    'UTC-1 (Azores)',
    'UTC+0 (London)',
    'UTC+1 (Central Europe)',
    'UTC+2 (Eastern Europe)',
    'UTC+3 (Moscow)',
    'UTC+4 (Dubai)',
    'UTC+5 (Pakistan)',
    'UTC+5:30 (India)',
    'UTC+6 (Bangladesh)',
    'UTC+7 (Thailand)',
    'UTC+8 (China)',
    'UTC+9 (Japan)',
    'UTC+10 (Australia East)',
    'UTC+11 (Solomon Islands)',
    'UTC+12 (New Zealand)',
  ];

  List<DropdownMenuItem<String>> get _timezones => _timezoneStrings
      .map((timezone) => DropdownMenuItem<String>(
            value: timezone,
            child: Text(timezone),
          ))
      .toList();

  final List<String> _currencyStrings = [
    'USD (\$)',
    'EUR (€)',
    'GBP (£)',
    'JPY (¥)',
    'CAD (C\$)',
    'AUD (A\$)',
    'CHF (Fr)',
    'CNY (¥)',
    'SEK (kr)',
    'NOK (kr)',
    'DKK (kr)',
    'PLN (zł)',
    'CZK (Kč)',
    'HUF (Ft)',
    'RUB (₽)',
    'BRL (R\$)',
    'MXN (\$)',
    'ARS (\$)',
    'INR (₹)',
    'KRW (₩)',
    'SGD (S\$)',
    'MYR (RM)',
    'THB (฿)',
    'PHP (₱)',
    'IDR (Rp)',
    'TRY (₺)',
  ];

  List<DropdownMenuItem<String>> get _currencies => _currencyStrings
      .map((currency) => DropdownMenuItem<String>(
            value: currency,
            child: Text(currency),
          ))
      .toList();

  final List<String> _dateFormatStrings = [
    'MM/DD/YYYY',
    'DD/MM/YYYY',
    'YYYY-MM-DD',
    'DD-MM-YYYY',
    'MM-DD-YYYY',
    'DD.MM.YYYY',
    'YYYY/MM/DD',
  ];

  List<DropdownMenuItem<String>> get _dateFormats => _dateFormatStrings
      .map((format) => DropdownMenuItem<String>(
            value: format,
            child: Text(format),
          ))
      .toList();

  final List<String> _timeFormatStrings = [
    '12-hour',
    '24-hour',
  ];

  List<DropdownMenuItem<String>> get _timeFormats => _timeFormatStrings
      .map((format) => DropdownMenuItem<String>(
            value: format,
            child: Text(format),
          ))
      .toList();

  final List<String> _numberFormatStrings = [
    '1,234.56',
    '1.234,56',
    '1 234,56',
    '1\'234.56',
  ];

  List<DropdownMenuItem<String>> get _numberFormats => _numberFormatStrings
      .map((format) => DropdownMenuItem<String>(
            value: format,
            child: Text(format),
          ))
      .toList();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      
      setState(() {
        _selectedLanguage = prefs.getString('selected_language') ?? 'English';
        _selectedCountry = prefs.getString('selected_country') ?? 'United States';
        _selectedTimezone = prefs.getString('selected_timezone') ?? 'UTC-5 (Eastern Time)';
        _selectedCurrency = prefs.getString('selected_currency') ?? 'USD (\$)';
        _dateFormat = prefs.getString('date_format') ?? 'MM/DD/YYYY';
        _timeFormat = prefs.getString('time_format') ?? '12-hour';
        _numberFormat = prefs.getString('number_format') ?? '1,234.56';
      });
    } catch (e) {
      debugPrint('Error loading language settings: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setString('selected_language', _selectedLanguage);
      await prefs.setString('selected_country', _selectedCountry);
      await prefs.setString('selected_timezone', _selectedTimezone);
      await prefs.setString('selected_currency', _selectedCurrency);
      await prefs.setString('date_format', _dateFormat);
      await prefs.setString('time_format', _timeFormat);
      await prefs.setString('number_format', _numberFormat);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Language and region settings saved successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving language settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to save settings. Please try again.',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: GoldenAppBar(
        title: 'Language & Region',
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveSettings,
            child: _isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                    ),
                  )
                : Text(
                    'Save',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFFFCC00),
              ),
            )
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoCard(isDarkMode),
                    const SizedBox(height: 24),
                    _buildLanguageSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildRegionSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildFormatSection(isDarkMode),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildLanguageSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Language Settings',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        StandardDropdownField(
          label: 'App Language',
          value: _selectedLanguage,
          items: _languages,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedLanguage = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildRegionSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Region Settings',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        StandardDropdownField(
          label: 'Country/Region',
          value: _selectedCountry,
          items: _countries,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedCountry = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        StandardDropdownField(
          label: 'Timezone',
          value: _selectedTimezone,
          items: _timezones,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedTimezone = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        StandardDropdownField(
          label: 'Currency',
          value: _selectedCurrency,
          items: _currencies,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedCurrency = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildFormatSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Format Settings',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        StandardDropdownField(
          label: 'Date Format',
          value: _dateFormat,
          items: _dateFormats,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _dateFormat = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        StandardDropdownField(
          label: 'Time Format',
          value: _timeFormat,
          items: _timeFormats,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _timeFormat = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        StandardDropdownField(
          label: 'Number Format',
          value: _numberFormat,
          items: _numberFormats,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _numberFormat = value;
              });
            }
          },
        ),
        const SizedBox(height: 24),
        _buildPreviewSection(isDarkMode),
      ],
    );
  }

  Widget _buildPreviewSection(bool isDarkMode) {
    final now = DateTime.now();
    String formattedDate = '';
    String formattedTime = '';
    String formattedNumber = '';

    // Format date based on selected format
    switch (_dateFormat) {
      case 'MM/DD/YYYY':
        formattedDate = '${now.month.toString().padLeft(2, '0')}/${now.day.toString().padLeft(2, '0')}/${now.year}';
        break;
      case 'DD/MM/YYYY':
        formattedDate = '${now.day.toString().padLeft(2, '0')}/${now.month.toString().padLeft(2, '0')}/${now.year}';
        break;
      case 'YYYY-MM-DD':
        formattedDate = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
        break;
      default:
        formattedDate = '${now.month.toString().padLeft(2, '0')}/${now.day.toString().padLeft(2, '0')}/${now.year}';
    }

    // Format time based on selected format
    if (_timeFormat == '12-hour') {
      final hour = now.hour > 12 ? now.hour - 12 : (now.hour == 0 ? 12 : now.hour);
      final period = now.hour >= 12 ? 'PM' : 'AM';
      formattedTime = '${hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')} $period';
    } else {
      formattedTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    }

    // Format number based on selected format
    const sampleNumber = 1234.56;
    switch (_numberFormat) {
      case '1,234.56':
        formattedNumber = '1,234.56';
        break;
      case '1.234,56':
        formattedNumber = '1.234,56';
        break;
      case '1 234,56':
        formattedNumber = '1 234,56';
        break;
      case '1\'234.56':
        formattedNumber = '1\'234.56';
        break;
      default:
        formattedNumber = '1,234.56';
    }

    return StandardCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Preview',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          _buildPreviewItem('Date', formattedDate, isDarkMode),
          _buildPreviewItem('Time', formattedTime, isDarkMode),
          _buildPreviewItem('Number', formattedNumber, isDarkMode),
          _buildPreviewItem('Currency', '${_selectedCurrency.split(' ')[1]}1,234.56', isDarkMode),
        ],
      ),
    );
  }

  Widget _buildPreviewItem(String label, String value, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}

  Widget _buildInfoCard(bool isDarkMode) {
    return StandardCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.language_outlined,
                color: isDarkMode ? const Color(0xFFD4AF37) : Colors.purple[700],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Language & Region',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? const Color(0xFFD4AF37) : Colors.purple[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Customize your language preferences and regional settings. Changes will take effect after restarting the app.',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }
