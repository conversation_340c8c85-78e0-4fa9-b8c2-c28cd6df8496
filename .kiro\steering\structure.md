# Project Structure

## Root Directory Organization

```
wiggyz/
├── backend/                 # Node.js/TypeScript API server
├── frontend/               # Flutter mobile application
├── wiggyz_admin/          # Next.js admin dashboard
├── scripts/               # Deployment and utility scripts
├── docs/                  # Project documentation
├── tests/                 # Cross-platform integration tests
└── *.js, *.md            # Root-level utilities and documentation
```

## Backend Structure (`backend/`)

```
backend/
├── src/
│   ├── features/          # Feature-based modules
│   │   ├── auth/         # Authentication system
│   │   ├── matches/      # Match management
│   │   ├── tournaments/  # Tournament system
│   │   ├── wallet/       # Payment and wallet
│   │   ├── profile/      # User profiles
│   │   └── admin/        # Admin-specific features
│   ├── middleware/       # Express middleware
│   ├── services/         # Business logic services
│   ├── utils/           # Utility functions
│   ├── config/          # Configuration files
│   └── types/           # TypeScript type definitions
├── dist/                # Compiled JavaScript output
└── uploads/             # Temporary file uploads
```

## Frontend Structure (`frontend/lib/`)

```
frontend/lib/
├── main.dart           # Application entry point
├── core/              # Core functionality
│   ├── api/          # API client and configuration
│   ├── services/     # Business logic services
│   ├── utils/        # Utility functions
│   └── navigation/   # Routing configuration
├── features/         # Feature-specific code
│   ├── auth/        # Authentication screens/logic
│   ├── wallet/      # Wallet and payment features
│   └── help_support/ # Support system
├── screens/          # UI screens
├── widgets/          # Reusable UI components
├── models/           # Data models
├── providers/        # State management
├── services/         # API services
└── theme/           # App theming
```

## Key Architectural Patterns

### Backend Patterns
- **Feature-based organization**: Each feature has its own controllers, services, routes
- **Middleware pattern**: Authentication, validation, rate limiting
- **Service layer**: Business logic separated from controllers
- **Repository pattern**: Database access abstraction through Supabase client

### Frontend Patterns
- **Provider pattern**: State management using Flutter Provider
- **Service layer**: API calls abstracted into service classes
- **Screen/Widget separation**: UI components organized by reusability
- **Model-driven**: Strong typing with Dart model classes

## File Naming Conventions

### Backend
- **Controllers**: `*.controller.ts`
- **Services**: `*.service.ts`
- **Routes**: `routes.ts` or `*.routes.ts`
- **Middleware**: `*.middleware.ts` or descriptive names
- **Tests**: `*.test.ts` in `__tests__` folders

### Frontend
- **Screens**: `*_screen.dart`
- **Services**: `*_service.dart`
- **Providers**: `*_provider.dart`
- **Models**: `*_model.dart` or `*_models.dart`
- **Widgets**: Descriptive names ending in `.dart`

## Configuration Files

- **Backend**: `.env` files for environment variables
- **Frontend**: `pubspec.yaml` for dependencies
- **Database**: Supabase configuration in backend config
- **Testing**: `jest.config.js` for backend, Flutter test config

## Development Workflow

1. **Backend changes**: Modify in `backend/src/`, test with Jest
2. **Frontend changes**: Modify in `frontend/lib/`, test with Flutter
3. **Database changes**: Update through Supabase dashboard or migrations
4. **Cross-platform testing**: Use root-level test scripts