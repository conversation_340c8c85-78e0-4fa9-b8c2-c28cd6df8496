'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { Plus, Search, Edit, Trash2, HelpCircle, FolderPlus } from 'lucide-react'

interface FaqCategory {
  id: string
  name: string
  description?: string
  display_order: number
  is_active: boolean
  created_at: string
  item_count?: number
}

interface FaqItem {
  id: string
  category_id: string
  question: string
  answer: string
  display_order: number
  is_active: boolean
  view_count: number
  helpful_count: number
  created_at: string
  updated_at: string
  category?: {
    id: string
    name: string
  }
}

export default function FaqManagementPage() {
  const [categories, setCategories] = useState<FaqCategory[]>([])
  const [items, setItems] = useState<FaqItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  
  // Dialog states
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false)
  const [isAddItemOpen, setIsAddItemOpen] = useState(false)
  const [isEditItemOpen, setIsEditItemOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<FaqItem | null>(null)
  
  // Form states
  const [newCategory, setNewCategory] = useState({
    name: '',
    description: '',
    display_order: 0
  })
  
  const [newItem, setNewItem] = useState({
    category_id: '',
    question: '',
    answer: '',
    display_order: 0
  })

  const { toast } = useToast()

  useEffect(() => {
    fetchFaqData()
  }, [])

  const fetchFaqData = async () => {
    setIsLoading(true)
    try {
      // Fetch categories and items
      const [categoriesRes, itemsRes] = await Promise.all([
        fetch('/api/support/faq/categories'),
        fetch('/api/support/faq/items')
      ])

      if (categoriesRes.ok && itemsRes.ok) {
        const categoriesData = await categoriesRes.json()
        const itemsData = await itemsRes.json()
        
        setCategories(categoriesData.data || [])
        setItems(itemsData.data || [])
      } else {
        throw new Error('Failed to fetch FAQ data')
      }
    } catch (error) {
      console.error('Error fetching FAQ data:', error)
      toast({
        title: 'Error',
        description: 'Failed to load FAQ data',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateCategory = async () => {
    try {
      const response = await fetch('/api/support/admin/faq/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newCategory)
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'FAQ category created successfully'
        })
        setIsAddCategoryOpen(false)
        setNewCategory({ name: '', description: '', display_order: 0 })
        fetchFaqData()
      } else {
        throw new Error('Failed to create category')
      }
    } catch (error) {
      console.error('Error creating category:', error)
      toast({
        title: 'Error',
        description: 'Failed to create FAQ category',
        variant: 'destructive'
      })
    }
  }

  const handleCreateItem = async () => {
    try {
      const response = await fetch('/api/support/admin/faq/items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newItem)
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'FAQ item created successfully'
        })
        setIsAddItemOpen(false)
        setNewItem({ category_id: '', question: '', answer: '', display_order: 0 })
        fetchFaqData()
      } else {
        throw new Error('Failed to create item')
      }
    } catch (error) {
      console.error('Error creating item:', error)
      toast({
        title: 'Error',
        description: 'Failed to create FAQ item',
        variant: 'destructive'
      })
    }
  }

  const handleEditItem = (item: FaqItem) => {
    setEditingItem(item)
    setNewItem({
      category_id: item.category_id,
      question: item.question,
      answer: item.answer,
      display_order: item.display_order
    })
    setIsEditItemOpen(true)
  }

  const handleUpdateItem = async () => {
    if (!editingItem) return

    try {
      const response = await fetch(`/api/support/admin/faq/items/${editingItem.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newItem)
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'FAQ item updated successfully'
        })
        setIsEditItemOpen(false)
        setEditingItem(null)
        setNewItem({ category_id: '', question: '', answer: '', display_order: 0 })
        fetchFaqData()
      } else {
        throw new Error('Failed to update item')
      }
    } catch (error) {
      console.error('Error updating item:', error)
      toast({
        title: 'Error',
        description: 'Failed to update FAQ item',
        variant: 'destructive'
      })
    }
  }

  const handleDeleteItem = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this FAQ item?')) return

    try {
      const response = await fetch(`/api/support/admin/faq/items/${itemId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'FAQ item deleted successfully'
        })
        fetchFaqData()
      } else {
        throw new Error('Failed to delete item')
      }
    } catch (error) {
      console.error('Error deleting item:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete FAQ item',
        variant: 'destructive'
      })
    }
  }

  const filteredItems = items.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || item.category_id === selectedCategory
    return matchesSearch && matchesCategory
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading FAQ data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">FAQ Management</h1>
          <p className="text-muted-foreground">Manage frequently asked questions and categories</p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isAddCategoryOpen} onOpenChange={setIsAddCategoryOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <FolderPlus className="mr-2 h-4 w-4" />
                Add Category
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add FAQ Category</DialogTitle>
                <DialogDescription>Create a new category to organize FAQ items</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="category-name">Category Name</Label>
                  <Input
                    id="category-name"
                    value={newCategory.name}
                    onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                    placeholder="Enter category name"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="category-description">Description</Label>
                  <Textarea
                    id="category-description"
                    value={newCategory.description}
                    onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                    placeholder="Enter category description"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="category-order">Display Order</Label>
                  <Input
                    id="category-order"
                    type="number"
                    value={newCategory.display_order}
                    onChange={(e) => setNewCategory({ ...newCategory, display_order: parseInt(e.target.value) || 0 })}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddCategoryOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateCategory}>Create Category</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={isAddItemOpen} onOpenChange={setIsAddItemOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add FAQ Item
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add FAQ Item</DialogTitle>
                <DialogDescription>Create a new frequently asked question</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="item-category">Category</Label>
                  <Select value={newItem.category_id} onValueChange={(value) => setNewItem({ ...newItem, category_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="item-question">Question</Label>
                  <Textarea
                    id="item-question"
                    value={newItem.question}
                    onChange={(e) => setNewItem({ ...newItem, question: e.target.value })}
                    placeholder="Enter the question"
                    rows={2}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="item-answer">Answer</Label>
                  <Textarea
                    id="item-answer"
                    value={newItem.answer}
                    onChange={(e) => setNewItem({ ...newItem, answer: e.target.value })}
                    placeholder="Enter the answer"
                    rows={4}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="item-order">Display Order</Label>
                  <Input
                    id="item-order"
                    type="number"
                    value={newItem.display_order}
                    onChange={(e) => setNewItem({ ...newItem, display_order: parseInt(e.target.value) || 0 })}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddItemOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateItem}>Create FAQ Item</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Categories Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {categories.map((category) => (
          <Card key={category.id}>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">{category.name}</CardTitle>
              <CardDescription className="text-xs">{category.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{category.item_count || 0}</div>
              <p className="text-xs text-muted-foreground">FAQ items</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* FAQ Items Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            FAQ Items
          </CardTitle>
          <CardDescription>Manage frequently asked questions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            {/* Filters */}
            <div className="flex gap-4">
              <div className="flex w-full items-center gap-2 sm:max-w-sm">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search FAQ items..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-9"
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* FAQ Items Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Question</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Views</TableHead>
                    <TableHead>Helpful</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredItems.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="text-muted-foreground">
                          {searchTerm || selectedCategory !== 'all'
                            ? 'No FAQ items match your filters'
                            : 'No FAQ items found. Create your first FAQ item to get started.'}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="max-w-md">
                          <div className="font-medium">{item.question}</div>
                          <div className="text-sm text-muted-foreground truncate">
                            {item.answer.substring(0, 100)}...
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">
                            {item.category?.name || 'Unknown'}
                          </Badge>
                        </TableCell>
                        <TableCell>{item.view_count}</TableCell>
                        <TableCell>{item.helpful_count}</TableCell>
                        <TableCell>
                          <Badge variant={item.is_active ? "default" : "secondary"}>
                            {item.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditItem(item)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteItem(item.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Edit FAQ Item Dialog */}
      <Dialog open={isEditItemOpen} onOpenChange={setIsEditItemOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit FAQ Item</DialogTitle>
            <DialogDescription>Update the frequently asked question</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-item-category">Category</Label>
              <Select value={newItem.category_id} onValueChange={(value) => setNewItem({ ...newItem, category_id: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-item-question">Question</Label>
              <Textarea
                id="edit-item-question"
                value={newItem.question}
                onChange={(e) => setNewItem({ ...newItem, question: e.target.value })}
                placeholder="Enter the question"
                rows={2}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-item-answer">Answer</Label>
              <Textarea
                id="edit-item-answer"
                value={newItem.answer}
                onChange={(e) => setNewItem({ ...newItem, answer: e.target.value })}
                placeholder="Enter the answer"
                rows={4}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-item-order">Display Order</Label>
              <Input
                id="edit-item-order"
                type="number"
                value={newItem.display_order}
                onChange={(e) => setNewItem({ ...newItem, display_order: parseInt(e.target.value) || 0 })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditItemOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateItem}>Update FAQ Item</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
