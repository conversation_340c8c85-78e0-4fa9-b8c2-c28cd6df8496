import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wiggyz_app/widgets/shared/golden_app_bar.dart';
import 'package:wiggyz_app/widgets/shared/standard_form_components.dart';

class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> {
  String _appName = 'WiggyZ Gaming';
  String _version = '1.0.0';
  String _buildNumber = '1';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAppInfo();
  }

  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appName = packageInfo.appName;
        _version = packageInfo.version;
        _buildNumber = packageInfo.buildNumber;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading app info: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Could not open link',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Could not open link',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: const GoldenAppBar(title: 'About'),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFFFCC00),
              ),
            )
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildAppInfoSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildLegalSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildSupportSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildCreditsSection(isDarkMode),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildAppInfoSection(bool isDarkMode) {
    return StandardCard(
      child: Column(
        children: [
          // App Logo
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.emoji_events,
              size: 40,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _appName,
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'The Ultimate Gaming Competition Platform',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Version $_version ($_buildNumber)',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Join millions of gamers worldwide in epic tournaments and competitions. Win real money, earn achievements, and become a gaming legend!',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLegalSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Legal & Policies',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        _buildLinkItem(
          icon: Icons.description_outlined,
          title: 'Terms of Service',
          subtitle: 'Read our terms and conditions',
          onTap: () => _launchUrl('https://wiggyz.com/terms'),
        ),
        _buildLinkItem(
          icon: Icons.privacy_tip_outlined,
          title: 'Privacy Policy',
          subtitle: 'Learn how we protect your data',
          onTap: () => _launchUrl('https://wiggyz.com/privacy'),
        ),
        _buildLinkItem(
          icon: Icons.gavel_outlined,
          title: 'Community Guidelines',
          subtitle: 'Rules for fair play and conduct',
          onTap: () => _launchUrl('https://wiggyz.com/guidelines'),
        ),
        _buildLinkItem(
          icon: Icons.security_outlined,
          title: 'Security Policy',
          subtitle: 'How we keep your account secure',
          onTap: () => _launchUrl('https://wiggyz.com/security'),
        ),
      ],
    );
  }

  Widget _buildSupportSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Support & Contact',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        _buildLinkItem(
          icon: Icons.help_outline,
          title: 'Help Center',
          subtitle: 'Find answers to common questions',
          onTap: () => _launchUrl('https://help.wiggyz.com'),
        ),
        _buildLinkItem(
          icon: Icons.email_outlined,
          title: 'Contact Support',
          subtitle: 'Get help from our support team',
          onTap: () => _launchUrl('mailto:<EMAIL>'),
        ),
        _buildLinkItem(
          icon: Icons.bug_report_outlined,
          title: 'Report a Bug',
          subtitle: 'Help us improve the app',
          onTap: () => _launchUrl('https://wiggyz.com/bug-report'),
        ),
        _buildLinkItem(
          icon: Icons.feedback_outlined,
          title: 'Send Feedback',
          subtitle: 'Share your thoughts and suggestions',
          onTap: () => _launchUrl('https://wiggyz.com/feedback'),
        ),
      ],
    );
  }

  Widget _buildCreditsSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Credits & Acknowledgments',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        StandardCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Development Team',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                '• WiggyZ Development Team\n'
                '• Flutter Framework by Google\n'
                '• Supabase for Backend Services\n'
                '• Various Open Source Contributors',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Special Thanks',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'Thanks to our amazing gaming community, beta testers, and everyone who helped make WiggyZ the best gaming platform possible.',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        _buildLinkItem(
          icon: Icons.code_outlined,
          title: 'Open Source Licenses',
          subtitle: 'View third-party licenses',
          onTap: () => _showLicensesDialog(),
        ),
      ],
    );
  }

  Widget _buildLinkItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: onTap,
        child: StandardCard(
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.open_in_new,
                size: 16,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLicensesDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Open Source Licenses',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: SingleChildScrollView(
            child: Text(
              '''Flutter Framework
Copyright (c) 2017 The Flutter Authors. All rights reserved.

Google Fonts
Copyright (c) 2019 Google Inc. All rights reserved.

Provider
Copyright (c) 2019 Remi Rousselet. All rights reserved.

Supabase Flutter
Copyright (c) 2020 Supabase. All rights reserved.

Go Router
Copyright (c) 2021 The Flutter Authors. All rights reserved.

Shared Preferences
Copyright (c) 2017 The Flutter Authors. All rights reserved.

Package Info Plus
Copyright (c) 2017 The Flutter Authors. All rights reserved.

URL Launcher
Copyright (c) 2017 The Flutter Authors. All rights reserved.

Image Picker
Copyright (c) 2017 The Flutter Authors. All rights reserved.

And many other open source packages that make this app possible.

For detailed license information, please visit our website.''',
              style: GoogleFonts.poppins(fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close', style: GoogleFonts.poppins()),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _launchUrl('https://wiggyz.com/licenses');
            },
            child: Text('View Full Licenses', style: GoogleFonts.poppins()),
          ),
        ],
      ),
    );
  }
}
