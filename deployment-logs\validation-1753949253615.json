{"timestamp": "2025-07-31T08:07:33.615Z", "results": [{"timestamp": "2025-07-31T08:07:33.113Z", "message": "Loaded environment variables from backend/.env", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.126Z", "message": "🔍 Running pre-deployment validation...", "type": "info", "category": "validation"}, {"timestamp": "2025-07-31T08:07:33.127Z", "message": "Validating environment variables...", "type": "info", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.128Z", "message": "Found required variable: SUPABASE_URL", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.128Z", "message": "Found required variable: SUPABASE_KEY", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.128Z", "message": "Found required variable: JWT_REFRESH_SECRET", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.129Z", "message": "Found required variable: RAZORPAY_KEY_ID", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.129Z", "message": "Found required variable: RAZORPAY_KEY_SECRET", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.130Z", "message": "Found optional variable: JWT_ACCESS_EXPIRY", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.130Z", "message": "Found optional variable: JWT_REFRESH_EXPIRY", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.131Z", "message": "Missing optional environment variable: PAYMENT_TIMEOUT_MINUTES (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.131Z", "message": "Missing optional environment variable: DISABLE_FRAUD_DETECTION (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.131Z", "message": "✅ All required environment variables are present", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:33.132Z", "message": "Validating Supabase connection...", "type": "info", "category": "database"}, {"timestamp": "2025-07-31T08:07:33.609Z", "message": "Supabase connection successful", "type": "success", "category": "database"}, {"timestamp": "2025-07-31T08:07:33.610Z", "message": "Validating Razorpay credentials...", "type": "info", "category": "payment"}, {"timestamp": "2025-07-31T08:07:33.610Z", "message": "Razorpay credentials format validation passed", "type": "success", "category": "payment"}, {"timestamp": "2025-07-31T08:07:33.610Z", "message": "Validating build output...", "type": "info", "category": "build"}, {"timestamp": "2025-07-31T08:07:33.613Z", "message": "Build output is recent", "type": "success", "category": "build"}, {"timestamp": "2025-07-31T08:07:33.614Z", "message": "Build output validation passed", "type": "success", "category": "build"}, {"timestamp": "2025-07-31T08:07:33.614Z", "message": "✅ All pre-deployment validations passed", "type": "success", "category": "validation"}]}