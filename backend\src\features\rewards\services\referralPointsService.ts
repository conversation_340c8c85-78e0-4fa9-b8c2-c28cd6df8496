/**
 * Referral Points Service
 * Manages non-withdrawable referral points that can only be used for match entries
 */

import { supabase } from '../../../config/supabase';
import { logger } from '../../../utils/logger';

export interface ReferralPointsBalance {
  totalPoints: number;
  availablePoints: number;
  usedPoints: number;
  earnedFromReferrals: number;
}

export interface ReferralPointTransaction {
  id: string;
  userId: string;
  transactionType: 'earned' | 'used' | 'expired' | 'admin_adjustment';
  points: number;
  sourceType: string;
  sourceId?: string;
  description?: string;
  createdAt: string;
  metadata?: any;
}

class ReferralPointsService {
  /**
   * Get user's referral points balance
   */
  async getUserBalance(userId: string): Promise<ReferralPointsBalance> {
    try {
      const { data, error } = await supabase
        .rpc('get_referral_points_balance', { p_user_id: userId });

      if (error) throw error;

      if (!data || data.length === 0) {
        return {
          totalPoints: 0,
          availablePoints: 0,
          usedPoints: 0,
          earnedFromReferrals: 0
        };
      }

      const balance = data[0];
      return {
        totalPoints: balance.total_points || 0,
        availablePoints: balance.available_points || 0,
        usedPoints: balance.used_points || 0,
        earnedFromReferrals: balance.earned_from_referrals || 0
      };
    } catch (error) {
      logger.error(`Error getting referral points balance for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Award referral points to a user
   */
  async awardPoints(
    userId: string,
    points: number,
    sourceType: string,
    sourceId?: string,
    description?: string
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('award_referral_points', {
          p_user_id: userId,
          p_points: points,
          p_source_type: sourceType,
          p_source_id: sourceId,
          p_description: description
        });

      if (error) throw error;

      logger.info(`Awarded ${points} referral points to user ${userId} from ${sourceType}`);
      return data;
    } catch (error) {
      logger.error(`Error awarding referral points to user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Use referral points for match entry
   */
  async usePointsForMatch(
    userId: string,
    points: number,
    matchId: string,
    description?: string
  ): Promise<boolean> {
    try {
      // Check if user has sufficient points
      const balance = await this.getUserBalance(userId);
      if (balance.availablePoints < points) {
        throw new Error(`Insufficient referral points. Available: ${balance.availablePoints}, Required: ${points}`);
      }

      const { data, error } = await supabase
        .rpc('use_referral_points', {
          p_user_id: userId,
          p_points: points,
          p_source_type: 'match_entry',
          p_source_id: matchId,
          p_description: description || `Match entry fee payment`
        });

      if (error) throw error;

      if (!data) {
        throw new Error('Failed to use referral points - insufficient balance or system error');
      }

      logger.info(`User ${userId} used ${points} referral points for match ${matchId}`);
      return true;
    } catch (error) {
      logger.error(`Error using referral points for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Check if user has sufficient referral points
   */
  async hasSufficientPoints(userId: string, requiredPoints: number): Promise<boolean> {
    try {
      const balance = await this.getUserBalance(userId);
      return balance.availablePoints >= requiredPoints;
    } catch (error) {
      logger.error(`Error checking sufficient referral points for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Get user's referral point transaction history
   */
  async getTransactionHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<ReferralPointTransaction[]> {
    try {
      const { data, error } = await supabase
        .from('referral_point_transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;

      return data.map((transaction: any) => ({
        id: transaction.id,
        userId: transaction.user_id,
        transactionType: transaction.transaction_type,
        points: transaction.points,
        sourceType: transaction.source_type,
        sourceId: transaction.source_id,
        description: transaction.description,
        createdAt: transaction.created_at,
        metadata: transaction.metadata
      }));
    } catch (error) {
      logger.error(`Error getting transaction history for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Admin function to adjust user's referral points
   */
  async adminAdjustPoints(
    userId: string,
    pointsAdjustment: number,
    adminId: string,
    reason: string
  ): Promise<boolean> {
    try {
      const sourceType = pointsAdjustment > 0 ? 'admin_adjustment' : 'admin_adjustment';
      const description = `Admin adjustment by ${adminId}: ${reason}`;

      if (pointsAdjustment > 0) {
        // Award points
        return await this.awardPoints(userId, pointsAdjustment, sourceType, adminId, description);
      } else {
        // Deduct points (use negative value)
        const { data, error } = await supabase
          .rpc('use_referral_points', {
            p_user_id: userId,
            p_points: Math.abs(pointsAdjustment),
            p_source_type: sourceType,
            p_source_id: adminId,
            p_description: description
          });

        if (error) throw error;
        return data;
      }
    } catch (error) {
      logger.error(`Error adjusting referral points for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get referral points statistics for admin dashboard
   */
  async getAdminStatistics(): Promise<{
    totalPointsAwarded: number;
    totalPointsUsed: number;
    activeUsers: number;
    averageBalance: number;
  }> {
    try {
      const { data: stats, error } = await supabase
        .from('referral_points')
        .select('total_points, used_for_matches, earned_from_referrals');

      if (error) throw error;

      const totalPointsAwarded = stats.reduce((sum: number, user: any) => sum + (user.earned_from_referrals || 0), 0);
      const totalPointsUsed = stats.reduce((sum: number, user: any) => sum + (user.used_for_matches || 0), 0);
      const activeUsers = stats.filter((user: any) => (user.total_points || 0) > 0).length;
      const averageBalance = activeUsers > 0 ?
        stats.reduce((sum: number, user: any) => sum + (user.total_points - user.used_for_matches || 0), 0) / activeUsers : 0;

      return {
        totalPointsAwarded,
        totalPointsUsed,
        activeUsers,
        averageBalance: Math.round(averageBalance * 100) / 100
      };
    } catch (error) {
      logger.error(`Error getting referral points admin statistics: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get top users by referral points earned
   */
  async getTopEarners(limit: number = 10): Promise<Array<{
    userId: string;
    userName: string;
    totalPoints: number;
    earnedFromReferrals: number;
    usedPoints: number;
  }>> {
    try {
      const { data, error } = await supabase
        .from('referral_points')
        .select(`
          user_id,
          total_points,
          earned_from_referrals,
          used_for_matches,
          users:user_id (
            name,
            email
          )
        `)
        .order('earned_from_referrals', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data.map((user: any) => ({
        userId: user.user_id,
        userName: user.users?.name || 'Unknown User',
        totalPoints: user.total_points || 0,
        earnedFromReferrals: user.earned_from_referrals || 0,
        usedPoints: user.used_for_matches || 0
      }));
    } catch (error) {
      logger.error(`Error getting top referral points earners: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Expire old unused referral points (if needed for business rules)
   */
  async expireOldPoints(daysOld: number = 365): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      // This would require additional logic to track point expiration
      // For now, we'll just log the action
      logger.info(`Expiring referral points older than ${daysOld} days (cutoff: ${cutoffDate.toISOString()})`);
      
      // Implementation would depend on business requirements
      // Could involve updating points and creating expiration transactions
      
      return 0; // Return number of points expired
    } catch (error) {
      logger.error(`Error expiring old referral points: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}

export default new ReferralPointsService();
