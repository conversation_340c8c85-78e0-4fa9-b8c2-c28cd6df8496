#!/usr/bin/env node

/**
 * Public Deployment Script for WiggyZ Backend
 * Deploys to Vercel with public access configuration
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PublicDeployment {
  constructor() {
    this.rootDir = path.resolve(__dirname, '../..');
    this.backendDir = path.join(this.rootDir, 'backend');
  }

  log(message, type = 'info') {
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} ${message}`);
  }

  ensurePublicVercelConfig() {
    this.log('Ensuring public Vercel configuration...');
    
    const vercelConfigPath = path.join(this.backendDir, 'vercel.json');
    const config = {
      "version": 2,
      "name": "wiggyz-api-public",
      "public": true,
      "builds": [
        {
          "src": "dist/vercel.js",
          "use": "@vercel/node",
          "config": {
            "maxLambdaSize": "50mb",
            "maxDuration": 30
          }
        }
      ],
      "routes": [
        {
          "src": "/health",
          "dest": "/dist/vercel.js"
        },
        {
          "src": "/api/v1/(.*)",
          "dest": "/dist/vercel.js"
        },
        {
          "src": "/(.*)",
          "dest": "/dist/vercel.js"
        }
      ],
      "headers": [
        {
          "source": "/api/v1/(.*)",
          "headers": [
            {
              "key": "Access-Control-Allow-Origin",
              "value": "*"
            },
            {
              "key": "Access-Control-Allow-Methods",
              "value": "GET, POST, PUT, DELETE, OPTIONS"
            },
            {
              "key": "Access-Control-Allow-Headers",
              "value": "Content-Type, Authorization, X-Idempotency-Key"
            }
          ]
        },
        {
          "source": "/health",
          "headers": [
            {
              "key": "Access-Control-Allow-Origin",
              "value": "*"
            }
          ]
        }
      ],
      "env": {
        "NODE_ENV": "production"
      },
      "regions": ["iad1"]
    };

    fs.writeFileSync(vercelConfigPath, JSON.stringify(config, null, 2));
    this.log('Updated vercel.json for public access', 'success');
  }

  removeVercelLink() {
    const vercelDir = path.join(this.backendDir, '.vercel');
    if (fs.existsSync(vercelDir)) {
      this.log('Removing existing Vercel project link...');
      try {
        if (process.platform === 'win32') {
          execSync('rmdir /s /q .vercel', { cwd: this.backendDir, stdio: 'inherit' });
        } else {
          execSync('rm -rf .vercel', { cwd: this.backendDir, stdio: 'inherit' });
        }
        this.log('Vercel link removed', 'success');
      } catch (error) {
        this.log('Failed to remove Vercel link, continuing...', 'warn');
      }
    }
  }

  buildBackend() {
    this.log('Building backend...');
    try {
      execSync('npm run build', { cwd: this.backendDir, stdio: 'inherit' });
      this.log('Backend built successfully', 'success');
      return true;
    } catch (error) {
      this.log(`Build failed: ${error.message}`, 'error');
      return false;
    }
  }

  deployToVercel() {
    this.log('Deploying to Vercel...');
    try {
      // Deploy with explicit settings for public access
      const deployCommand = 'npx vercel --prod --yes --public';
      this.log(`Running: ${deployCommand}`);
      
      const output = execSync(deployCommand, { 
        cwd: this.backendDir, 
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      this.log('Deployment output:', 'info');
      console.log(output);
      
      // Extract deployment URL
      const urlMatch = output.match(/https:\/\/[^\s]+/);
      const deploymentUrl = urlMatch ? urlMatch[0] : null;
      
      if (deploymentUrl) {
        this.log(`Deployment successful! URL: ${deploymentUrl}`, 'success');
        return deploymentUrl;
      } else {
        this.log('Deployment completed but URL not found', 'warn');
        return null;
      }
    } catch (error) {
      this.log(`Deployment failed: ${error.message}`, 'error');
      this.log('Error output:', 'error');
      console.error(error.stdout || error.message);
      return null;
    }
  }

  async testDeployment(url) {
    if (!url) {
      this.log('No URL to test', 'warn');
      return false;
    }

    this.log(`Testing deployment at: ${url}`);
    
    try {
      // Test health endpoint
      const https = require('https');
      const testUrl = `${url}/health`;
      
      return new Promise((resolve) => {
        const req = https.get(testUrl, (res) => {
          let data = '';
          res.on('data', chunk => data += chunk);
          res.on('end', () => {
            if (res.statusCode === 200) {
              this.log('Health endpoint test passed!', 'success');
              this.log(`Response: ${data}`, 'info');
              resolve(true);
            } else if (res.statusCode === 401) {
              this.log('Deployment still requires authentication', 'error');
              resolve(false);
            } else {
              this.log(`Health endpoint returned status ${res.statusCode}`, 'warn');
              resolve(false);
            }
          });
        });
        
        req.on('error', (error) => {
          this.log(`Test failed: ${error.message}`, 'error');
          resolve(false);
        });
        
        req.setTimeout(10000, () => {
          this.log('Test timeout', 'error');
          req.destroy();
          resolve(false);
        });
      });
    } catch (error) {
      this.log(`Test error: ${error.message}`, 'error');
      return false;
    }
  }

  async deployPublic() {
    this.log('🚀 Starting public deployment process...');
    
    try {
      // Step 1: Ensure public configuration
      this.ensurePublicVercelConfig();
      
      // Step 2: Remove existing Vercel link
      this.removeVercelLink();
      
      // Step 3: Build backend
      if (!this.buildBackend()) {
        throw new Error('Build failed');
      }
      
      // Step 4: Deploy to Vercel
      const deploymentUrl = this.deployToVercel();
      
      if (!deploymentUrl) {
        throw new Error('Deployment failed');
      }
      
      // Step 5: Test deployment
      this.log('Waiting 10 seconds for deployment to be ready...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      const testPassed = await this.testDeployment(deploymentUrl);
      
      if (testPassed) {
        this.log('🎉 Public deployment successful and accessible!', 'success');
        this.log(`✅ Backend URL: ${deploymentUrl}`, 'success');
        this.log(`✅ API Base URL: ${deploymentUrl}/api/v1`, 'success');
        return { success: true, url: deploymentUrl };
      } else {
        this.log('⚠️ Deployment completed but accessibility test failed', 'warn');
        return { success: false, url: deploymentUrl, issue: 'accessibility' };
      }
      
    } catch (error) {
      this.log(`💥 Deployment failed: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }
}

// CLI Interface
if (require.main === module) {
  console.log('🌐 WiggyZ Public Deployment Tool\n');
  
  const deployer = new PublicDeployment();
  
  deployer.deployPublic()
    .then(result => {
      if (result.success) {
        console.log('\n✅ Deployment completed successfully!');
        console.log(`🔗 Use this URL in your Flutter app: ${result.url}/api/v1`);
        process.exit(0);
      } else {
        console.log('\n❌ Deployment failed or has issues');
        if (result.url) {
          console.log(`🔗 URL: ${result.url} (may require manual configuration)`);
        }
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Deployment crashed:', error.message);
      process.exit(1);
    });
}

module.exports = PublicDeployment;
