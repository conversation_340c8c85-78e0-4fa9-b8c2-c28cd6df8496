"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AdminHeader } from "@/components/admin-header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  CreditCard, 
  Search, 
  Filter, 
  Eye, 
  Check, 
  X, 
  Clock,
  AlertCircle,
  User,
  Calendar,
  DollarSign,
  RefreshCw
} from "lucide-react"
import { toast } from "sonner"
import { createClient } from "@/lib/supabaseClient"

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8080/api/v1'

interface WithdrawalRequest {
  id: string
  user_id: string
  amount: number
  currency: string
  status: 'pending' | 'approved' | 'completed' | 'rejected'
  account_details: any
  requested_at: string
  processed_at?: string
  completed_at?: string
  admin_notes?: string
  user_name: string
  user_email: string
  user_phone: string
  admin_name?: string
  transaction_metadata: any
}

interface WithdrawalStats {
  pending: number
  approved: number
  completed: number
  rejected: number
  total: number
}

export default function WithdrawalsPage() {
  const [withdrawals, setWithdrawals] = useState<WithdrawalRequest[]>([])
  const [stats, setStats] = useState<WithdrawalStats>({ pending: 0, approved: 0, completed: 0, rejected: 0, total: 0 })
  const [loading, setLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalRequest | null>(null)
  const [actionLoading, setActionLoading] = useState(false)
  const [adminNotes, setAdminNotes] = useState('')

  const supabase = createClient()

  const fetchWithdrawals = async () => {
    try {
      setLoading(true)
      console.log('🔄 Fetching withdrawals...')

      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      })

      if (selectedStatus !== 'all') {
        params.append('status', selectedStatus)
      }

      const session = await supabase.auth.getSession()
      const token = session.data.session?.access_token

      if (!token) {
        throw new Error('No authentication token available')
      }

      console.log(`📡 Making request to: ${API_BASE_URL}/admin/withdrawals?${params}`)
      console.log(`🔑 Using token (first 20 chars): ${token.substring(0, 20)}...`)

      const response = await fetch(`${API_BASE_URL}/admin/withdrawals?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log(`📊 Response status: ${response.status}`)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Response error:', errorText)
        throw new Error(`Failed to fetch withdrawals: ${response.status} ${errorText}`)
      }

      const data = await response.json()
      console.log('✅ Withdrawals data received:', data)

      setWithdrawals(data.data.requests || [])
      setTotalPages(data.data.pagination?.total_pages || 1)

      console.log(`📋 Set ${data.data.requests?.length || 0} withdrawals`)
    } catch (error) {
      console.error('❌ Error fetching withdrawals:', error)
      toast.error(`Failed to fetch withdrawal requests: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      console.log('📊 Fetching withdrawal stats...')

      const session = await supabase.auth.getSession()
      const token = session.data.session?.access_token

      if (!token) {
        throw new Error('No authentication token available')
      }

      console.log(`📡 Making request to: ${API_BASE_URL}/admin/withdrawals/stats`)

      const response = await fetch(`${API_BASE_URL}/admin/withdrawals/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log(`📊 Stats response status: ${response.status}`)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Stats response error:', errorText)
        throw new Error(`Failed to fetch stats: ${response.status} ${errorText}`)
      }

      const data = await response.json()
      console.log('✅ Stats data received:', data)

      setStats(data.data || { pending: 0, approved: 0, completed: 0, rejected: 0, total: 0 })
      console.log('📈 Stats updated:', data.data)
    } catch (error) {
      console.error('❌ Error fetching stats:', error)
      toast.error(`Failed to fetch withdrawal statistics: ${error.message}`)
    }
  }

  const handleAction = async (action: 'approve' | 'reject' | 'complete', withdrawalId: string) => {
    if (!selectedWithdrawal) return

    // Validate required fields
    if (action === 'reject' && !adminNotes.trim()) {
      toast.error('Admin notes are required for rejection')
      return
    }

    try {
      setActionLoading(true)

      const session = await supabase.auth.getSession()
      const token = session.data.session?.access_token

      if (!token) {
        throw new Error('No authentication token available')
      }

      const endpoint = `${API_BASE_URL}/admin/withdrawals/${withdrawalId}/${action}`
      const body = action === 'reject'
        ? { admin_notes: adminNotes.trim() }
        : adminNotes.trim()
          ? { admin_notes: adminNotes.trim() }
          : {}

      console.log(`🔄 ${action.toUpperCase()} withdrawal request:`, {
        endpoint,
        withdrawalId,
        body,
        token: token.substring(0, 20) + '...'
      })

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(body)
      })

      console.log(`📊 ${action.toUpperCase()} response status:`, response.status)

      if (!response.ok) {
        let errorMessage = `Failed to ${action} withdrawal`
        try {
          const errorData = await response.json()
          errorMessage = errorData.message || errorData.error || errorMessage
          console.error(`❌ ${action.toUpperCase()} error response:`, errorData)
        } catch (parseError) {
          const errorText = await response.text()
          console.error(`❌ ${action.toUpperCase()} error text:`, errorText)
          if (errorText) {
            errorMessage = errorText
          }
        }
        throw new Error(errorMessage)
      }

      const responseData = await response.json()
      console.log(`✅ ${action.toUpperCase()} successful:`, responseData)

      toast.success(`Withdrawal ${action}d successfully`)
      setSelectedWithdrawal(null)
      setAdminNotes('')
      fetchWithdrawals()
      fetchStats()
    } catch (error: any) {
      console.error(`❌ Error ${action}ing withdrawal:`, error)
      toast.error(error.message || `Failed to ${action} withdrawal`)
    } finally {
      setActionLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      approved: 'bg-blue-100 text-blue-800 border-blue-200',
      completed: 'bg-green-100 text-green-800 border-green-200',
      rejected: 'bg-red-100 text-red-800 border-red-200'
    }
    
    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🔄 Loading withdrawal data...')
        await Promise.all([fetchWithdrawals(), fetchStats()])
        console.log('✅ Withdrawal data loaded successfully')
      } catch (error) {
        console.error('❌ Failed to load withdrawal data:', error)
      }
    }

    loadData()
  }, [currentPage, selectedStatus])

  // Filter withdrawals based on search term
  const filteredWithdrawals = withdrawals.filter(withdrawal =>
    withdrawal.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    withdrawal.user_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    withdrawal.id.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <AdminHeader 
        title="Withdrawal Requests" 
        description="Manage user withdrawal requests and process payments"
      />

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <Check className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.approved}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <Check className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <X className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by user name, email, or ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-[300px]"
                />
              </div>
              
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button onClick={() => { fetchWithdrawals(); fetchStats(); }} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Withdrawals Table */}
      <Card>
        <CardHeader>
          <CardTitle>Withdrawal Requests</CardTitle>
          <CardDescription>
            {filteredWithdrawals.length} of {withdrawals.length} requests shown
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Requested</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredWithdrawals.map((withdrawal) => (
                    <TableRow key={withdrawal.id}>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{withdrawal.user_name}</span>
                          <span className="text-sm text-muted-foreground">{withdrawal.user_email}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{formatCurrency(withdrawal.amount)}</span>
                          <span className="text-sm text-muted-foreground">{withdrawal.currency}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(withdrawal.status)}</TableCell>
                      <TableCell>
                        <span className="text-sm">{formatDate(withdrawal.requested_at)}</span>
                      </TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setSelectedWithdrawal(withdrawal)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                            {selectedWithdrawal && (
                              <WithdrawalDetailsDialog 
                                withdrawal={selectedWithdrawal}
                                onAction={handleAction}
                                actionLoading={actionLoading}
                                adminNotes={adminNotes}
                                setAdminNotes={setAdminNotes}
                              />
                            )}
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Separate component for withdrawal details dialog
function WithdrawalDetailsDialog({ 
  withdrawal, 
  onAction, 
  actionLoading, 
  adminNotes, 
  setAdminNotes 
}: {
  withdrawal: WithdrawalRequest
  onAction: (action: 'approve' | 'reject' | 'complete', id: string) => void
  actionLoading: boolean
  adminNotes: string
  setAdminNotes: (notes: string) => void
}) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <>
      <DialogHeader>
        <DialogTitle>Withdrawal Request Details</DialogTitle>
        <DialogDescription>
          Review and manage this withdrawal request
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-6">
        {/* User Information */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-sm font-medium">User Name</Label>
            <p className="text-sm">{withdrawal.user_name}</p>
          </div>
          <div>
            <Label className="text-sm font-medium">Email</Label>
            <p className="text-sm">{withdrawal.user_email}</p>
          </div>
          <div>
            <Label className="text-sm font-medium">Phone</Label>
            <p className="text-sm">{withdrawal.user_phone || 'N/A'}</p>
          </div>
          <div>
            <Label className="text-sm font-medium">Request ID</Label>
            <p className="text-sm font-mono">{withdrawal.id}</p>
          </div>
        </div>

        {/* Withdrawal Details */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-sm font-medium">Amount</Label>
            <p className="text-lg font-semibold">{formatCurrency(withdrawal.amount)}</p>
          </div>
          <div>
            <Label className="text-sm font-medium">Status</Label>
            <div className="mt-1">
              <Badge className={
                withdrawal.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                withdrawal.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                withdrawal.status === 'completed' ? 'bg-green-100 text-green-800' :
                'bg-red-100 text-red-800'
              }>
                {withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1)}
              </Badge>
            </div>
          </div>
          <div>
            <Label className="text-sm font-medium">Requested At</Label>
            <p className="text-sm">{formatDate(withdrawal.requested_at)}</p>
          </div>
          {withdrawal.processed_at && (
            <div>
              <Label className="text-sm font-medium">Processed At</Label>
              <p className="text-sm">{formatDate(withdrawal.processed_at)}</p>
            </div>
          )}
        </div>

        {/* Bank Account Details */}
        <div>
          <Label className="text-sm font-medium mb-2 block">Bank Account Details</Label>
          <div className="bg-muted/50 dark:bg-muted/30 p-4 rounded-lg space-y-2">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm font-medium text-foreground">Account Holder:</span>
                <p className="text-sm text-muted-foreground">{withdrawal.account_details?.account_holder_name}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-foreground">Bank Name:</span>
                <p className="text-sm text-muted-foreground">{withdrawal.account_details?.bank_name}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-foreground">Account Number:</span>
                <p className="text-sm font-mono text-muted-foreground">{withdrawal.account_details?.account_number}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-foreground">IFSC Code:</span>
                <p className="text-sm font-mono text-muted-foreground">{withdrawal.account_details?.ifsc_code}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Fee Breakdown */}
        {withdrawal.transaction_metadata?.fee_breakdown && (
          <div>
            <Label className="text-sm font-medium mb-2 block">Fee Breakdown</Label>
            <div className="bg-muted/50 dark:bg-muted/30 p-4 rounded-lg space-y-2">
              <div className="flex justify-between">
                <span className="text-foreground">Gross Amount:</span>
                <span className="text-foreground">{formatCurrency(withdrawal.transaction_metadata.fee_breakdown.gross_amount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-foreground">Transaction Fee:</span>
                <span className="text-foreground">{formatCurrency(withdrawal.transaction_metadata.fee_breakdown.transaction_fee)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-foreground">GST on Fee:</span>
                <span className="text-foreground">{formatCurrency(withdrawal.transaction_metadata.fee_breakdown.gst_on_fee)}</span>
              </div>
              <div className="flex justify-between font-semibold border-t border-border pt-2">
                <span className="text-foreground">Net Amount to Transfer:</span>
                <span className="text-foreground">{formatCurrency(withdrawal.transaction_metadata.fee_breakdown.net_amount_transferred)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Admin Notes */}
        {withdrawal.admin_notes && (
          <div>
            <Label className="text-sm font-medium">Admin Notes</Label>
            <p className="text-sm bg-muted/50 dark:bg-muted/30 text-muted-foreground p-3 rounded-lg mt-1">{withdrawal.admin_notes}</p>
          </div>
        )}

        {/* Action Section */}
        {withdrawal.status === 'pending' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="admin-notes">Admin Notes</Label>
              <Textarea
                id="admin-notes"
                placeholder="Add notes about this withdrawal request..."
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                className="mt-1"
              />
            </div>
            
            <div className="flex gap-2">
              <Button
                onClick={() => onAction('approve', withdrawal.id)}
                disabled={actionLoading}
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="h-4 w-4 mr-2" />
                Approve
              </Button>
              <Button
                onClick={() => onAction('reject', withdrawal.id)}
                disabled={actionLoading || !adminNotes.trim()}
                variant="destructive"
              >
                <X className="h-4 w-4 mr-2" />
                Reject
              </Button>
            </div>
          </div>
        )}

        {withdrawal.status === 'approved' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="completion-notes">Completion Notes (Optional)</Label>
              <Textarea
                id="completion-notes"
                placeholder="Add notes about the money transfer..."
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                className="mt-1"
              />
            </div>
            
            <Button
              onClick={() => onAction('complete', withdrawal.id)}
              disabled={actionLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Check className="h-4 w-4 mr-2" />
              Mark as Completed
            </Button>
          </div>
        )}
      </div>
    </>
  )
}
