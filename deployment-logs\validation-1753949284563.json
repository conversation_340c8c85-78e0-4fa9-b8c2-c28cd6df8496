{"timestamp": "2025-07-31T08:08:04.563Z", "results": [{"timestamp": "2025-07-31T08:08:04.208Z", "message": "Loaded environment variables from backend/.env", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:08:04.221Z", "message": "🔍 Running post-deployment validation...", "type": "info", "category": "validation"}, {"timestamp": "2025-07-31T08:08:04.221Z", "message": "Validating deployed endpoint: https://wiggyz-backend-fxui5976h-tausifraja977-gmailcoms-projects.vercel.app", "type": "info", "category": "deployment"}, {"timestamp": "2025-07-31T08:08:04.562Z", "message": "Health endpoint failed: undefined", "type": "error", "category": "deployment"}, {"timestamp": "2025-07-31T08:08:04.562Z", "message": "❌ Post-deployment validation failed", "type": "error", "category": "validation"}]}