"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { 
  AlertCircle, 
  CheckCircle, 
  Eye, 
  Trophy, 
  Users, 
  Clock,
  Image as ImageIcon,
  ExternalLink,
  XCircle
} from 'lucide-react'
import { 
  getTournamentPendingResults,
  verifyTournamentResult,
  assignTournamentWinners,
  getTournaments,
  type TournamentResultData
} from '@/lib/tournamentService'
import { format } from 'date-fns'

// Game mapping for display
const gameNames: Record<number, string> = {
  1: "Free Fire",
  2: "PUBG Mobile", 
  3: "Call of Duty Mobile",
  4: "Valorant",
  5: "Mobile Legends"
}

export default function TournamentPendingResultsPage() {
  const router = useRouter()
  const { toast } = useToast()

  // State
  const [tournaments, setTournaments] = useState<any[]>([])
  const [pendingResults, setPendingResults] = useState<TournamentResultData[]>([])
  const [selectedTournament, setSelectedTournament] = useState<any | null>(null)
  const [selectedResult, setSelectedResult] = useState<TournamentResultData | null>(null)
  const [loading, setLoading] = useState(true)
  const [assignWinnerDialogOpen, setAssignWinnerDialogOpen] = useState(false)
  const [verifyResultDialogOpen, setVerifyResultDialogOpen] = useState(false)
  const [assignLoading, setAssignLoading] = useState(false)
  const [verifyLoading, setVerifyLoading] = useState(false)
  const [winners, setWinners] = useState<Array<{user_id: string; position: number; prize_amount: number}>>([])
  const [verificationNotes, setVerificationNotes] = useState('')
  const [refreshData, setRefreshData] = useState(0)

  // Fetch pending tournaments and results
  useEffect(() => {
    async function fetchPendingData() {
      try {
        setLoading(true)
        
        // Fetch both pending tournaments and pending results for verification
        const [tournamentsResponse, resultsResponse] = await Promise.all([
          getTournaments({ status: 'active' }),
          getTournamentPendingResults(50, 0)
        ]);

        if (tournamentsResponse.error) {
          throw tournamentsResponse.error
        }

        if (resultsResponse.error) {
          console.error('Error fetching pending tournament results:', resultsResponse.error)
          toast({
            title: "Warning",
            description: `Could not load pending results: ${resultsResponse.error.message}`,
            variant: "destructive"
          })
          // Still continue with tournaments
        } else {
          console.log(`Loaded ${resultsResponse.data?.results?.length || 0} pending tournament results`)
        }

        setTournaments(tournamentsResponse.data || [])
        setPendingResults(resultsResponse.data?.results || [])

      } catch (error) {
        console.error('Error fetching pending tournament data:', error)
        toast({
          title: "Error Loading Data",
          description: error instanceof Error ? error.message : "Failed to load pending verification data",
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }

    fetchPendingData()
  }, [refreshData, toast])

  // Handle assign winners
  const handleAssignWinners = async () => {
    if (!selectedTournament || winners.length === 0) return

    try {
      setAssignLoading(true)
      
      const { success, error } = await assignTournamentWinners(
        selectedTournament.id,
        winners
      )

      if (error) throw error

      toast({
        title: "Winners Assigned",
        description: "The tournament winners have been assigned successfully.",
      })

      // Reset form and refresh data
      setAssignWinnerDialogOpen(false)
      setSelectedTournament(null)
      setWinners([])
      setVerificationNotes('')
      setRefreshData(prev => prev + 1)

    } catch (error) {
      console.error("Error assigning winners:", error)
      toast({
        title: "Assignment Failed",
        description: error instanceof Error ? error.message : "Failed to assign winners",
        variant: "destructive",
      })
    } finally {
      setAssignLoading(false)
    }
  }

  // Dialog handlers
  const openAssignWinnerDialog = (tournament: any) => {
    setSelectedTournament(tournament)
    setWinners([])
    setVerificationNotes('')
    setAssignWinnerDialogOpen(true)
  }

  const openVerifyResultDialog = (result: TournamentResultData) => {
    setSelectedResult(result)
    setVerificationNotes('')
    setVerifyResultDialogOpen(true)
  }

  // Handle verify result
  const handleVerifyResult = async (action: 'verified' | 'rejected') => {
    if (!selectedResult) return

    try {
      setVerifyLoading(true)
      
      const { success, error } = await verifyTournamentResult(
        selectedResult.id,
        action,
        verificationNotes.trim() || undefined
      )

      if (error) throw error

      toast({
        title: action === 'verified' ? "Result Approved" : "Result Rejected",
        description: action === 'verified' 
          ? "The tournament result has been approved successfully." 
          : "The tournament result has been rejected.",
      })

      // Reset form and refresh data
      setVerifyResultDialogOpen(false)
      setSelectedResult(null)
      setVerificationNotes('')
      setRefreshData(prev => prev + 1)

    } catch (error) {
      console.error("Error verifying result:", error)
      toast({
        title: "Verification Failed",
        description: error instanceof Error ? error.message : "Failed to verify result",
        variant: "destructive",
      })
    } finally {
      setVerifyLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading pending tournament data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tournament Verification</h1>
          <p className="text-muted-foreground">
            Review and verify tournament results, assign winners
          </p>
        </div>
      </div>

      <Tabs defaultValue="pending-results" className="space-y-4">
        <TabsList>
          <TabsTrigger value="pending-results" className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            Pending Results ({pendingResults.length})
          </TabsTrigger>
          <TabsTrigger value="pending-tournaments" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Active Tournaments ({tournaments.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending-results" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Pending Tournament Results
              </CardTitle>
              <CardDescription>
                Tournament results submitted by participants awaiting admin verification
              </CardDescription>
            </CardHeader>
            <CardContent>
              {pendingResults.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Pending Results</h3>
                  <p className="text-muted-foreground">All tournament results have been verified.</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Tournament</TableHead>
                        <TableHead>Participant</TableHead>
                        <TableHead>Position</TableHead>
                        <TableHead>Score</TableHead>
                        <TableHead>Submitted</TableHead>
                        <TableHead>Screenshots</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {pendingResults.map((result) => (
                        <TableRow key={result.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{(result as any).tournament_name}</div>
                              <div className="text-sm text-muted-foreground">
                                {gameNames[(result.tournaments?.game_id as number)] || 'Unknown Game'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{(result as any).user_name}</div>
                              <div className="text-sm text-muted-foreground">{result.users?.email}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">#{result.final_position}</Badge>
                          </TableCell>
                          <TableCell>{result.final_score || 'N/A'}</TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {format(new Date(result.submitted_at), 'MMM dd, yyyy')}
                              <div className="text-muted-foreground">
                                {format(new Date(result.submitted_at), 'HH:mm')}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {result.screenshot_urls && result.screenshot_urls.length > 0 ? (
                              <div className="flex items-center gap-1">
                                <ImageIcon className="h-4 w-4" />
                                <span className="text-sm">{result.screenshot_urls.length}</span>
                              </div>
                            ) : (
                              <span className="text-muted-foreground text-sm">None</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openVerifyResultDialog(result)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              Review
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending-tournaments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5" />
                Active Tournaments
              </CardTitle>
              <CardDescription>
                Tournaments that may need winner assignment after result verification
              </CardDescription>
            </CardHeader>
            <CardContent>
              {tournaments.length === 0 ? (
                <div className="text-center py-8">
                  <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Active Tournaments</h3>
                  <p className="text-muted-foreground">No tournaments are currently active.</p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Tournament</TableHead>
                        <TableHead>Game</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Participants</TableHead>
                        <TableHead>Prize Pool</TableHead>
                        <TableHead>End Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {tournaments.map((tournament) => (
                        <TableRow key={tournament.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{tournament.name}</div>
                              <div className="text-sm text-muted-foreground">{tournament.description}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {gameNames[tournament.game_id] || 'Unknown Game'}
                          </TableCell>
                          <TableCell>
                            <Badge variant={tournament.status === 'active' ? 'default' : 'secondary'}>
                              {tournament.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              <span>{tournament.current_participants || 0}/{tournament.max_participants}</span>
                            </div>
                          </TableCell>
                          <TableCell>${tournament.prize_pool || 0}</TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {tournament.end_date ? format(new Date(tournament.end_date), 'MMM dd, yyyy') : 'TBD'}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openAssignWinnerDialog(tournament)}
                            >
                              <Trophy className="h-4 w-4 mr-1" />
                              Assign Winners
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Verify Result Dialog */}
      <Dialog open={verifyResultDialogOpen} onOpenChange={setVerifyResultDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Verify Tournament Result</DialogTitle>
            <DialogDescription>
              Review the submitted result and approve or reject it.
            </DialogDescription>
          </DialogHeader>

          {selectedResult && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Tournament</Label>
                  <p className="text-sm text-muted-foreground">{(selectedResult as any).tournament_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Participant</Label>
                  <p className="text-sm text-muted-foreground">{(selectedResult as any).user_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Position</Label>
                  <p className="text-sm text-muted-foreground">#{selectedResult.final_position}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Score</Label>
                  <p className="text-sm text-muted-foreground">{selectedResult.final_score || 'N/A'}</p>
                </div>
              </div>

              {selectedResult.screenshot_urls && selectedResult.screenshot_urls.length > 0 && (
                <div>
                  <Label className="text-sm font-medium">Screenshots</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {selectedResult.screenshot_urls.map((url, index) => (
                      <div key={index} className="relative">
                        <img
                          src={url}
                          alt={`Screenshot ${index + 1}`}
                          className="w-full h-32 object-cover rounded border"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={() => window.open(url, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div>
                <Label htmlFor="verification-notes">Admin Notes (Optional)</Label>
                <Textarea
                  id="verification-notes"
                  placeholder="Add any notes about this verification..."
                  value={verificationNotes}
                  onChange={(e) => setVerificationNotes(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setVerifyResultDialogOpen(false)}
              disabled={verifyLoading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleVerifyResult('rejected')}
              disabled={verifyLoading}
            >
              {verifyLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <XCircle className="h-4 w-4 mr-2" />
              )}
              Reject
            </Button>
            <Button
              onClick={() => handleVerifyResult('verified')}
              disabled={verifyLoading}
            >
              {verifyLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <CheckCircle className="h-4 w-4 mr-2" />
              )}
              Approve
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assign Winners Dialog */}
      <Dialog open={assignWinnerDialogOpen} onOpenChange={setAssignWinnerDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Assign Tournament Winners</DialogTitle>
            <DialogDescription>
              Assign winners and prize distribution for this tournament.
            </DialogDescription>
          </DialogHeader>

          {selectedTournament && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Tournament</Label>
                  <p className="text-sm text-muted-foreground">{selectedTournament.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Prize Pool</Label>
                  <p className="text-sm text-muted-foreground">${selectedTournament.prize_pool || 0}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Winner Assignment</Label>
                <p className="text-sm text-muted-foreground mb-2">
                  Add winners with their positions and prize amounts
                </p>

                <div className="space-y-2">
                  {winners.map((winner, index) => (
                    <div key={index} className="flex gap-2 items-center">
                      <Select
                        value={winner.user_id}
                        onValueChange={(value) => {
                          const newWinners = [...winners]
                          newWinners[index].user_id = value
                          setWinners(newWinners)
                        }}
                      >
                        <SelectTrigger className="flex-1">
                          <SelectValue placeholder="Select participant" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="user1">User 1</SelectItem>
                          <SelectItem value="user2">User 2</SelectItem>
                          <SelectItem value="user3">User 3</SelectItem>
                        </SelectContent>
                      </Select>
                      <input
                        type="number"
                        placeholder="Position"
                        value={winner.position}
                        onChange={(e) => {
                          const newWinners = [...winners]
                          newWinners[index].position = parseInt(e.target.value) || 1
                          setWinners(newWinners)
                        }}
                        className="w-20 px-2 py-1 border rounded"
                      />
                      <input
                        type="number"
                        placeholder="Prize"
                        value={winner.prize_amount}
                        onChange={(e) => {
                          const newWinners = [...winners]
                          newWinners[index].prize_amount = parseFloat(e.target.value) || 0
                          setWinners(newWinners)
                        }}
                        className="w-24 px-2 py-1 border rounded"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newWinners = winners.filter((_, i) => i !== index)
                          setWinners(newWinners)
                        }}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}

                  <Button
                    variant="outline"
                    onClick={() => {
                      setWinners([...winners, { user_id: '', position: winners.length + 1, prize_amount: 0 }])
                    }}
                  >
                    Add Winner
                  </Button>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setAssignWinnerDialogOpen(false)}
              disabled={assignLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssignWinners}
              disabled={assignLoading || winners.length === 0}
            >
              {assignLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Trophy className="h-4 w-4 mr-2" />
              )}
              Assign Winners
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
