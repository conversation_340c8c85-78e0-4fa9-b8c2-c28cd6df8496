import 'package:flutter/foundation.dart';
import 'package:wiggyz_app/services/match_history_service.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';

/// Enhanced provider for managing match history state and operations
class MatchHistoryProvider extends ChangeNotifier {
  final MatchHistoryService _matchHistoryService;
  
  MatchHistoryProvider(AuthProvider authProvider) 
      : _matchHistoryService = MatchHistoryService(authProvider);

  // Match history state
  List<MatchHistoryItem> _matchHistory = [];
  List<MatchHistoryItem> get matchHistory => _matchHistory;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  bool _isLoadingMore = false;
  bool get isLoadingMore => _isLoadingMore;

  String? _error;
  String? get error => _error;

  bool _hasMore = true;
  bool get hasMore => _hasMore;

  int _currentOffset = 0;
  static const int _pageSize = 20;

  // Filter state
  String _selectedStatus = 'all';
  String get selectedStatus => _selectedStatus;

  String _selectedMatchType = 'all';
  String get selectedMatchType => _selectedMatchType;

  // Statistics state
  MatchStatistics? _statistics;
  MatchStatistics? get statistics => _statistics;

  bool _isLoadingStatistics = false;
  bool get isLoadingStatistics => _isLoadingStatistics;

  String? _statisticsError;
  String? get statisticsError => _statisticsError;

  // Active matches state
  List<MatchHistoryItem> _activeMatches = [];
  List<MatchHistoryItem> get activeMatches => _activeMatches;

  bool _isLoadingActiveMatches = false;
  bool get isLoadingActiveMatches => _isLoadingActiveMatches;

  String? _activeMatchesError;
  String? get activeMatchesError => _activeMatchesError;

  /// Get filtered match history based on current filters
  List<MatchHistoryItem> get filteredMatchHistory {
    List<MatchHistoryItem> filtered = _matchHistory;
    final originalCount = filtered.length;

    // Apply status filter
    if (_selectedStatus != 'all') {
      switch (_selectedStatus.toLowerCase()) {
        case 'won':
          // Filter for matches where user is the winner
          filtered = filtered.where((m) => m.isWinner && m.isCompleted).toList();
          break;
        case 'lost':
          // Filter for completed matches where user is not the winner
          filtered = filtered.where((m) => m.isCompleted && !m.isWinner).toList();
          break;
        case 'pending':
          // Filter for matches that are pending (scheduled or pending verification)
          filtered = filtered.where((m) => m.isPending).toList();
          break;
        case 'active':
          // Filter for matches that are currently active/live
          filtered = filtered.where((m) => m.isActive).toList();
          break;
        case 'completed':
          // Filter for all completed matches (won, lost, or cancelled)
          filtered = filtered.where((m) => m.isCompleted).toList();
          break;
      }
    }

    // Apply match type filter
    if (_selectedMatchType != 'all') {
      filtered = filtered.where((m) => m.matchType == _selectedMatchType).toList();
    }

    debugPrint('Filtered matches: ${filtered.length}/$originalCount (status: $_selectedStatus, type: $_selectedMatchType)');

    return filtered;
  }

  /// Load initial match history
  Future<void> loadMatchHistory({bool refresh = false}) async {
    if (_isLoading) return;

    if (refresh) {
      _currentOffset = 0;
      _hasMore = true;
      _matchHistory.clear();
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Map frontend filter values to backend status values
      String? backendStatus = _mapFilterToBackendStatus(_selectedStatus);

      final response = await _matchHistoryService.getMatchHistory(
        limit: _pageSize,
        offset: _currentOffset,
        status: backendStatus,
        matchType: _selectedMatchType == 'all' ? null : _selectedMatchType,
        forceRefresh: refresh,
      );

      if (refresh) {
        _matchHistory = response.matches;
      } else {
        _matchHistory.addAll(response.matches);
      }

      _hasMore = response.hasMore;
      _currentOffset += response.matches.length;

      debugPrint('Loaded ${response.matches.length} matches. Total: ${_matchHistory.length}');
    } catch (e) {
      _error = e.toString();
      debugPrint('Error loading match history: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load more match history (pagination)
  Future<void> loadMoreMatches() async {
    if (_isLoadingMore || !_hasMore) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      // Map frontend filter values to backend status values
      String? backendStatus = _mapFilterToBackendStatus(_selectedStatus);

      final response = await _matchHistoryService.getMatchHistory(
        limit: _pageSize,
        offset: _currentOffset,
        status: backendStatus,
        matchType: _selectedMatchType == 'all' ? null : _selectedMatchType,
      );

      _matchHistory.addAll(response.matches);
      _hasMore = response.hasMore;
      _currentOffset += response.matches.length;

      debugPrint('Loaded ${response.matches.length} more matches. Total: ${_matchHistory.length}');
    } catch (e) {
      _error = e.toString();
      debugPrint('Error loading more matches: $e');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// Update status filter and reload data
  Future<void> updateStatusFilter(String status) async {
    if (_selectedStatus == status) return;

    debugPrint('Updating status filter from $_selectedStatus to $status');
    _selectedStatus = status;
    await loadMatchHistory(refresh: true);
  }

  /// Update match type filter and reload data
  Future<void> updateMatchTypeFilter(String matchType) async {
    if (_selectedMatchType == matchType) return;

    _selectedMatchType = matchType;
    await loadMatchHistory(refresh: true);
  }

  /// Map frontend filter values to backend status values
  /// Returns null for filters that should be handled client-side only
  String? _mapFilterToBackendStatus(String frontendFilter) {
    String? backendStatus;

    switch (frontendFilter.toLowerCase()) {
      case 'all':
        backendStatus = null; // No filter
        break;
      case 'completed':
        backendStatus = 'completed'; // Direct mapping
        break;
      case 'won':
      case 'lost':
        // These are derived from isWinner + isCompleted, not actual statuses
        // Load all completed matches and filter client-side
        backendStatus = 'completed';
        break;
      case 'active':
        // Backend uses 'live' and 'in_progress' for active matches
        // Load all and filter client-side using isActive property
        backendStatus = null;
        break;
      case 'pending':
        // Backend uses 'scheduled' and 'pending_verification' for pending matches
        // Load all and filter client-side using isPending property
        backendStatus = null;
        break;
      default:
        backendStatus = null;
        break;
    }

    debugPrint('Mapping frontend filter "$frontendFilter" to backend status: ${backendStatus ?? "null (client-side filtering)"}');
    return backendStatus;
  }

  /// Load match statistics
  Future<void> loadStatistics() async {
    if (_isLoadingStatistics) return;

    _isLoadingStatistics = true;
    _statisticsError = null;
    notifyListeners();

    try {
      _statistics = await _matchHistoryService.getMatchStatistics(forceRefresh: true);
      debugPrint('Loaded match statistics: ${_statistics?.totalMatches} total matches');
    } catch (e) {
      _statisticsError = e.toString();
      debugPrint('Error loading statistics: $e');
    } finally {
      _isLoadingStatistics = false;
      notifyListeners();
    }
  }

  /// Load active matches
  Future<void> loadActiveMatches() async {
    if (_isLoadingActiveMatches) return;

    _isLoadingActiveMatches = true;
    _activeMatchesError = null;
    notifyListeners();

    try {
      _activeMatches = await _matchHistoryService.getActiveMatches();
      debugPrint('Loaded ${_activeMatches.length} active matches');
    } catch (e) {
      _activeMatchesError = e.toString();
      debugPrint('Error loading active matches: $e');
    } finally {
      _isLoadingActiveMatches = false;
      notifyListeners();
    }
  }

  /// Refresh all data
  Future<void> refreshAll() async {
    await Future.wait([
      loadMatchHistory(refresh: true),
      loadStatistics(),
      loadActiveMatches(),
    ]);
  }

  /// Clear all data
  void clearData() {
    _matchHistory.clear();
    _activeMatches.clear();
    _statistics = null;
    _error = null;
    _statisticsError = null;
    _activeMatchesError = null;
    _currentOffset = 0;
    _hasMore = true;
    _selectedStatus = 'all';
    _selectedMatchType = 'all';

    // Clear service cache as well
    MatchHistoryService.clearCache();

    notifyListeners();
  }

  @override
  void dispose() {
    _matchHistoryService.dispose();
    super.dispose();
  }
}
