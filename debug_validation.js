#!/usr/bin/env node

/**
 * Debug Validation Issues
 * Test the exact validation logic that's failing
 */

const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:5000/api/v1';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Advanture101$'
};

let authToken = null;

async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${BASE_URL}/auth/login`, TEST_USER);
    
    if (response.data && response.data.access_token) {
      authToken = response.data.access_token;
      console.log('✅ Login successful');
      return true;
    }
    return false;
  } catch (error) {
    console.log('❌ Login failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testValidationWithDifferentInputs() {
  console.log('\n🧪 Testing validation with different inputs...\n');
  
  const testCases = [
    {
      name: 'Simple valid message',
      data: {
        subject: 'Test Subject',
        category: 'general_inquiry',
        message: 'This is a simple test message.'
      }
    },
    {
      name: 'Short subject (should fail)',
      data: {
        subject: 'Hi',
        category: 'general_inquiry',
        message: 'This is a test message with short subject.'
      }
    },
    {
      name: 'Short message (should fail)',
      data: {
        subject: 'Test Subject',
        category: 'general_inquiry',
        message: 'Short'
      }
    },
    {
      name: 'Invalid category (should fail)',
      data: {
        subject: 'Test Subject',
        category: 'invalid_category',
        message: 'This is a test message with invalid category.'
      }
    },
    {
      name: 'Empty fields (should fail)',
      data: {
        subject: '',
        category: '',
        message: ''
      }
    },
    {
      name: 'Message with special characters',
      data: {
        subject: 'Test with special chars!',
        category: 'technical_support',
        message: 'This message contains special characters: @#$%^&*()_+-=[]{}|;:,.<>?'
      }
    },
    {
      name: 'Message with quotes',
      data: {
        subject: 'Test with quotes',
        category: 'general_inquiry',
        message: 'This message contains "quotes" and \'apostrophes\' which should be allowed.'
      }
    },
    {
      name: 'Message with HTML-like content',
      data: {
        subject: 'HTML content test',
        category: 'technical_support',
        message: 'This message contains <script>alert("test")</script> and other HTML-like content.'
      }
    },
    {
      name: 'Long valid message',
      data: {
        subject: 'This is a longer subject line to test length validation',
        category: 'bug_report',
        message: 'This is a much longer message to test the validation system. '.repeat(10)
      }
    },
    {
      name: 'Message with repeated characters',
      data: {
        subject: 'Repeated chars test',
        category: 'general_inquiry',
        message: 'This message has some repeated characters like aaaaaaa but not excessive.'
      }
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`🔍 Testing: ${testCase.name}`);
      
      const response = await axios.post(
        `${BASE_URL}/support/messages`,
        testCase.data,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.status === 201) {
        console.log(`   ✅ PASSED - Message created successfully`);
      } else {
        console.log(`   ❓ Unexpected status: ${response.status}`);
      }
      
    } catch (error) {
      const status = error.response?.status;
      const errorMsg = error.response?.data?.error || error.message;
      const details = error.response?.data?.details;
      
      if (status === 400) {
        console.log(`   ❌ FAILED - Validation error: ${errorMsg}`);
        if (details && Array.isArray(details)) {
          details.forEach(detail => console.log(`      - ${detail}`));
        } else if (details) {
          console.log(`      Details: ${JSON.stringify(details)}`);
        }
      } else {
        console.log(`   ❌ FAILED - ${status}: ${errorMsg}`);
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

async function runDebugTest() {
  console.log('🚀 Starting Validation Debug Test\n');
  
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }

  await testValidationWithDifferentInputs();
  
  console.log('\n📊 Debug Test Complete');
  console.log('=====================================');
  console.log('Check the results above to identify which validation rules are failing.');
}

// Run the debug test
runDebugTest().catch(console.error);
