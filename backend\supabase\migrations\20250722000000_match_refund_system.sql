-- Match Refund System Migration
-- Date: 2025-07-22
-- Description: Add refund functionality for expired matches with no participants

-- ==================== ADD REFUND FIELDS TO MATCHES TABLE ====================

-- Add refund tracking fields to matches table
ALTER TABLE matches 
ADD COLUMN IF NOT EXISTS refund_processed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS refund_amount DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS refund_timestamp TIMESTAMP WITH TIME ZONE;

-- ==================== UPDATE AUDIT LOG ACTION TYPES ====================

-- Update the match_audit_log table to include creator_refunded action type
ALTER TABLE match_audit_log 
DROP CONSTRAINT IF EXISTS match_audit_log_action_type_check;

ALTER TABLE match_audit_log 
ADD CONSTRAINT match_audit_log_action_type_check 
CHECK (action_type IN (
  'auto_finalized', 
  'deadline_expired', 
  'participant_defaulted',
  'auto_winner_assigned',
  'auto_loser_assigned',
  'match_auto_completed',
  'deadline_notification_sent',
  'manual_admin_override',
  'creator_refunded'
));

-- ==================== INDEXES FOR PERFORMANCE ====================

-- Add indexes for refund queries
CREATE INDEX IF NOT EXISTS idx_matches_refund_processed ON matches(refund_processed);
CREATE INDEX IF NOT EXISTS idx_matches_refund_timestamp ON matches(refund_timestamp) WHERE refund_timestamp IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_matches_entry_fee_refund ON matches(entry_fee, refund_processed) WHERE entry_fee > 0;

-- ==================== COMMENTS FOR DOCUMENTATION ====================

COMMENT ON COLUMN matches.refund_processed IS 'Indicates if a refund has been processed for this match';
COMMENT ON COLUMN matches.refund_amount IS 'Amount refunded to the match creator (should match entry_fee when refunded)';
COMMENT ON COLUMN matches.refund_timestamp IS 'Timestamp when the refund was processed';

-- ==================== FUNCTIONS FOR REFUND LOGIC ====================

-- Function to check if a match is eligible for creator refund
CREATE OR REPLACE FUNCTION is_match_eligible_for_creator_refund(match_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
  match_record RECORD;
  participant_count INTEGER;
BEGIN
  -- Get match details
  SELECT 
    created_by, 
    entry_fee, 
    refund_processed,
    status
  INTO match_record
  FROM matches 
  WHERE id = match_id_param;
  
  -- Check if match exists
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Check if refund already processed
  IF match_record.refund_processed = TRUE THEN
    RETURN FALSE;
  END IF;
  
  -- Check if match has entry fee
  IF match_record.entry_fee IS NULL OR match_record.entry_fee <= 0 THEN
    RETURN FALSE;
  END IF;
  
  -- Count participants (excluding creator)
  SELECT COUNT(*)
  INTO participant_count
  FROM match_participants 
  WHERE match_id = match_id_param 
    AND participant_type = 'player'
    AND user_id != match_record.created_by;
  
  -- Return true if no participants joined
  RETURN participant_count = 0;
END;
$$ LANGUAGE plpgsql;

-- Function to process creator refund
CREATE OR REPLACE FUNCTION process_creator_refund(match_id_param UUID)
RETURNS JSONB AS $$
DECLARE
  match_record RECORD;
  refund_result JSONB;
BEGIN
  -- Check eligibility first
  IF NOT is_match_eligible_for_creator_refund(match_id_param) THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Match not eligible for creator refund'
    );
  END IF;
  
  -- Get match details
  SELECT 
    created_by, 
    entry_fee
  INTO match_record
  FROM matches 
  WHERE id = match_id_param;
  
  -- Update match with refund information
  UPDATE matches 
  SET 
    refund_processed = TRUE,
    refund_amount = match_record.entry_fee,
    refund_timestamp = NOW(),
    updated_at = NOW()
  WHERE id = match_id_param;
  
  RETURN jsonb_build_object(
    'success', true,
    'refund_amount', match_record.entry_fee,
    'creator_id', match_record.created_by,
    'timestamp', NOW()
  );
END;
$$ LANGUAGE plpgsql;

-- ==================== ROW LEVEL SECURITY ====================

-- No additional RLS policies needed as refund fields inherit from matches table policies
