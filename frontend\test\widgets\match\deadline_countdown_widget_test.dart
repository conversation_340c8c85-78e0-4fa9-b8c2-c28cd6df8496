import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'package:wiggyz_app/widgets/match/deadline_countdown_widget.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';

// Generate mocks
@GenerateMocks([http.Client])
import 'deadline_countdown_widget_test.mocks.dart';

class MockAuthProvider extends Mock implements AuthProvider {
  @override
  Future<String?> get token async => 'test-token';
}

void main() {
  group('DeadlineCountdownWidget', () {
    late MockClient mockClient;
    late MockAuthProvider mockAuthProvider;

    setUp(() {
      mockClient = MockClient();
      mockAuthProvider = MockAuthProvider();
    });

    Widget createTestWidget({
      required String matchId,
      VoidCallback? onDeadlineExpired,
      VoidCallback? onSubmitPressed,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
            child: DeadlineCountdownWidget(
              matchId: matchId,
              onDeadlineExpired: onDeadlineExpired,
              onSubmitPressed: onSubmitPressed,
            ),
          ),
        ),
      );
    }

    group('Loading State', () {
      testWidgets('should show loading indicator initially', (WidgetTester tester) async {
        // Mock a delayed response
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => Future.delayed(
                const Duration(seconds: 1),
                () => http.Response('{}', 200)
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should show error message on network failure', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenThrow(Exception('Network error'));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('Network error: Exception: Network error'), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
      });

      testWidgets('should retry on error button press', (WidgetTester tester) async {
        // First call fails, second succeeds
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenThrow(Exception('Network error'))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': null,
                  'timeRemaining': 0,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': false,
                  'participantsSubmitted': 0,
                  'totalParticipants': 0,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'normal'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        // Tap retry button
        await tester.tap(find.text('Retry'));
        await tester.pumpAndSettle();

        // Should no longer show error
        expect(find.text('Network error'), findsNothing);
      });

      testWidgets('should handle 403 forbidden gracefully', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response('{"error": "Forbidden"}', 403));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        // Widget should be hidden for non-participants
        expect(find.byType(DeadlineCountdownWidget), findsOneWidget);
        expect(find.byType(Container), findsNothing); // No content container
      });
    });

    group('No Deadline', () {
      testWidgets('should hide widget when no deadline is set', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': null,
                  'timeRemaining': 0,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': false,
                  'participantsSubmitted': 0,
                  'totalParticipants': 0,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'normal'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.byType(SizedBox), findsOneWidget); // SizedBox.shrink()
      });
    });

    group('Results Already Submitted', () {
      testWidgets('should show success message when user already submitted', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(minutes: 30)).toIso8601String(),
                  'timeRemaining': 30 * 60 * 1000,
                  'hasExpired': false,
                  'userHasSubmitted': true,
                  'canSubmitResults': false,
                  'participantsSubmitted': 1,
                  'totalParticipants': 2,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'normal'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('✅ Results submitted successfully'), findsOneWidget);
        expect(find.byIcon(Icons.check_circle), findsOneWidget);
      });
    });

    group('Warning Levels', () {
      testWidgets('should show normal state with green color', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(minutes: 30)).toIso8601String(),
                  'timeRemaining': 30 * 60 * 1000,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': true,
                  'participantsSubmitted': 0,
                  'totalParticipants': 2,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'normal'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('Submit results by:'), findsOneWidget);
        expect(find.byIcon(Icons.schedule), findsOneWidget);
      });

      testWidgets('should show warning state with orange color', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(minutes: 5)).toIso8601String(),
                  'timeRemaining': 5 * 60 * 1000,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': true,
                  'participantsSubmitted': 0,
                  'totalParticipants': 2,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'warning'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('⚠️ Submit results soon'), findsOneWidget);
        expect(find.byIcon(Icons.access_time), findsOneWidget);
      });

      testWidgets('should show critical state with red color and blinking', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(minutes: 1)).toIso8601String(),
                  'timeRemaining': 1 * 60 * 1000,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': true,
                  'participantsSubmitted': 0,
                  'totalParticipants': 2,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'critical'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('🚨 URGENT: Submit results now!'), findsOneWidget);
        expect(find.byIcon(Icons.warning), findsOneWidget);
        expect(find.byType(AnimatedBuilder), findsOneWidget); // Blinking animation
      });

      testWidgets('should show expired state', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().subtract(const Duration(minutes: 5)).toIso8601String(),
                  'timeRemaining': 0,
                  'hasExpired': true,
                  'userHasSubmitted': false,
                  'canSubmitResults': false,
                  'participantsSubmitted': 0,
                  'totalParticipants': 2,
                  'matchStatus': 'completed',
                  'deadlineWarningLevel': 'expired'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('❌ Deadline passed. Results auto-finalized.'), findsOneWidget);
        expect(find.byIcon(Icons.cancel), findsOneWidget);
      });
    });

    group('Submit Button', () {
      testWidgets('should show submit button when user can submit', (WidgetTester tester) async {
        bool submitPressed = false;

        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(minutes: 5)).toIso8601String(),
                  'timeRemaining': 5 * 60 * 1000,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': true,
                  'participantsSubmitted': 0,
                  'totalParticipants': 2,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'warning'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(
          matchId: 'test-match',
          onSubmitPressed: () => submitPressed = true,
        ));
        await tester.pumpAndSettle();

        expect(find.text('Submit Now'), findsOneWidget);

        await tester.tap(find.text('Submit Now'));
        expect(submitPressed, isTrue);
      });

      testWidgets('should not show submit button when user cannot submit', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(minutes: 5)).toIso8601String(),
                  'timeRemaining': 5 * 60 * 1000,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': false,
                  'participantsSubmitted': 0,
                  'totalParticipants': 2,
                  'matchStatus': 'completed',
                  'deadlineWarningLevel': 'warning'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('Submit Now'), findsNothing);
      });
    });

    group('Time Formatting', () {
      testWidgets('should format time correctly for hours:minutes:seconds', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(hours: 2, minutes: 30, seconds: 45)).toIso8601String(),
                  'timeRemaining': (2 * 60 * 60 + 30 * 60 + 45) * 1000,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': true,
                  'participantsSubmitted': 0,
                  'totalParticipants': 2,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'normal'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('02:30:45'), findsOneWidget);
      });

      testWidgets('should format time correctly for minutes:seconds only', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(minutes: 15, seconds: 30)).toIso8601String(),
                  'timeRemaining': (15 * 60 + 30) * 1000,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': true,
                  'participantsSubmitted': 0,
                  'totalParticipants': 2,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'normal'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('15:30'), findsOneWidget);
      });
    });

    group('Participant Count', () {
      testWidgets('should show participant submission count', (WidgetTester tester) async {
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(minutes: 30)).toIso8601String(),
                  'timeRemaining': 30 * 60 * 1000,
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': true,
                  'participantsSubmitted': 2,
                  'totalParticipants': 4,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'normal'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(matchId: 'test-match'));
        await tester.pumpAndSettle();

        expect(find.text('2/4 submitted'), findsOneWidget);
        expect(find.byIcon(Icons.people), findsOneWidget);
      });
    });

    group('Deadline Expiry Callback', () {
      testWidgets('should call onDeadlineExpired when deadline passes', (WidgetTester tester) async {
        bool deadlineExpiredCalled = false;

        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
                json.encode({
                  'matchId': 'test-match',
                  'deadline': DateTime.now().add(const Duration(seconds: 1)).toIso8601String(),
                  'timeRemaining': 1000, // 1 second
                  'hasExpired': false,
                  'userHasSubmitted': false,
                  'canSubmitResults': true,
                  'participantsSubmitted': 0,
                  'totalParticipants': 2,
                  'matchStatus': 'live',
                  'deadlineWarningLevel': 'critical'
                }),
                200
            ));

        await tester.pumpWidget(createTestWidget(
          matchId: 'test-match',
          onDeadlineExpired: () => deadlineExpiredCalled = true,
        ));
        await tester.pumpAndSettle();

        // Wait for deadline to expire
        await tester.pump(const Duration(seconds: 2));

        expect(deadlineExpiredCalled, isTrue);
      });
    });
  });
}
