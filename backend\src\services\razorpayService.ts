/**
 * Razorpay Service - Secure payment processing with Razorpay
 * Handles order creation, payment verification, and webhook processing
 */
import <PERSON><PERSON>pay from 'razorpay';
import crypto from 'crypto';
import { logger } from '../utils/logger';

// Environment validation
const requiredEnvVars = ['RAZORPAY_KEY_ID', 'RAZORPAY_KEY_SECRET', 'RAZORPAY_WEBHOOK_SECRET'];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

// Initialize Razorpay instance
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET!,
});

// Payment configuration
const PAYMENT_CONFIG = {
  timeout: parseInt(process.env.PAYMENT_TIMEOUT_MINUTES || '15') * 60 * 1000, // Convert to milliseconds
  maxAmount: parseInt(process.env.PAYMENT_MAX_AMOUNT || '100000'), // In paise (₹1000)
  minAmount: parseInt(process.env.PAYMENT_MIN_AMOUNT || '2500'), // In paise (₹25)
  currency: 'INR',
  supportedMethods: ['card', 'netbanking', 'wallet', 'upi'],
};

export interface RazorpayOrderOptions {
  amount: number; // Amount in paise
  currency?: string;
  receipt: string;
  notes?: Record<string, string>;
  userId: string;
  description?: string;
}

export interface RazorpayOrder {
  id: string;
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  status: string;
  attempts: number;
  notes: Record<string, string>;
  created_at: number;
}

export interface PaymentVerificationData {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
}

export interface WebhookEvent {
  entity: string;
  account_id: string;
  event: string;
  contains: string[];
  payload: {
    payment?: {
      entity: any;
    };
    order?: {
      entity: any;
    };
  };
  created_at: number;
}

export interface RazorpayPayoutOptions {
  amount: number; // Amount in paise
  currency?: string;
  account_details: {
    account_number: string;
    ifsc_code: string;
    account_holder_name: string;
    bank_name?: string;
  };
  mode?: string; // IMPS, NEFT, RTGS, UPI
  purpose?: string;
  queue_if_low_balance?: boolean;
  reference_id?: string;
}

export interface RazorpayPayout {
  id: string;
  entity: string;
  fund_account_id: string;
  amount: number;
  currency: string;
  notes: Record<string, string>;
  fees: number;
  tax: number;
  status: string;
  purpose: string;
  utr: string | null;
  mode: string;
  reference_id: string;
  narration: string;
  batch_id: string | null;
  failure_reason: string | null;
  created_at: number;
}

class RazorpayService {
  /**
   * Create a new order for payment
   */
  async createOrder(options: RazorpayOrderOptions): Promise<RazorpayOrder> {
    try {
      // Validate amount
      if (options.amount < PAYMENT_CONFIG.minAmount || options.amount > PAYMENT_CONFIG.maxAmount) {
        throw new Error(`Amount must be between ₹${PAYMENT_CONFIG.minAmount/100} and ₹${PAYMENT_CONFIG.maxAmount/100}`);
      }

      const orderOptions = {
        amount: options.amount,
        currency: options.currency || PAYMENT_CONFIG.currency,
        receipt: options.receipt,
        notes: {
          user_id: options.userId,
          description: options.description || 'Wallet top-up',
          ...options.notes,
        },
      };

      logger.info(`Creating Razorpay order for user ${options.userId} - Amount: ${options.amount}, Receipt: ${options.receipt}, Currency: ${orderOptions.currency}`);

      const order = await razorpay.orders.create(orderOptions);
      
      logger.info(`Razorpay order created successfully - Order ID: ${order.id}, User ID: ${options.userId}, Amount: ${order.amount}`);

      return order as RazorpayOrder;
    } catch (error: any) {
      logger.error(`Failed to create Razorpay order for user ${options.userId}: ${error.message} - Amount: ${options.amount}, Receipt: ${options.receipt}`);
      throw new Error(`Failed to create payment order: ${error.message}`);
    }
  }

  /**
   * Verify payment signature
   */
  verifyPaymentSignature(data: PaymentVerificationData): boolean {
    try {
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = data;

      // Create signature string
      const signatureString = `${razorpay_order_id}|${razorpay_payment_id}`;
      
      // Generate expected signature
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
        .update(signatureString)
        .digest('hex');

      // Compare signatures
      const isValid = crypto.timingSafeEqual(
        Buffer.from(expectedSignature, 'hex'),
        Buffer.from(razorpay_signature, 'hex')
      );

      logger.info(`Payment signature verification - Order ID: ${razorpay_order_id}, Payment ID: ${razorpay_payment_id}, Valid: ${isValid}`);

      return isValid;
    } catch (error: any) {
      logger.error(`Payment signature verification failed: ${error.message} - Order ID: ${data.razorpay_order_id}, Payment ID: ${data.razorpay_payment_id}`);
      return false;
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET!)
        .update(body)
        .digest('hex');

      const isValid = crypto.timingSafeEqual(
        Buffer.from(expectedSignature, 'hex'),
        Buffer.from(signature, 'hex')
      );

      logger.info(`Webhook signature verification - Valid: ${isValid}`);
      return isValid;
    } catch (error: any) {
      logger.error(`Webhook signature verification failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Fetch payment details
   */
  async fetchPayment(paymentId: string) {
    try {
      const payment = await razorpay.payments.fetch(paymentId);
      logger.info(`Fetched payment details - Payment ID: ${paymentId}, Status: ${payment.status}, Amount: ${payment.amount}`);
      return payment;
    } catch (error: any) {
      logger.error(`Failed to fetch payment details: ${error.message} - Payment ID: ${paymentId}`);
      throw new Error(`Failed to fetch payment: ${error.message}`);
    }
  }

  /**
   * Fetch order details
   */
  async fetchOrder(orderId: string) {
    try {
      const order = await razorpay.orders.fetch(orderId);
      logger.info(`Fetched order details - Order ID: ${orderId}, Status: ${order.status}, Amount: ${order.amount}`);
      return order;
    } catch (error: any) {
      logger.error(`Failed to fetch order details: ${error.message} - Order ID: ${orderId}`);
      throw new Error(`Failed to fetch order: ${error.message}`);
    }
  }

  /**
   * Process webhook event
   */
  async processWebhookEvent(event: WebhookEvent): Promise<void> {
    try {
      logger.info(`Processing webhook event - Event: ${event.event}, Entity: ${event.entity}, Account ID: ${event.account_id}`);

      switch (event.event) {
        case 'payment.authorized':
          await this.handlePaymentAuthorized(event);
          break;
        case 'payment.captured':
          await this.handlePaymentCaptured(event);
          break;
        case 'payment.failed':
          await this.handlePaymentFailed(event);
          break;
        case 'order.paid':
          await this.handleOrderPaid(event);
          break;
        default:
          logger.info(`Unhandled webhook event: ${event.event}`);
      }
    } catch (error: any) {
      logger.error(`Failed to process webhook event: ${error.message} - Event: ${event.event}`);
      throw error;
    }
  }

  private async handlePaymentAuthorized(event: WebhookEvent): Promise<void> {
    const payment = event.payload.payment?.entity;
    if (payment) {
      logger.info(`Payment authorized - Payment ID: ${payment.id}, Order ID: ${payment.order_id}, Amount: ${payment.amount}`);
      // Handle payment authorization logic here
    }
  }

  private async handlePaymentCaptured(event: WebhookEvent): Promise<void> {
    const payment = event.payload.payment?.entity;
    if (payment) {
      logger.info(`Payment captured - Payment ID: ${payment.id}, Order ID: ${payment.order_id}, Amount: ${payment.amount}`);
      // Handle payment capture logic here
    }
  }

  private async handlePaymentFailed(event: WebhookEvent): Promise<void> {
    const payment = event.payload.payment?.entity;
    if (payment) {
      logger.error(`Payment failed - Payment ID: ${payment.id}, Order ID: ${payment.order_id}, Amount: ${payment.amount}, Error: ${payment.error_code} - ${payment.error_description}`);
      // Handle payment failure logic here
    }
  }

  private async handleOrderPaid(event: WebhookEvent): Promise<void> {
    const order = event.payload.order?.entity;
    if (order) {
      logger.info(`Order paid - Order ID: ${order.id}, Amount: ${order.amount}, Status: ${order.status}`);
      // Handle order paid logic here
    }
  }

  /**
   * Create a payout for withdrawal
   */
  async createPayout(options: RazorpayPayoutOptions): Promise<RazorpayPayout> {
    try {
      // Validate amount
      if (options.amount < PAYMENT_CONFIG.minAmount || options.amount > PAYMENT_CONFIG.maxAmount) {
        throw new Error(`Payout amount must be between ₹${PAYMENT_CONFIG.minAmount/100} and ₹${PAYMENT_CONFIG.maxAmount/100}`);
      }

      // First, create a fund account for the beneficiary
      const fundAccountData = {
        account_type: 'bank_account',
        bank_account: {
          name: options.account_details.account_holder_name,
          ifsc: options.account_details.ifsc_code,
          account_number: options.account_details.account_number,
        },
        contact: {
          name: options.account_details.account_holder_name,
          email: '<EMAIL>', // This should come from user data
          contact: '**********', // This should come from user data
          type: 'customer',
        }
      };

      logger.info(`Creating Razorpay fund account for payout - Account: ${options.account_details.account_number}, IFSC: ${options.account_details.ifsc_code}`);

      // Note: In a real implementation, you would create the fund account first
      // const fundAccount = await razorpay.fundAccount.create(fundAccountData);

      // For now, we'll simulate the payout creation
      // In production, you would use: await razorpay.payouts.create(payoutData);
      const simulatedPayout: RazorpayPayout = {
        id: `pout_${crypto.randomBytes(16).toString('hex')}`,
        entity: 'payout',
        fund_account_id: `fa_${crypto.randomBytes(16).toString('hex')}`,
        amount: options.amount,
        currency: options.currency || 'INR',
        notes: {
          reference_id: options.reference_id || '',
          purpose: options.purpose || 'refund'
        },
        fees: Math.round(options.amount * 0.02), // 2% fee simulation
        tax: Math.round(options.amount * 0.0036), // 18% GST on fees
        status: 'processing',
        purpose: options.purpose || 'refund',
        utr: null,
        mode: options.mode || 'IMPS',
        reference_id: options.reference_id || '',
        narration: `Withdrawal to ${options.account_details.account_holder_name}`,
        batch_id: null,
        failure_reason: null,
        created_at: Math.floor(Date.now() / 1000)
      };

      logger.info(`Razorpay payout created successfully - Payout ID: ${simulatedPayout.id}, Amount: ${simulatedPayout.amount}`);

      return simulatedPayout;
    } catch (error: any) {
      logger.error(`Failed to create Razorpay payout: ${error.message} - Amount: ${options.amount}`);
      throw new Error(`Payout creation failed: ${error.message}`);
    }
  }

  /**
   * Get payment configuration
   */
  getPaymentConfig() {
    return {
      ...PAYMENT_CONFIG,
      keyId: process.env.RAZORPAY_KEY_ID, // Safe to expose key_id to frontend
    };
  }
}

export const razorpayService = new RazorpayService();
export default razorpayService;
