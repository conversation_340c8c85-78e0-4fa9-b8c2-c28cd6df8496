/**
 * Comprehensive test suite for the enhanced referral system
 * Tests fraud detection, referral processing, admin controls, and integration
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import referralService from '../services/referralService';
import referralFraudService from '../services/referralFraudService';
import referralPointsService from '../services/referralPointsService';
import { supabase } from '../../../config/supabase';

// Mock Supabase
jest.mock('../../../config/supabase');
const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('Enhanced Referral System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Referral Points Service', () => {
    it('should award referral points correctly', async () => {
      const mockRpcResponse = { data: true, error: null };
      mockSupabase.rpc.mockResolvedValue(mockRpcResponse);

      const result = await referralPointsService.awardPoints(
        'user-123',
        25,
        'referral_reward',
        'referral-456',
        'Test referral reward'
      );

      expect(result).toBe(true);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('award_referral_points', {
        p_user_id: 'user-123',
        p_points: 25,
        p_source_type: 'referral_reward',
        p_source_id: 'referral-456',
        p_description: 'Test referral reward'
      });
    });

    it('should check sufficient points correctly', async () => {
      const mockBalance = {
        totalPoints: 50,
        availablePoints: 30,
        usedPoints: 20,
        earnedFromReferrals: 50
      };
      
      jest.spyOn(referralPointsService, 'getUserBalance').mockResolvedValue(mockBalance);

      const hasSufficient = await referralPointsService.hasSufficientPoints('user-123', 25);
      const hasInsufficient = await referralPointsService.hasSufficientPoints('user-123', 35);

      expect(hasSufficient).toBe(true);
      expect(hasInsufficient).toBe(false);
    });

    it('should use points for match entry correctly', async () => {
      const mockBalance = {
        totalPoints: 50,
        availablePoints: 30,
        usedPoints: 20,
        earnedFromReferrals: 50
      };
      
      jest.spyOn(referralPointsService, 'getUserBalance').mockResolvedValue(mockBalance);
      mockSupabase.rpc.mockResolvedValue({ data: true, error: null });

      const result = await referralPointsService.usePointsForMatch(
        'user-123',
        25,
        'match-456',
        'Match entry fee'
      );

      expect(result).toBe(true);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('use_referral_points', {
        p_user_id: 'user-123',
        p_points: 25,
        p_source_type: 'match_entry',
        p_source_id: 'match-456',
        p_description: 'Match entry fee'
      });
    });

    it('should throw error when insufficient points for match entry', async () => {
      const mockBalance = {
        totalPoints: 50,
        availablePoints: 10,
        usedPoints: 40,
        earnedFromReferrals: 50
      };
      
      jest.spyOn(referralPointsService, 'getUserBalance').mockResolvedValue(mockBalance);

      await expect(
        referralPointsService.usePointsForMatch('user-123', 25, 'match-456')
      ).rejects.toThrow('Insufficient referral points');
    });
  });

  describe('Referral Fraud Detection Service', () => {
    it('should generate device fingerprint correctly', () => {
      const deviceData = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        screenResolution: '1920x1080',
        timezone: 'America/New_York',
        language: 'en-US',
        platform: 'Win32',
        cookiesEnabled: true,
        doNotTrack: false,
        plugins: ['Chrome PDF Plugin', 'Chrome PDF Viewer'],
        canvas: 'canvas-hash-123',
        webgl: 'webgl-hash-456'
      };

      const fingerprint = referralFraudService.generateDeviceFingerprint(deviceData);
      
      expect(fingerprint).toBeDefined();
      expect(typeof fingerprint).toBe('string');
      expect(fingerprint.length).toBe(64); // SHA256 hash length
    });

    it('should detect fraud correctly', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: 0.8, error: null });
      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'fraud-detection-123' },
              error: null
            })
          })
        })
      } as any);

      const deviceData = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        screenResolution: '1920x1080',
        timezone: 'America/New_York',
        language: 'en-US',
        platform: 'Win32',
        cookiesEnabled: true,
        doNotTrack: false,
        plugins: ['Chrome PDF Plugin']
      };

      const result = await referralFraudService.detectReferralFraud(
        'referrer-123',
        'referred-456',
        deviceData,
        '***********'
      );

      expect(result.fraudScore).toBe(0.8);
      expect(result.recommendedAction).toBe('reject');
      expect(result.requiresAdminReview).toBe(true);
    });

    it('should check verification status correctly', async () => {
      const mockVerificationData = [{
        is_eligible: true,
        phone_verified: true,
        email_verified: true,
        activity_score: 15,
        days_since_registration: 10,
        missing_requirements: []
      }];

      mockSupabase.rpc.mockResolvedValue({ data: mockVerificationData, error: null });

      const status = await referralFraudService.checkVerificationStatus('user-123');

      expect(status.isEligible).toBe(true);
      expect(status.phoneVerified).toBe(true);
      expect(status.emailVerified).toBe(true);
      expect(status.activityScore).toBe(15);
      expect(status.missingRequirements).toEqual([]);
    });
  });

  describe('Enhanced Referral Processing', () => {
    it('should process referral with verification successfully', async () => {
      // Mock referral data
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { referrer_id: 'referrer-123', id: 'referral-456' },
              error: null
            })
          })
        })
      } as any);

      // Mock verification status
      jest.spyOn(referralFraudService, 'checkVerificationStatus')
        .mockResolvedValueOnce({
          isEligible: true,
          phoneVerified: true,
          emailVerified: true,
          activityScore: 15,
          daysSinceRegistration: 10,
          missingRequirements: []
        })
        .mockResolvedValueOnce({
          isEligible: true,
          phoneVerified: true,
          emailVerified: true,
          activityScore: 12,
          daysSinceRegistration: 5,
          missingRequirements: []
        });

      // Mock fraud detection
      jest.spyOn(referralFraudService, 'detectReferralFraud').mockResolvedValue({
        fraudScore: 0.2,
        riskFactors: [],
        recommendedAction: 'approve',
        requiresAdminReview: false,
        fraudDetectionId: 'fraud-123'
      });

      // Mock database operations
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({ data: [], error: null })
        }),
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'user-referral-123' },
              error: null
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({ error: null })
        })
      } as any);

      // Mock award points
      jest.spyOn(referralService, 'awardReferralPoints').mockResolvedValue();

      const deviceData = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        screenResolution: '1920x1080',
        timezone: 'America/New_York',
        language: 'en-US',
        platform: 'Win32',
        cookiesEnabled: true,
        doNotTrack: false,
        plugins: ['Chrome PDF Plugin']
      };

      const result = await referralService.processReferralWithVerification(
        'TESTCODE123',
        'referred-456',
        deviceData,
        '***********'
      );

      expect(result.success).toBe(true);
      expect(result.status).toBe('approve');
      expect(result.pointsAwarded).toBe(true);
    });

    it('should reject referral with high fraud score', async () => {
      // Mock referral data
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { referrer_id: 'referrer-123', id: 'referral-456' },
              error: null
            })
          })
        })
      } as any);

      // Mock verification status
      jest.spyOn(referralFraudService, 'checkVerificationStatus')
        .mockResolvedValue({
          isEligible: true,
          phoneVerified: true,
          emailVerified: true,
          activityScore: 15,
          daysSinceRegistration: 10,
          missingRequirements: []
        });

      // Mock high fraud score
      jest.spyOn(referralFraudService, 'detectReferralFraud').mockResolvedValue({
        fraudScore: 0.9,
        riskFactors: ['device_multiple_users', 'ip_high_referral_count'],
        recommendedAction: 'reject',
        requiresAdminReview: true,
        fraudDetectionId: 'fraud-123'
      });

      const deviceData = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        screenResolution: '1920x1080',
        timezone: 'America/New_York',
        language: 'en-US',
        platform: 'Win32',
        cookiesEnabled: true,
        doNotTrack: false,
        plugins: ['Chrome PDF Plugin']
      };

      const result = await referralService.processReferralWithVerification(
        'TESTCODE123',
        'referred-456',
        deviceData,
        '***********'
      );

      expect(result.error).toBeDefined();
      expect(result.error).toContain('security concerns');
      expect(result.fraudScore).toBe(0.9);
    });

    it('should reject referral for unverified user', async () => {
      // Mock referral data
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { referrer_id: 'referrer-123', id: 'referral-456' },
              error: null
            })
          })
        })
      } as any);

      // Mock unverified status
      jest.spyOn(referralFraudService, 'checkVerificationStatus')
        .mockResolvedValueOnce({
          isEligible: true,
          phoneVerified: true,
          emailVerified: true,
          activityScore: 15,
          daysSinceRegistration: 10,
          missingRequirements: []
        })
        .mockResolvedValueOnce({
          isEligible: false,
          phoneVerified: false,
          emailVerified: true,
          activityScore: 5,
          daysSinceRegistration: 1,
          missingRequirements: ['phone_verification', 'minimum_activity']
        });

      const deviceData = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        screenResolution: '1920x1080',
        timezone: 'America/New_York',
        language: 'en-US',
        platform: 'Win32',
        cookiesEnabled: true,
        doNotTrack: false,
        plugins: ['Chrome PDF Plugin']
      };

      const result = await referralService.processReferralWithVerification(
        'TESTCODE123',
        'referred-456',
        deviceData,
        '***********'
      );

      expect(result.error).toBeDefined();
      expect(result.error).toContain('verification requirements');
      expect(result.missingRequirements).toEqual(['phone_verification', 'minimum_activity']);
    });
  });

  describe('Integration Tests', () => {
    it('should integrate referral points with match entry', async () => {
      // This would test the integration between referral points and match service
      // In a real test environment, you would test the actual API endpoints
      expect(true).toBe(true); // Placeholder for integration test
    });

    it('should handle admin review workflow', async () => {
      // Test admin approval/rejection workflow
      mockSupabase.rpc.mockResolvedValue({ data: true, error: null });

      const result = await referralFraudService.adminReviewReferral(
        'referral-123',
        'admin-456',
        'approve',
        'Reviewed and approved manually'
      );

      expect(result).toBe(true);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('admin_review_referral', {
        p_referral_id: 'referral-123',
        p_admin_id: 'admin-456',
        p_action: 'approve',
        p_notes: 'Reviewed and approved manually'
      });
    });
  });
});
