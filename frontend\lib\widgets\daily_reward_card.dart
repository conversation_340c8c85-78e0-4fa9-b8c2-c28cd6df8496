/// Daily reward card widget for displaying individual day rewards in calendar
/// Shows reward status, amount, and claim state with beautiful animations

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/daily_reward_models.dart';

class DailyRewardCard extends StatefulWidget {
  final DailyRewardDay day;
  final bool isDarkMode;
  final VoidCallback? onTap;

  const DailyRewardCard({
    super.key,
    required this.day,
    required this.isDarkMode,
    this.onTap,
  });

  @override
  State<DailyRewardCard> createState() => _DailyRewardCardState();
}

class _DailyRewardCardState extends State<DailyRewardCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    // Start pulse animation for current day
    if (widget.day.isCurrentDay && widget.day.canClaim) {
      _startPulseAnimation();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startPulseAnimation() {
    _animationController.repeat(reverse: true);
  }

  void _stopPulseAnimation() {
    _animationController.stop();
    _animationController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.day.canClaim ? _pulseAnimation.value : 1.0,
          child: GestureDetector(
            onTap: widget.onTap,
            onTapDown: (_) {
              if (widget.onTap != null) {
                _animationController.forward();
              }
            },
            onTapUp: (_) {
              if (widget.onTap != null) {
                _animationController.reverse();
              }
            },
            onTapCancel: () {
              if (widget.onTap != null) {
                _animationController.reverse();
              }
            },
            child: Transform.scale(
              scale: widget.onTap != null ? _scaleAnimation.value : 1.0,
              child: Container(
                decoration: BoxDecoration(
                  color: _getBackgroundColor(),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getBorderColor(),
                    width: widget.day.isCurrentDay ? 2 : 1,
                  ),
                  boxShadow: _getBoxShadow(),
                ),
                child: Tooltip(
                  message: _getTooltipMessage(),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildDayNumber(),
                      const SizedBox(height: 4),
                      _buildRewardIcon(),
                      const SizedBox(height: 2),
                      _buildRewardAmount(),
                      const SizedBox(height: 2),
                      _buildStatusIndicator(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getBackgroundColor() {
    if (widget.day.isClaimed) {
      return const Color(0xFFFFCC00).withOpacity(0.1);
    } else if (widget.day.isCurrentDay) {
      return const Color(0xFFFFCC00).withOpacity(0.2);
    } else if (widget.day.isAvailable) {
      return widget.isDarkMode 
          ? const Color(0xFF2A2A2A) 
          : Colors.grey[100]!;
    } else {
      return widget.isDarkMode 
          ? const Color(0xFF1A1A1A) 
          : Colors.grey[50]!;
    }
  }

  Color _getBorderColor() {
    if (widget.day.isClaimed) {
      return const Color(0xFFFFCC00);
    } else if (widget.day.isCurrentDay) {
      return const Color(0xFFFFCC00);
    } else if (widget.day.isAvailable) {
      return widget.isDarkMode ? Colors.grey[600]! : Colors.grey[300]!;
    } else {
      return widget.isDarkMode ? Colors.grey[800]! : Colors.grey[200]!;
    }
  }

  List<BoxShadow> _getBoxShadow() {
    if (widget.day.isCurrentDay && widget.day.canClaim) {
      return [
        BoxShadow(
          color: const Color(0xFFFFCC00).withOpacity(0.4),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];
    } else if (widget.day.isClaimed) {
      return [
        BoxShadow(
          color: const Color(0xFFFFCC00).withOpacity(0.2),
          blurRadius: 4,
          offset: const Offset(0, 1),
        ),
      ];
    }
    return [
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        blurRadius: 2,
        offset: const Offset(0, 1),
      ),
    ];
  }

  Widget _buildDayNumber() {
    return Text(
      '${widget.day.day}',
      style: GoogleFonts.poppins(
        fontSize: 12,
        fontWeight: FontWeight.bold,
        color: _getTextColor(),
      ),
    );
  }

  Widget _buildRewardIcon() {
    IconData icon;
    Color iconColor;

    if (widget.day.isClaimed) {
      icon = Icons.check_circle;
      iconColor = const Color(0xFFFFCC00);
    } else if (widget.day.canClaim) {
      icon = Icons.card_giftcard;
      iconColor = const Color(0xFFFFCC00);
    } else if (widget.day.isAvailable) {
      icon = Icons.cancel;
      iconColor = Colors.red[400]!;
    } else {
      icon = Icons.lock;
      iconColor = widget.isDarkMode ? Colors.grey[600]! : Colors.grey[400]!;
    }

    return Icon(
      icon,
      size: 16,
      color: iconColor,
    );
  }

  Widget _buildRewardAmount() {
    if (widget.day.reward == null) {
      return Text(
        '-',
        style: GoogleFonts.poppins(
          fontSize: 8,
          color: widget.isDarkMode ? Colors.grey[600] : Colors.grey[400],
        ),
      );
    }

    final reward = widget.day.reward!;
    return Column(
      children: [
        if (reward.points > 0) ...[
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.stars,
                size: 8,
                color: _getTextColor(),
              ),
              const SizedBox(width: 2),
              Text(
                '${reward.points}',
                style: GoogleFonts.poppins(
                  fontSize: 7,
                  fontWeight: FontWeight.w600,
                  color: _getTextColor(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 1),
        ],
        if (reward.diamondValue > 0) ...[
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.diamond,
                size: 8,
                color: _getTextColor(),
              ),
              const SizedBox(width: 2),
              Text(
                '${reward.diamondValue}',
                style: GoogleFonts.poppins(
                  fontSize: 7,
                  fontWeight: FontWeight.w600,
                  color: _getTextColor(),
                ),
              ),
            ],
          ),
        ],
        if (reward.points == 0 && reward.diamondValue == 0)
          Text(
            'No reward',
            style: GoogleFonts.poppins(
              fontSize: 6,
              color: widget.isDarkMode ? Colors.grey[600] : Colors.grey[400],
            ),
          ),
      ],
    );
  }

  Widget _buildStatusIndicator() {
    String status;
    Color statusColor;

    switch (widget.day.status) {
      case DailyRewardDayStatus.claimed:
        status = 'Claimed';
        statusColor = const Color(0xFFFFCC00);
        break;
      case DailyRewardDayStatus.available:
        status = 'Claim';
        statusColor = const Color(0xFFFFCC00);
        break;
      case DailyRewardDayStatus.missed:
        status = 'Missed';
        statusColor = Colors.red[400]!;
        break;
      case DailyRewardDayStatus.locked:
        status = 'Locked';
        statusColor = widget.isDarkMode ? Colors.grey[600]! : Colors.grey[400]!;
        break;
    }

    return Text(
      status,
      style: GoogleFonts.poppins(
        fontSize: 6,
        fontWeight: FontWeight.w500,
        color: statusColor,
      ),
    );
  }

  Color _getTextColor() {
    if (widget.day.isClaimed || widget.day.isCurrentDay) {
      return const Color(0xFFFFCC00);
    } else if (widget.day.isAvailable) {
      return widget.isDarkMode ? Colors.white : Colors.black;
    } else {
      return widget.isDarkMode ? Colors.grey[600]! : Colors.grey[400]!;
    }
  }

  String _getTooltipMessage() {
    if (widget.day.reward == null) {
      return 'Day ${widget.day.day}: No reward available';
    }

    final reward = widget.day.reward!;
    final parts = <String>[];

    if (reward.points > 0) {
      parts.add('${reward.points} Points');
    }
    if (reward.diamondValue > 0) {
      parts.add('${reward.diamondValue} Diamonds');
    }

    if (parts.isEmpty) {
      return 'Day ${widget.day.day}: No reward';
    }

    final rewardText = parts.join(' + ');
    final status = widget.day.isClaimed ? 'Claimed' :
                   widget.day.canClaim ? 'Available to claim' :
                   widget.day.isAvailable ? 'Missed' : 'Locked';

    return 'Day ${widget.day.day}: $rewardText\nStatus: $status';
  }
}

/// Streak indicator widget for showing current streak status
class StreakIndicator extends StatelessWidget {
  final int currentStreak;
  final int longestStreak;
  final bool isDarkMode;
  final bool isCompact;

  const StreakIndicator({
    super.key,
    required this.currentStreak,
    required this.longestStreak,
    required this.isDarkMode,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(isCompact ? 12 : 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange[400]!,
            Colors.red[400]!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(isCompact ? 8 : 12),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: isCompact ? MainAxisSize.min : MainAxisSize.max,
        children: [
          Icon(
            Icons.local_fire_department,
            color: Colors.white,
            size: isCompact ? 20 : 24,
          ),
          SizedBox(width: isCompact ? 6 : 8),
          if (!isCompact) Expanded(child: _buildStreakInfo()),
          if (isCompact) _buildCompactStreakInfo(),
        ],
      ),
    );
  }

  Widget _buildStreakInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Current Streak',
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.white.withOpacity(0.9),
          ),
        ),
        Text(
          '$currentStreak Days',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          'Best: $longestStreak days',
          style: GoogleFonts.poppins(
            fontSize: 10,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildCompactStreakInfo() {
    return Text(
      '$currentStreak',
      style: GoogleFonts.poppins(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    );
  }
}

/// Reward claim button with loading state
class RewardClaimButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final bool isLoading;
  final String text;
  final bool isEnabled;

  const RewardClaimButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
    this.text = 'Claim Reward',
    this.isEnabled = true,
  });

  @override
  State<RewardClaimButton> createState() => _RewardClaimButtonState();
}

class _RewardClaimButtonState extends State<RewardClaimButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: widget.isEnabled && !widget.isLoading ? _handlePress : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.isEnabled 
                    ? const Color(0xFFFFCC00) 
                    : Colors.grey[400],
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: widget.isEnabled ? 4 : 0,
              ),
              child: widget.isLoading
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                      ),
                    )
                  : Text(
                      widget.text,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        );
      },
    );
  }

  void _handlePress() {
    _animationController.forward().then((_) {
      _animationController.reverse();
      widget.onPressed?.call();
    });
  }
}
