{"timestamp": "2025-07-31T07:26:36.601Z", "results": [{"timestamp": "2025-07-31T07:26:36.545Z", "message": "🔍 Running pre-deployment validation...", "type": "info", "category": "validation"}, {"timestamp": "2025-07-31T07:26:36.572Z", "message": "Validating environment variables...", "type": "info", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.572Z", "message": "Missing required environment variable: SUPABASE_URL", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.573Z", "message": "Missing required environment variable: SUPABASE_KEY", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.573Z", "message": "Missing required environment variable: JWT_REFRESH_SECRET", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.574Z", "message": "Missing required environment variable: RAZORPAY_KEY_ID", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.574Z", "message": "Missing required environment variable: RAZOR<PERSON>Y_KEY_SECRET", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.575Z", "message": "Missing optional environment variable: JWT_ACCESS_EXPIRY (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.578Z", "message": "Missing optional environment variable: JWT_REFRESH_EXPIRY (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.579Z", "message": "Missing optional environment variable: PAYMENT_TIMEOUT_MINUTES (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.579Z", "message": "Missing optional environment variable: DISABLE_FRAUD_DETECTION (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.581Z", "message": "❌ Missing 5 required environment variables", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:26:36.581Z", "message": "Validating Supabase connection...", "type": "info", "category": "database"}, {"timestamp": "2025-07-31T07:26:36.582Z", "message": "Supabase credentials not found", "type": "error", "category": "database"}, {"timestamp": "2025-07-31T07:26:36.583Z", "message": "Validating Razorpay credentials...", "type": "info", "category": "payment"}, {"timestamp": "2025-07-31T07:26:36.583Z", "message": "<PERSON><PERSON><PERSON><PERSON> credentials not found", "type": "error", "category": "payment"}, {"timestamp": "2025-07-31T07:26:36.585Z", "message": "Validating build output...", "type": "info", "category": "build"}, {"timestamp": "2025-07-31T07:26:36.590Z", "message": "Build output is older than 1 hour, consider rebuilding", "type": "warn", "category": "build"}, {"timestamp": "2025-07-31T07:26:36.591Z", "message": "Build output validation passed", "type": "success", "category": "build"}, {"timestamp": "2025-07-31T07:26:36.593Z", "message": "❌ Some pre-deployment validations failed", "type": "error", "category": "validation"}]}