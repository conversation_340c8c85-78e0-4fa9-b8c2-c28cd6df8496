{"timestamp": "2025-07-31T08:16:43.962Z", "results": [{"timestamp": "2025-07-31T08:16:42.322Z", "message": "Loaded environment variables from backend/.env", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:16:42.349Z", "message": "🔍 Running post-deployment validation...", "type": "info", "category": "validation"}, {"timestamp": "2025-07-31T08:16:42.350Z", "message": "Validating deployed endpoint: https://wiggyz-backend-dt566rys4-tausifraja977-gmailcoms-projects.vercel.app", "type": "info", "category": "deployment"}, {"timestamp": "2025-07-31T08:16:43.959Z", "message": "Health endpoint failed: undefined", "type": "error", "category": "deployment"}, {"timestamp": "2025-07-31T08:16:43.960Z", "message": "❌ Post-deployment validation failed", "type": "error", "category": "validation"}]}