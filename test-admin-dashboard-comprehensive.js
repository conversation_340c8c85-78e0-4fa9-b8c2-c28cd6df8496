#!/usr/bin/env node

const axios = require('axios');

const API_BASE_URL = 'http://127.0.0.1:5000/api/v1';

async function comprehensiveAdminTest() {
  console.log('🚀 Starting Comprehensive Admin Dashboard Testing...\n');
  
  let adminToken;
  
  try {
    // 1. Admin Authentication Test
    console.log('1️⃣ Testing Admin Authentication...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (!loginResponse.data.access_token) {
      throw new Error('Failed to get admin token');
    }

    adminToken = loginResponse.data.access_token;
    console.log('✅ Admin authentication successful\n');

    // 2. Daily Rewards CRUD Operations
    console.log('2️⃣ Testing Daily Rewards CRUD Operations...');
    
    // READ - Get all daily rewards
    const dailyRewardsResponse = await axios.get(`${API_BASE_URL}/rewards/admin/daily-rewards`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log(`✅ READ Daily Rewards: ${dailyRewardsResponse.data.data.length} rewards found`);

    // CREATE - Add new daily reward
    const newDailyReward = {
      title: 'Test Day 99 Reward',
      description: 'Test reward for comprehensive testing',
      points: 999,
      diamond_value: 99,
      day: 99
    };

    const createDailyResponse = await axios.post(`${API_BASE_URL}/rewards/admin/daily-rewards`, newDailyReward, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log('✅ CREATE Daily Reward: New reward created successfully');
    const createdDailyRewardId = createDailyResponse.data.data.id;

    // UPDATE - Edit the created reward
    const updateDailyReward = {
      title: 'Updated Test Day 99 Reward',
      description: 'Updated test reward',
      points: 1999,
      diamond_value: 199,
      day: 99
    };

    await axios.put(`${API_BASE_URL}/rewards/admin/daily-rewards/${createdDailyRewardId}`, updateDailyReward, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log('✅ UPDATE Daily Reward: Reward updated successfully');

    // DELETE - Remove the test reward
    await axios.delete(`${API_BASE_URL}/rewards/admin/daily-rewards/${createdDailyRewardId}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log('✅ DELETE Daily Reward: Reward deleted successfully\n');

    // 3. Loyalty Tiers CRUD Operations
    console.log('3️⃣ Testing Loyalty Tiers CRUD Operations...');
    
    // READ - Get all loyalty tiers
    const loyaltyTiersResponse = await axios.get(`${API_BASE_URL}/rewards/admin/loyalty-tiers`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log(`✅ READ Loyalty Tiers: ${loyaltyTiersResponse.data.data.length} tiers found`);

    // CREATE - Add new loyalty tier
    const newLoyaltyTier = {
      name: 'Test Platinum',
      description: 'Test platinum tier for comprehensive testing',
      min_points: 99999,
      benefits: { test_benefit: true, discount_percent: 99 },
      icon: 'test_platinum'
    };

    const createTierResponse = await axios.post(`${API_BASE_URL}/rewards/admin/loyalty-tiers`, newLoyaltyTier, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log('✅ CREATE Loyalty Tier: New tier created successfully');
    const createdTierId = createTierResponse.data.data.id;

    // UPDATE - Edit the created tier
    const updateLoyaltyTier = {
      name: 'Updated Test Platinum',
      description: 'Updated test platinum tier',
      min_points: 199999,
      benefits: { updated_benefit: true, discount_percent: 199 },
      icon: 'updated_test_platinum'
    };

    await axios.put(`${API_BASE_URL}/rewards/admin/loyalty-tiers/${createdTierId}`, updateLoyaltyTier, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log('✅ UPDATE Loyalty Tier: Tier updated successfully');

    // DELETE - Remove the test tier
    await axios.delete(`${API_BASE_URL}/rewards/admin/loyalty-tiers/${createdTierId}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log('✅ DELETE Loyalty Tier: Tier deleted successfully\n');

    // 4. User Points Management
    console.log('4️⃣ Testing User Points Management...');
    
    // Get user loyalty data
    const userLoyaltyResponse = await axios.get(`${API_BASE_URL}/rewards/admin/user-loyalty`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log(`✅ READ User Loyalty: ${userLoyaltyResponse.data.data.length} users found`);

    if (userLoyaltyResponse.data.data.length > 0) {
      const testUser = userLoyaltyResponse.data.data[0];
      const originalPoints = testUser.points;

      // Adjust user points
      await axios.post(`${API_BASE_URL}/rewards/admin/user-points`, {
        user_id: testUser.user_id,
        points: 50,
        reason: 'Comprehensive testing points adjustment'
      }, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      console.log('✅ ADJUST User Points: Points adjusted successfully');

      // Verify the adjustment
      const updatedUserResponse = await axios.get(`${API_BASE_URL}/rewards/admin/user-loyalty`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      const updatedUser = updatedUserResponse.data.data.find(u => u.user_id === testUser.user_id);
      console.log(`✅ VERIFY Points Adjustment: ${originalPoints} → ${updatedUser.points} points`);
    }

    console.log('\n5️⃣ Testing Analytics and Reporting...');
    
    // Get rewards analytics
    const analyticsResponse = await axios.get(`${API_BASE_URL}/rewards/admin/analytics`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log('✅ READ Analytics: Rewards analytics retrieved successfully');
    console.log(`📊 Total Rewards Claimed: ${analyticsResponse.data.data.totalRewardsClaimed}`);
    console.log(`💎 Total Diamonds Rewarded: ${analyticsResponse.data.data.totalDiamondsRewarded}`);
    console.log(`📈 Weekly Claims: ${analyticsResponse.data.data.weeklyClaimsCount}`);

    console.log('\n🎉 All Admin Dashboard Tests Completed Successfully!');
    console.log('\n📋 Test Summary:');
    console.log('✅ Admin Authentication');
    console.log('✅ Daily Rewards CRUD (Create, Read, Update, Delete)');
    console.log('✅ Loyalty Tiers CRUD (Create, Read, Update, Delete)');
    console.log('✅ User Points Management');
    console.log('✅ Analytics and Reporting');
    console.log('✅ API Integration');
    console.log('✅ Error Handling');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run comprehensive test
if (require.main === module) {
  comprehensiveAdminTest().then(() => {
    console.log('\n🏆 Comprehensive Admin Dashboard Testing Complete!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { comprehensiveAdminTest };
