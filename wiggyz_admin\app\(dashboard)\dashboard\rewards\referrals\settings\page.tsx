"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { Save, Settings, Shield, Clock, Users, Gift } from "lucide-react"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"

interface ReferralSettings {
  referral_reward_points: string
  verification_required: string
  minimum_activity_days: string
  cooldown_period_hours: string
  max_referrals_per_ip_daily: string
  max_referrals_per_device_daily: string
  fraud_threshold_score: string
  admin_review_threshold: string
}

export default function ReferralSettingsPage() {
  const [settings, setSettings] = useState<ReferralSettings>({
    referral_reward_points: "25",
    verification_required: "true",
    minimum_activity_days: "7",
    cooldown_period_hours: "24",
    max_referrals_per_ip_daily: "5",
    max_referrals_per_device_daily: "3",
    fraud_threshold_score: "0.7",
    admin_review_threshold: "0.5"
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/rewards/admin/referral-settings')
      if (response.ok) {
        const data = await response.json()
        if (data.data) {
          const settingsMap: ReferralSettings = {} as ReferralSettings
          data.data.forEach((setting: any) => {
            settingsMap[setting.setting_key as keyof ReferralSettings] = setting.setting_value
          })
          setSettings(settingsMap)
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load referral settings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/rewards/admin/referral-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Referral settings updated successfully",
        })
      } else {
        throw new Error('Failed to save settings')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save referral settings",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const updateSetting = (key: keyof ReferralSettings, value: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading...</div>
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <Link href="/dashboard/rewards/referrals">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Back</span>
          </Button>
        </Link>
        <h1 className="text-3xl font-bold tracking-tight">Referral Settings</h1>
      </div>

      <div className="grid gap-6">
        {/* Reward Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5" />
              Reward Configuration
            </CardTitle>
            <CardDescription>Configure referral reward amounts and conditions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="reward-points">Referral Reward Points</Label>
              <Input
                id="reward-points"
                type="number"
                min="1"
                max="1000"
                value={settings.referral_reward_points}
                onChange={(e) => updateSetting('referral_reward_points', e.target.value)}
                placeholder="25"
              />
              <p className="text-sm text-muted-foreground">
                Points awarded to both referrer and referee (non-withdrawable, for match entries only)
              </p>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="min-activity">Minimum Activity Days</Label>
              <Input
                id="min-activity"
                type="number"
                min="0"
                max="30"
                value={settings.minimum_activity_days}
                onChange={(e) => updateSetting('minimum_activity_days', e.target.value)}
                placeholder="7"
              />
              <p className="text-sm text-muted-foreground">
                Minimum days of account activity before referral rewards are eligible
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Verification Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Verification Requirements
            </CardTitle>
            <CardDescription>Configure user verification requirements for referrals</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Require Verification</Label>
                <p className="text-sm text-muted-foreground">
                  Require phone and email verification before referral rewards
                </p>
              </div>
              <Switch
                checked={settings.verification_required === "true"}
                onCheckedChange={(checked) => updateSetting('verification_required', checked.toString())}
              />
            </div>
          </CardContent>
        </Card>

        {/* Anti-Abuse Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Anti-Abuse Settings
            </CardTitle>
            <CardDescription>Configure limits to prevent referral abuse</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="ip-limit">Max Referrals per IP (Daily)</Label>
              <Input
                id="ip-limit"
                type="number"
                min="1"
                max="50"
                value={settings.max_referrals_per_ip_daily}
                onChange={(e) => updateSetting('max_referrals_per_ip_daily', e.target.value)}
                placeholder="5"
              />
              <p className="text-sm text-muted-foreground">
                Maximum referrals allowed from the same IP address per day
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="device-limit">Max Referrals per Device (Daily)</Label>
              <Input
                id="device-limit"
                type="number"
                min="1"
                max="20"
                value={settings.max_referrals_per_device_daily}
                onChange={(e) => updateSetting('max_referrals_per_device_daily', e.target.value)}
                placeholder="3"
              />
              <p className="text-sm text-muted-foreground">
                Maximum referrals allowed from the same device per day
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Cooldown Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Cooldown Settings
            </CardTitle>
            <CardDescription>Configure cooldown periods between referral rewards</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="cooldown">Cooldown Period (Hours)</Label>
              <Input
                id="cooldown"
                type="number"
                min="1"
                max="168"
                value={settings.cooldown_period_hours}
                onChange={(e) => updateSetting('cooldown_period_hours', e.target.value)}
                placeholder="24"
              />
              <p className="text-sm text-muted-foreground">
                Hours between referral rewards for the same user
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Fraud Detection Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Fraud Detection Settings
            </CardTitle>
            <CardDescription>Configure fraud detection thresholds and actions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="fraud-threshold">Auto-Reject Threshold</Label>
              <Input
                id="fraud-threshold"
                type="number"
                min="0.1"
                max="1.0"
                step="0.1"
                value={settings.fraud_threshold_score}
                onChange={(e) => updateSetting('fraud_threshold_score', e.target.value)}
                placeholder="0.7"
              />
              <p className="text-sm text-muted-foreground">
                Fraud score threshold for automatic rejection (0.1 - 1.0)
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="review-threshold">Admin Review Threshold</Label>
              <Input
                id="review-threshold"
                type="number"
                min="0.1"
                max="1.0"
                step="0.1"
                value={settings.admin_review_threshold}
                onChange={(e) => updateSetting('admin_review_threshold', e.target.value)}
                placeholder="0.5"
              />
              <p className="text-sm text-muted-foreground">
                Fraud score threshold requiring manual admin review (0.1 - 1.0)
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-end">
              <Button onClick={saveSettings} disabled={saving}>
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Saving...' : 'Save Settings'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
