-- Create admin_notifications table for admin alerts and notifications
-- This table stores notifications specifically for admin users

CREATE TABLE IF NOT EXISTS admin_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Notification details
  type VARCHAR(50) NOT NULL, -- withdrawal_request, user_signup, suspicious_activity, etc.
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  severity VARCHAR(20) NOT NULL DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
  
  -- Status and targeting
  is_read BOOLEAN NOT NULL DEFAULT FALSE,
  target_admin_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- NULL means for all admins
  
  -- Related data
  related_entity_type VARCHAR(50), -- withdrawal_request, user, transaction, etc.
  related_entity_id UUID, -- ID of the related entity
  metadata JSONB, -- Additional data for the notification
  
  -- Action tracking
  action_required BOOLEAN NOT NULL DEFAULT FALSE, -- Whether this notification requires admin action
  action_url VARCHAR(500), -- URL to navigate to for action (e.g., /admin/withdrawals/123)
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE, -- When the notification was marked as read
  expires_at TIMESTAMP WITH TIME ZONE -- Optional expiration for temporary notifications
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_admin_notifications_type ON admin_notifications(type);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_is_read ON admin_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_target_admin_id ON admin_notifications(target_admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_created_at ON admin_notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_severity ON admin_notifications(severity);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_action_required ON admin_notifications(action_required);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_related_entity ON admin_notifications(related_entity_type, related_entity_id);

-- Create trigger to update read_at timestamp
CREATE OR REPLACE FUNCTION update_admin_notification_read_at()
RETURNS TRIGGER AS $$
BEGIN
  -- Set read_at when is_read changes from false to true
  IF OLD.is_read = FALSE AND NEW.is_read = TRUE AND NEW.read_at IS NULL THEN
    NEW.read_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_admin_notification_read_at
BEFORE UPDATE ON admin_notifications
FOR EACH ROW
EXECUTE FUNCTION update_admin_notification_read_at();

-- Enable Row Level Security
ALTER TABLE admin_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Admins can view all notifications or notifications targeted to them
CREATE POLICY "Admins can view admin notifications"
  ON admin_notifications FOR SELECT
  USING (
    (auth.jwt() ->> 'role' = 'admin' OR auth.jwt() ->> 'role' = 'superadmin') AND
    (target_admin_id IS NULL OR target_admin_id = auth.uid())
  );

-- Admins can update notifications (mark as read)
CREATE POLICY "Admins can update admin notifications"
  ON admin_notifications FOR UPDATE
  USING (
    (auth.jwt() ->> 'role' = 'admin' OR auth.jwt() ->> 'role' = 'superadmin') AND
    (target_admin_id IS NULL OR target_admin_id = auth.uid())
  );

-- System can insert admin notifications
CREATE POLICY "System can insert admin notifications"
  ON admin_notifications FOR INSERT
  WITH CHECK (true); -- Will be controlled by API authentication

-- Create a function to create admin notifications
CREATE OR REPLACE FUNCTION create_admin_notification(
  p_type VARCHAR(50),
  p_title VARCHAR(255),
  p_message TEXT,
  p_severity VARCHAR(20) DEFAULT 'info',
  p_target_admin_id UUID DEFAULT NULL,
  p_related_entity_type VARCHAR(50) DEFAULT NULL,
  p_related_entity_id UUID DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL,
  p_action_required BOOLEAN DEFAULT FALSE,
  p_action_url VARCHAR(500) DEFAULT NULL,
  p_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO admin_notifications (
    type, title, message, severity, target_admin_id,
    related_entity_type, related_entity_id, metadata,
    action_required, action_url, expires_at
  ) VALUES (
    p_type, p_title, p_message, p_severity, p_target_admin_id,
    p_related_entity_type, p_related_entity_id, p_metadata,
    p_action_required, p_action_url, p_expires_at
  ) RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Create a view for unread admin notifications count
CREATE OR REPLACE VIEW admin_notifications_summary AS
SELECT 
  COUNT(*) FILTER (WHERE is_read = FALSE) as unread_count,
  COUNT(*) FILTER (WHERE is_read = FALSE AND action_required = TRUE) as action_required_count,
  COUNT(*) FILTER (WHERE is_read = FALSE AND severity = 'critical') as critical_count,
  COUNT(*) FILTER (WHERE is_read = FALSE AND severity = 'error') as error_count,
  COUNT(*) FILTER (WHERE is_read = FALSE AND severity = 'warning') as warning_count,
  COUNT(*) as total_count
FROM admin_notifications
WHERE (target_admin_id IS NULL OR target_admin_id = auth.uid())
  AND (expires_at IS NULL OR expires_at > NOW());

-- Grant access to the view and function for authenticated users
GRANT SELECT ON admin_notifications_summary TO authenticated;
GRANT EXECUTE ON FUNCTION create_admin_notification TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE admin_notifications IS 'Stores notifications and alerts specifically for admin users';
COMMENT ON COLUMN admin_notifications.type IS 'Type of notification (withdrawal_request, user_signup, etc.)';
COMMENT ON COLUMN admin_notifications.action_required IS 'Whether this notification requires admin action';
COMMENT ON COLUMN admin_notifications.action_url IS 'URL to navigate to for taking action on this notification';
COMMENT ON COLUMN admin_notifications.target_admin_id IS 'Specific admin user this notification is for, NULL means all admins';
COMMENT ON FUNCTION create_admin_notification IS 'Helper function to create admin notifications with proper validation';
