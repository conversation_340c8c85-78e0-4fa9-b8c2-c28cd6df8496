/**
 * Support Service
 * Business logic for support message operations
 */

import { supabase } from '../../../config/supabase';
import { logger } from '../../../utils/logger';
import { ApiError } from '../../../utils/errorHandler';

export interface CreateSupportMessageData {
  userId: string;
  subject: string;
  category: string;
  message: string;
}

export interface CreateSupportMessageReplyData {
  supportMessageId: string;
  adminUserId: string;
  replyText: string;
  isInternal: boolean;
}

export interface SupportMessageFilters {
  status?: string;
  category?: string;
  priority?: string;
  assigned_to?: string;
  created_after?: string;
  created_before?: string;
  page?: number;
  limit?: number;
  search?: string;
}

export const supportService = {
  /**
   * Create a new support message
   */
  async createSupportMessage(data: CreateSupportMessageData) {
    try {
      const { data: supportMessage, error } = await supabase
        .from('support_messages')
        .insert({
          user_id: data.userId,
          subject: data.subject,
          category: data.category,
          message: data.message,
          status: 'new',
          priority: 'normal'
        })
        .select(`
          *,
          user:users!user_id(id, name, email)
        `)
        .single();

      if (error) {
        logger.error(`Error creating support message: ${error.message}`);
        throw new ApiError(500, 'Failed to create support message');
      }

      // Create notification for admins about new support message
      await this.notifyAdminsOfNewMessage(supportMessage);

      return supportMessage;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get user's support messages
   */
  async getUserSupportMessages(userId: string, filters: SupportMessageFilters) {
    try {
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const offset = (page - 1) * limit;

      let query = supabase
        .from('support_messages')
        .select(`
          *,
          user:users!user_id(id, name, email),
          assigned_admin:users!assigned_to(id, name, email),
          replies:support_message_replies(
            id,
            reply_text,
            is_internal,
            created_at,
            admin:users!admin_user_id(id, name, email)
          )
        `, { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.category) {
        query = query.eq('category', filters.category);
      }
      if (filters.priority) {
        query = query.eq('priority', filters.priority);
      }
      if (filters.created_after) {
        query = query.gte('created_at', filters.created_after);
      }
      if (filters.created_before) {
        query = query.lte('created_at', filters.created_before);
      }
      if (filters.search) {
        query = query.or(`subject.ilike.%${filters.search}%,message.ilike.%${filters.search}%`);
      }

      const { data, error, count } = await query
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error(`Error getting user support messages: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve support messages');
      }

      return {
        messages: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get all support messages (admin only)
   */
  async getAllSupportMessages(filters: SupportMessageFilters) {
    try {
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const offset = (page - 1) * limit;

      let query = supabase
        .from('support_messages')
        .select(`
          *,
          user:users!user_id(id, name, email),
          assigned_admin:users!assigned_to(id, name, email),
          replies:support_message_replies(
            id,
            reply_text,
            is_internal,
            created_at,
            admin:users!admin_user_id(id, name, email)
          )
        `, { count: 'exact' })
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.category) {
        query = query.eq('category', filters.category);
      }
      if (filters.priority) {
        query = query.eq('priority', filters.priority);
      }
      if (filters.assigned_to) {
        query = query.eq('assigned_to', filters.assigned_to);
      }
      if (filters.created_after) {
        query = query.gte('created_at', filters.created_after);
      }
      if (filters.created_before) {
        query = query.lte('created_at', filters.created_before);
      }
      if (filters.search) {
        query = query.or(`subject.ilike.%${filters.search}%,message.ilike.%${filters.search}%`);
      }

      const { data, error, count } = await query
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error(`Error getting all support messages: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve support messages');
      }

      return {
        messages: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get support message by ID
   */
  async getSupportMessageById(messageId: string, userId: string, userRole?: string) {
    try {
      let query = supabase
        .from('support_messages')
        .select(`
          *,
          user:users(id, name, email),
          assigned_admin:users!assigned_to(id, name, email),
          replies:support_message_replies(
            id,
            reply_text,
            is_internal,
            created_at,
            admin:users(id, name, email)
          )
        `)
        .eq('id', messageId);

      // Non-admin users can only see their own messages
      if (!userRole || !['admin', 'manager', 'spectator'].includes(userRole)) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query.single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Message not found
        }
        logger.error(`Error getting support message by ID: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve support message');
      }

      // Filter out internal replies for non-admin users
      if (!userRole || !['admin', 'manager', 'spectator'].includes(userRole)) {
        if (data.replies) {
          data.replies = data.replies.filter((reply: any) => !reply.is_internal);
        }
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Update support message
   */
  async updateSupportMessage(messageId: string, updates: any, adminUserId: string) {
    try {
      const updateData: any = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      // Set resolved_at if status is being changed to resolved
      if (updates.status === 'resolved' && updates.status !== 'resolved') {
        updateData.resolved_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from('support_messages')
        .update(updateData)
        .eq('id', messageId)
        .select(`
          *,
          user:users(id, name, email),
          assigned_admin:users!assigned_to(id, name, email)
        `)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Message not found
        }
        logger.error(`Error updating support message: ${error.message}`);
        throw new ApiError(500, 'Failed to update support message');
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Create support message reply
   */
  async createSupportMessageReply(data: CreateSupportMessageReplyData) {
    try {
      const { data: reply, error } = await supabase
        .from('support_message_replies')
        .insert({
          support_message_id: data.supportMessageId,
          admin_user_id: data.adminUserId,
          reply_text: data.replyText,
          is_internal: data.isInternal
        })
        .select(`
          *,
          admin:users(id, name, email)
        `)
        .single();

      if (error) {
        logger.error(`Error creating support message reply: ${error.message}`);
        throw new ApiError(500, 'Failed to create reply');
      }

      // Update support message status to in_progress if it's new
      await supabase
        .from('support_messages')
        .update({
          status: 'in_progress',
          updated_at: new Date().toISOString()
        })
        .eq('id', data.supportMessageId)
        .eq('status', 'new');

      // Notify user of reply if it's not internal
      if (!data.isInternal) {
        await this.notifyUserOfReply(data.supportMessageId, reply);
      }

      return reply;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get support message replies
   */
  async getSupportMessageReplies(messageId: string, userId: string, userRole?: string) {
    try {
      // First check if user has access to this message
      const messageAccess = await this.getSupportMessageById(messageId, userId, userRole);
      if (!messageAccess) {
        throw new ApiError(404, 'Support message not found');
      }

      let query = supabase
        .from('support_message_replies')
        .select(`
          *,
          admin:users(id, name, email)
        `)
        .eq('support_message_id', messageId)
        .order('created_at', { ascending: true });

      // Filter out internal replies for non-admin users
      if (!userRole || !['admin', 'manager', 'spectator'].includes(userRole)) {
        query = query.eq('is_internal', false);
      }

      const { data, error } = await query;

      if (error) {
        logger.error(`Error getting support message replies: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve replies');
      }

      return data || [];
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Assign support message to admin
   */
  async assignSupportMessage(messageId: string, assignedTo: string, adminUserId: string) {
    try {
      const { data, error } = await supabase
        .from('support_messages')
        .update({
          assigned_to: assignedTo,
          updated_at: new Date().toISOString()
        })
        .eq('id', messageId)
        .select(`
          *,
          user:users(id, name, email),
          assigned_admin:users!assigned_to(id, name, email)
        `)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null;
        }
        logger.error(`Error assigning support message: ${error.message}`);
        throw new ApiError(500, 'Failed to assign support message');
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Update support message status
   */
  async updateSupportMessageStatus(messageId: string, status: string, adminUserId: string) {
    try {
      logger.info(`Updating support message status: messageId=${messageId}, status=${status}, adminUserId=${adminUserId}`);

      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      };

      if (status === 'resolved') {
        updateData.resolved_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from('support_messages')
        .update(updateData)
        .eq('id', messageId)
        .select(`
          *,
          user:users!support_messages_user_id_fkey(id, name, email),
          assigned_admin:users!support_messages_assigned_to_fkey(id, name, email)
        `)
        .single();

      if (error) {
        logger.error(`Supabase error updating support message status: ${JSON.stringify(error)}`);
        if (error.code === 'PGRST116') {
          return null;
        }
        throw new ApiError(500, 'Failed to update status');
      }

      logger.info(`Support message status updated successfully: ${messageId}`);
      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get support analytics
   */
  async getSupportAnalytics() {
    try {
      // Get message counts by status
      const { data: statusCounts, error: statusError } = await supabase
        .from('support_messages')
        .select('status')
        .then(({ data, error }: any) => {
          if (error) return { data: null, error };
          
          const counts = data?.reduce((acc: any, msg: any) => {
            acc[msg.status] = (acc[msg.status] || 0) + 1;
            return acc;
          }, {}) || {};
          
          return { data: counts, error: null };
        });

      if (statusError) {
        throw new ApiError(500, 'Failed to get status analytics');
      }

      // Get message counts by category
      const { data: categoryCounts, error: categoryError } = await supabase
        .from('support_messages')
        .select('category')
        .then(({ data, error }: any) => {
          if (error) return { data: null, error };
          
          const counts = data?.reduce((acc: any, msg: any) => {
            acc[msg.category] = (acc[msg.category] || 0) + 1;
            return acc;
          }, {}) || {};
          
          return { data: counts, error: null };
        });

      if (categoryError) {
        throw new ApiError(500, 'Failed to get category analytics');
      }

      // Get total counts
      const { count: totalMessages } = await supabase
        .from('support_messages')
        .select('*', { count: 'exact', head: true });

      const { count: openMessages } = await supabase
        .from('support_messages')
        .select('*', { count: 'exact', head: true })
        .in('status', ['new', 'in_progress']);

      return {
        totalMessages: totalMessages || 0,
        openMessages: openMessages || 0,
        statusBreakdown: statusCounts,
        categoryBreakdown: categoryCounts
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get response time analytics
   */
  async getResponseTimeAnalytics() {
    try {
      // This would typically involve more complex queries
      // For now, return mock data structure
      return {
        averageFirstResponseTime: '2.5 hours',
        averageResolutionTime: '24 hours',
        responseTimeByCategory: {
          bug_report: '1.5 hours',
          feature_request: '4 hours',
          general_inquiry: '2 hours',
          account_issue: '1 hour',
          payment_issue: '30 minutes',
          technical_support: '2 hours'
        }
      };
    } catch (error) {
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get satisfaction analytics
   */
  async getSatisfactionAnalytics() {
    try {
      // This would typically involve satisfaction surveys
      // For now, return mock data structure
      return {
        overallSatisfaction: 4.2,
        satisfactionByCategory: {
          bug_report: 4.0,
          feature_request: 4.5,
          general_inquiry: 4.3,
          account_issue: 4.1,
          payment_issue: 4.8,
          technical_support: 3.9
        },
        totalResponses: 150
      };
    } catch (error) {
      logger.error(`Support service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Notify admins of new support message
   */
  async notifyAdminsOfNewMessage(supportMessage: any) {
    try {
      // Get all admin users
      const { data: admins, error } = await supabase
        .from('users')
        .select('id')
        .in('role', ['admin', 'manager', 'spectator']);

      if (error || !admins) {
        logger.error(`Error getting admin users for notification: ${error?.message}`);
        return;
      }

      // Create notifications for all admins
      const notifications = admins.map((admin: any) => ({
        user_id: admin.id,
        title: 'New Support Message',
        message: `New ${supportMessage.category.replace('_', ' ')} message: ${supportMessage.subject}`,
        type: 'support_message',
        data: {
          support_message_id: supportMessage.id,
          category: supportMessage.category,
          priority: supportMessage.priority
        },
        created_at: new Date().toISOString(),
        read: false
      }));

      await supabase
        .from('notifications')
        .insert(notifications);

    } catch (error) {
      logger.error(`Error notifying admins of new support message: ${error}`);
    }
  },

  /**
   * Notify user of reply
   */
  async notifyUserOfReply(supportMessageId: string, reply: any) {
    try {
      // Get the support message to find the user
      const { data: supportMessage, error } = await supabase
        .from('support_messages')
        .select('user_id, subject')
        .eq('id', supportMessageId)
        .single();

      if (error || !supportMessage) {
        logger.error(`Error getting support message for reply notification: ${error?.message}`);
        return;
      }

      // Create notification for the user
      await supabase
        .from('notifications')
        .insert({
          user_id: supportMessage.user_id,
          title: 'Support Reply Received',
          message: `You have received a reply to your support message: ${supportMessage.subject}`,
          type: 'support_reply',
          data: {
            support_message_id: supportMessageId,
            reply_id: reply.id
          },
          created_at: new Date().toISOString(),
          read: false
        });

    } catch (error) {
      logger.error(`Error notifying user of support reply: ${error}`);
    }
  }
};
