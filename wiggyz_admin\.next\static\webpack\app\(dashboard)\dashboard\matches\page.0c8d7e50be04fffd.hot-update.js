"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/matches/page",{

/***/ "(app-pages-browser)/./lib/matchService.ts":
/*!*****************************!*\
  !*** ./lib/matchService.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignWinner: () => (/* binding */ assignWinner),\n/* harmony export */   createMatch: () => (/* binding */ createMatch),\n/* harmony export */   deleteMatch: () => (/* binding */ deleteMatch),\n/* harmony export */   getAllMatches: () => (/* binding */ getAllMatches),\n/* harmony export */   getGames: () => (/* binding */ getGames),\n/* harmony export */   getMatchById: () => (/* binding */ getMatchById),\n/* harmony export */   getMatchStatistics: () => (/* binding */ getMatchStatistics),\n/* harmony export */   getPendingResults: () => (/* binding */ getPendingResults),\n/* harmony export */   getPendingVerificationMatches: () => (/* binding */ getPendingVerificationMatches),\n/* harmony export */   matchService: () => (/* binding */ matchService),\n/* harmony export */   updateMatch: () => (/* binding */ updateMatch),\n/* harmony export */   verifyMatchResult: () => (/* binding */ verifyMatchResult)\n/* harmony export */ });\n/* harmony import */ var _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabaseClient */ \"(app-pages-browser)/./lib/supabaseClient.ts\");\n/**\r\n * Match Service for Admin Interface\r\n * Handles all match-related operations for the admin dashboard\r\n */ \n// Get all matches with filtering\nasync function getAllMatches() {\n    let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        console.log('Fetching matches with filters:', filters);\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        let query = supabase.from('matches').select(\"\\n        *,\\n        games (id, name, image),\\n        users!matches_created_by_fkey (id, name, email),\\n        tournaments (id, name, status),\\n        match_participants (\\n          id,\\n          user_id,\\n          participant_type,\\n          team_name,\\n          team_position,\\n          joined_at,\\n          result_score,\\n          result_position,\\n          result_kills,\\n          result_screenshot_url,\\n          result_submitted_at,\\n          is_winner,\\n          users (id, name, email)\\n        )\\n      \").order('created_at', {\n            ascending: false\n        });\n        // Apply filters\n        if (filters.game_id) {\n            query = query.eq('game_id', filters.game_id);\n        }\n        if (filters.status) {\n            query = query.eq('status', filters.status);\n        }\n        if (filters.match_type) {\n            query = query.eq('match_type', filters.match_type);\n        }\n        if (filters.game_format) {\n            query = query.eq('game_format', filters.game_format);\n        }\n        if (filters.match_mode) {\n            query = query.eq('match_mode', filters.match_mode);\n        }\n        if (filters.created_by) {\n            query = query.eq('created_by', filters.created_by);\n        }\n        if (filters.start_date) {\n            query = query.gte('match_time', filters.start_date);\n        }\n        if (filters.end_date) {\n            query = query.lte('match_time', filters.end_date);\n        }\n        // Pagination\n        if (filters.limit) {\n            query = query.limit(filters.limit);\n        }\n        if (filters.offset) {\n            query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Error fetching matches:', error);\n            throw new Error(\"Failed to fetch matches: \".concat(error.message));\n        }\n        return {\n            data: data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in getAllMatches:', error);\n        return {\n            data: null,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Get match by ID\nasync function getMatchById(matchId) {\n    try {\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data, error } = await supabase.from('matches').select(\"\\n        *,\\n        games (id, name, image),\\n        users!matches_created_by_fkey (id, name, email),\\n        tournaments (id, name, status),\\n        match_participants (\\n          id,\\n          user_id,\\n          participant_type,\\n          team_name,\\n          team_position,\\n          joined_at,\\n          result_score,\\n          result_position,\\n          result_kills,\\n          result_screenshot_url,\\n          result_submitted_at,\\n          is_winner,\\n          users (id, name, email)\\n        )\\n      \").eq('id', matchId).single();\n        if (error) {\n            if (error.code === 'PGRST116') {\n                return {\n                    data: null,\n                    error: null\n                } // Match not found\n                ;\n            }\n            console.error('Error fetching match:', error);\n            throw new Error(\"Failed to fetch match: \".concat(error.message));\n        }\n        return {\n            data: data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in getMatchById:', error);\n        return {\n            data: null,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Create a new match\nasync function createMatch(matchData) {\n    try {\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        // Set default values\n        const matchPayload = {\n            ...matchData,\n            match_type: 'standalone',\n            status: 'scheduled',\n            // Set result deadline to 30 minutes after match time if not provided\n            result_submission_deadline: matchData.result_submission_deadline || new Date(new Date(matchData.match_time).getTime() + 30 * 60000).toISOString()\n        };\n        const { data, error } = await supabase.from('matches').insert([\n            matchPayload\n        ]).select(\"\\n        *,\\n        games (id, name, image),\\n        users!matches_created_by_fkey (id, name, email)\\n      \").single();\n        if (error) {\n            console.error('Error creating match:', error);\n            throw new Error(\"Failed to create match: \".concat(error.message));\n        }\n        return {\n            data: data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in createMatch:', error);\n        return {\n            data: null,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Update match\nasync function updateMatch(matchId, updateData) {\n    try {\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data, error } = await supabase.from('matches').update({\n            ...updateData,\n            updated_at: new Date().toISOString()\n        }).eq('id', matchId).select(\"\\n        *,\\n        games (id, name, image),\\n        users!matches_created_by_fkey (id, name, email),\\n        tournaments (id, name, status)\\n      \").single();\n        if (error) {\n            console.error('Error updating match:', error);\n            throw new Error(\"Failed to update match: \".concat(error.message));\n        }\n        return {\n            data: data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in updateMatch:', error);\n        return {\n            data: null,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Delete match\nasync function deleteMatch(matchId) {\n    try {\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { error } = await supabase.from('matches').delete().eq('id', matchId);\n        if (error) {\n            console.error('Error deleting match:', error);\n            throw new Error(\"Failed to delete match: \".concat(error.message));\n        }\n        return {\n            success: true,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in deleteMatch:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Assign winner to match\nasync function assignWinner(matchId, winnerId, notes) {\n    try {\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        // Update match with winner\n        const { data: matchData, error: matchError } = await supabase.from('matches').update({\n            winner_id: winnerId,\n            status: 'completed',\n            admin_verified: true,\n            verification_notes: notes,\n            updated_at: new Date().toISOString()\n        }).eq('id', matchId).select(\"\\n        *,\\n        games (id, name, image),\\n        users!matches_created_by_fkey (id, name, email),\\n        tournaments (id, name, status)\\n      \").single();\n        if (matchError) {\n            console.error('Error updating match with winner:', matchError);\n            throw new Error(\"Failed to assign winner: \".concat(matchError.message));\n        }\n        // Update winner participant\n        const { error: participantError } = await supabase.from('match_participants').update({\n            is_winner: true\n        }).eq('user_id', winnerId).eq('match_id', matchId);\n        if (participantError) {\n            console.error('Error updating winner participant:', participantError);\n        // Don't throw here as match is already updated\n        }\n        // Reset other participants' winner status\n        await supabase.from('match_participants').update({\n            is_winner: false\n        }).eq('match_id', matchId).neq('user_id', winnerId);\n        // Trigger automated winner payout\n        try {\n            console.log(\"\\uD83C\\uDFC6 Triggering automated payout for winner \".concat(winnerId, \" in match \").concat(matchId));\n            // Get match details for prize calculation\n            const match = matchData;\n            const entryFee = match.entry_fee || 0;\n            const maxParticipants = match.max_participants || 1;\n            const prizePool = match.prize_pool || entryFee * maxParticipants;\n            if (prizePool > 0) {\n                var _data_session;\n                // Call backend API to process winner payout\n                const response = await fetch(\"\".concat(\"https://wiggyz-public-75pt8bev8-tausifraja977-gmailcoms-projects.vercel.app/api/v1\" || 0, \"/api/matches/\").concat(matchId, \"/distribute-rewards\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat((_data_session = (await supabase.auth.getSession()).data.session) === null || _data_session === void 0 ? void 0 : _data_session.access_token)\n                    },\n                    body: JSON.stringify({\n                        winner_id: winnerId,\n                        prize_amount: prizePool\n                    })\n                });\n                if (response.ok) {\n                    console.log(\"✅ Automated payout of \".concat(prizePool, \" processed for winner \").concat(winnerId));\n                } else {\n                    console.error(\"❌ Failed to process automated payout: \".concat(response.statusText));\n                }\n            } else {\n                console.log(\"ℹ️ No prize pool to distribute for match \".concat(matchId));\n            }\n        } catch (payoutError) {\n            console.error('Error processing automated payout:', payoutError);\n        // Don't fail the winner assignment due to payout errors\n        }\n        return {\n            data: matchData,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in assignWinner:', error);\n        return {\n            data: null,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Get match statistics\nasync function getMatchStatistics() {\n    try {\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data, error } = await supabase.from('matches').select('status');\n        if (error) {\n            console.error('Error fetching match statistics:', error);\n            throw new Error(\"Failed to fetch match statistics: \".concat(error.message));\n        }\n        const stats = {\n            total: data.length,\n            scheduled: data.filter((m)=>m.status === 'scheduled').length,\n            live: data.filter((m)=>m.status === 'live' || m.status === 'in_progress').length,\n            completed: data.filter((m)=>m.status === 'completed').length,\n            pending_verification: data.filter((m)=>m.status === 'pending_verification').length\n        };\n        return {\n            data: stats,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in getMatchStatistics:', error);\n        return {\n            data: null,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Get available games for match creation\nasync function getGames() {\n    try {\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        const { data, error } = await supabase.from('games').select('id, name, image').order('name');\n        if (error) {\n            console.error('Error fetching games:', error);\n            throw new Error(\"Failed to fetch games: \".concat(error.message));\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in getGames:', error);\n        return {\n            data: null,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Get pending match results for admin verification\nasync function getPendingResults() {\n    let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n    try {\n        var _userData_role;\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        // Get current user session and verify admin role\n        const { data: { session } } = await supabase.auth.getSession();\n        if (!session) {\n            throw new Error('No authenticated session found');\n        }\n        // Verify admin role\n        const { data: userData, error: userError } = await supabase.from('users').select('role').eq('id', session.user.id).single();\n        if (userError || !userData || ![\n            'admin',\n            'super admin'\n        ].includes((_userData_role = userData.role) === null || _userData_role === void 0 ? void 0 : _userData_role.toLowerCase())) {\n            throw new Error('Insufficient permissions - admin role required');\n        }\n        console.log(\"Admin user \".concat(session.user.email, \" (\").concat(userData.role, \") accessing pending results\"));\n        // Fetch directly from Supabase with enhanced query\n        const { data: results, error: fetchError, count } = await supabase.from('match_results').select(\"\\n        *,\\n        matches!inner (\\n          id,\\n          match_description,\\n          status,\\n          match_time,\\n          game_id,\\n          games (name, image)\\n        ),\\n        users!match_results_user_id_fkey (\\n          id,\\n          name,\\n          email\\n        )\\n      \", {\n            count: 'exact'\n        }).eq('verification_status', 'pending').order('submitted_at', {\n            ascending: false\n        }).range(offset, offset + limit - 1);\n        if (fetchError) {\n            throw new Error(\"Failed to fetch pending results: \".concat(fetchError.message));\n        }\n        // Calculate if there are more results\n        const hasMore = count ? count > offset + results.length : false;\n        // Transform results to add screenshot_url property for compatibility\n        const transformedResults = results.map((result)=>{\n            var _result_matches, _result_matches_games, _result_matches1;\n            // Handle different field names for screenshots\n            const screenshot_url = result.screenshot_urls && result.screenshot_urls.length > 0 ? result.screenshot_urls[0] : result.screenshots && result.screenshots.length > 0 ? result.screenshots[0] : null;\n            return {\n                ...result,\n                screenshot_url,\n                // Always ensure screenshots and screenshot_urls are set properly\n                screenshots: result.screenshots || result.screenshot_urls || [],\n                screenshot_urls: result.screenshot_urls || result.screenshots || [],\n                // Ensure position field is available\n                position: result.placement || result.position,\n                // Ensure match description is available\n                match_description: ((_result_matches = result.matches) === null || _result_matches === void 0 ? void 0 : _result_matches.match_description) || 'No description',\n                // Ensure game name is available\n                game_name: ((_result_matches1 = result.matches) === null || _result_matches1 === void 0 ? void 0 : (_result_matches_games = _result_matches1.games) === null || _result_matches_games === void 0 ? void 0 : _result_matches_games.name) || 'Unknown Game'\n            };\n        });\n        console.log(\"Successfully fetched \".concat(transformedResults.length, \" pending results for admin review\"));\n        return {\n            data: {\n                results: transformedResults,\n                count: count || 0,\n                pagination: {\n                    limit,\n                    offset,\n                    hasMore\n                }\n            },\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in getPendingResults:', error);\n        return {\n            data: null,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Verify a match result\nasync function verifyMatchResult(resultId, action, notes) {\n    try {\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        // Get user info from Supabase session for admin ID\n        const { data: { user } } = await supabase.auth.getUser();\n        if (!user) {\n            throw new Error('No authenticated user available');\n        }\n        // Update directly in Supabase\n        const verificationStatus = action === 'approve' ? 'verified' : 'rejected';\n        const verifiedAt = new Date().toISOString();\n        const { error: updateError } = await supabase.from('match_results').update({\n            verification_status: verificationStatus,\n            verified_at: verifiedAt,\n            verified_by: user.id,\n            admin_notes: notes || null\n        }).eq('id', resultId);\n        if (updateError) {\n            throw new Error(\"Failed to verify match result: \".concat(updateError.message));\n        }\n        // If we approved, also update the match status to indicate verification progress\n        if (action === 'approve') {\n            // First get the match ID from the result\n            const { data: resultData } = await supabase.from('match_results').select('match_id, user_id').eq('id', resultId).single();\n            if (resultData === null || resultData === void 0 ? void 0 : resultData.match_id) {\n                const matchId = resultData.match_id;\n                // Check if all results for this match are now verified\n                const { data: pendingResults, count } = await supabase.from('match_results').select('*', {\n                    count: 'exact'\n                }).eq('match_id', matchId).eq('verification_status', 'pending');\n                // Update match status to indicate verification progress\n                if (count === 0) {\n                    // All results verified - mark as ready for winner assignment\n                    await supabase.from('matches').update({\n                        status: 'pending_winner_assignment',\n                        admin_verified: true,\n                        updated_at: new Date().toISOString()\n                    }).eq('id', matchId);\n                } else {\n                    // Some results still pending - mark as partially verified\n                    await supabase.from('matches').update({\n                        status: 'pending_verification',\n                        updated_at: new Date().toISOString()\n                    }).eq('id', matchId);\n                }\n            }\n        }\n        return {\n            success: true,\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error in verifyMatchResult (\".concat(action, \"):\"), error);\n        return {\n            success: false,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n/**\r\n * Get matches pending verification directly from Supabase\r\n * These are matches with status 'pending_verification' that need admin review\r\n */ async function getPendingVerificationMatches() {\n    let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n    try {\n        const supabase = (0,_lib_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n        // Fetch matches with 'pending_verification' status directly from Supabase\n        const { data, error: fetchError, count } = await supabase.from('matches').select(\"\\n        *,\\n        games (id, name, image),\\n        users!matches_created_by_fkey (id, name, email),\\n        tournaments (id, name, status),\\n        match_participants (\\n          id,\\n          user_id,\\n          participant_type,\\n          team_name,\\n          team_position,\\n          joined_at,\\n          result_score,\\n          result_position,\\n          result_kills,\\n          result_screenshot_url,\\n          result_submitted_at,\\n          is_winner,\\n          users (id, name, email)\\n        )\\n      \", {\n            count: 'exact'\n        }).eq('status', 'pending_verification').order('updated_at', {\n            ascending: false\n        }).range(offset, offset + limit - 1);\n        if (fetchError) {\n            throw new Error(\"Failed to fetch pending verification matches: \".concat(fetchError.message));\n        }\n        return {\n            data: data,\n            count: count || 0,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error in getPendingVerificationMatches:', error);\n        return {\n            data: null,\n            count: 0,\n            error: error instanceof Error ? error : new Error('Unknown error occurred')\n        };\n    }\n}\n// Export matchService object containing all functions\nconst matchService = {\n    getAllMatches,\n    getMatchById,\n    createMatch,\n    updateMatch,\n    deleteMatch,\n    assignWinner,\n    getMatchStatistics,\n    getGames,\n    getPendingResults,\n    verifyMatchResult,\n    getPendingVerificationMatches\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/matchService.ts\n"));

/***/ })

});