/// Advanced performance monitoring and optimization for the reward system
/// Tracks metrics, manages memory, and provides performance insights

import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Performance metrics for reward operations
class RewardPerformanceMetrics {
  final String operation;
  final DateTime startTime;
  final DateTime endTime;
  final Duration duration;
  final bool success;
  final String? error;
  final Map<String, dynamic> metadata;

  const RewardPerformanceMetrics({
    required this.operation,
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.success,
    this.error,
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'operation': operation,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'duration': duration.inMilliseconds,
      'success': success,
      'error': error,
      'metadata': metadata,
    };
  }
}

/// Performance monitor for reward system operations
class RewardPerformanceMonitor {
  static final RewardPerformanceMonitor _instance = RewardPerformanceMonitor._internal();
  factory RewardPerformanceMonitor() => _instance;
  RewardPerformanceMonitor._internal();

  final List<RewardPerformanceMetrics> _metrics = [];
  final Map<String, DateTime> _operationStartTimes = {};
  final Map<String, int> _operationCounts = {};
  final Map<String, Duration> _operationTotalDurations = {};

  // Performance thresholds
  static const Duration _slowOperationThreshold = Duration(seconds: 2);
  static const int _maxMetricsHistory = 100;

  /// Start tracking an operation
  void startOperation(String operation, {Map<String, dynamic>? metadata}) {
    final operationId = '${operation}_${DateTime.now().millisecondsSinceEpoch}';
    _operationStartTimes[operationId] = DateTime.now();
    
    if (kDebugMode) {
      developer.Timeline.startSync(operation, arguments: metadata);
    }
  }

  /// End tracking an operation
  void endOperation(
    String operation, {
    bool success = true,
    String? error,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    final operationId = _operationStartTimes.keys
        .where((key) => key.startsWith(operation))
        .lastOrNull;

    if (operationId == null) {
      debugPrint('Warning: No start time found for operation: $operation');
      return;
    }

    final startTime = _operationStartTimes.remove(operationId)!;
    final duration = now.difference(startTime);

    final metrics = RewardPerformanceMetrics(
      operation: operation,
      startTime: startTime,
      endTime: now,
      duration: duration,
      success: success,
      error: error,
      metadata: metadata ?? {},
    );

    _addMetrics(metrics);

    if (kDebugMode) {
      developer.Timeline.finishSync();
      
      // Log slow operations
      if (duration > _slowOperationThreshold) {
        debugPrint('Slow operation detected: $operation took ${duration.inMilliseconds}ms');
      }
    }
  }

  /// Add metrics to history
  void _addMetrics(RewardPerformanceMetrics metrics) {
    _metrics.add(metrics);
    
    // Update operation counts and durations
    _operationCounts[metrics.operation] = (_operationCounts[metrics.operation] ?? 0) + 1;
    _operationTotalDurations[metrics.operation] = 
        (_operationTotalDurations[metrics.operation] ?? Duration.zero) + metrics.duration;

    // Limit history size
    if (_metrics.length > _maxMetricsHistory) {
      _metrics.removeAt(0);
    }
  }

  /// Get performance summary
  Map<String, dynamic> getPerformanceSummary() {
    final summary = <String, dynamic>{};
    
    for (final operation in _operationCounts.keys) {
      final count = _operationCounts[operation]!;
      final totalDuration = _operationTotalDurations[operation]!;
      final averageDuration = totalDuration ~/ count;
      
      final operationMetrics = _metrics.where((m) => m.operation == operation);
      final successRate = operationMetrics.where((m) => m.success).length / operationMetrics.length;
      
      summary[operation] = {
        'count': count,
        'averageDuration': averageDuration.inMilliseconds,
        'totalDuration': totalDuration.inMilliseconds,
        'successRate': successRate,
        'slowOperations': operationMetrics.where((m) => m.duration > _slowOperationThreshold).length,
      };
    }
    
    return summary;
  }

  /// Get recent metrics
  List<RewardPerformanceMetrics> getRecentMetrics({int limit = 20}) {
    return _metrics.reversed.take(limit).toList();
  }

  /// Get metrics for specific operation
  List<RewardPerformanceMetrics> getMetricsForOperation(String operation) {
    return _metrics.where((m) => m.operation == operation).toList();
  }

  /// Clear all metrics
  void clearMetrics() {
    _metrics.clear();
    _operationCounts.clear();
    _operationTotalDurations.clear();
  }

  /// Check if operation is performing well
  bool isOperationHealthy(String operation) {
    final operationMetrics = _metrics.where((m) => m.operation == operation);
    if (operationMetrics.isEmpty) return true;

    final recentMetrics = operationMetrics.toList()..sort((a, b) => b.startTime.compareTo(a.startTime));
    final recent10 = recentMetrics.take(10);

    // Check success rate (should be > 90%)
    final successRate = recent10.where((m) => m.success).length / recent10.length;
    if (successRate < 0.9) return false;

    // Check average duration (should be reasonable)
    final averageDuration = recent10.fold<Duration>(
      Duration.zero,
      (sum, m) => sum + m.duration,
    ) ~/ recent10.length;
    
    if (averageDuration > _slowOperationThreshold) return false;

    return true;
  }
}

/// Memory manager for reward system
class RewardMemoryManager {
  static final RewardMemoryManager _instance = RewardMemoryManager._internal();
  factory RewardMemoryManager() => _instance;
  RewardMemoryManager._internal();

  Timer? _memoryCheckTimer;
  final Map<String, int> _memoryUsage = {};

  /// Start memory monitoring
  void startMonitoring() {
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _checkMemoryUsage();
    });
  }

  /// Stop memory monitoring
  void stopMonitoring() {
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = null;
  }

  /// Check current memory usage
  void _checkMemoryUsage() {
    if (kDebugMode) {
      // Get memory info (this would need platform-specific implementation)
      final timestamp = DateTime.now().toIso8601String();
      debugPrint('Memory check at $timestamp');
      
      // Trigger garbage collection if memory usage is high
      _triggerGarbageCollection();
    }
  }

  /// Trigger garbage collection
  void _triggerGarbageCollection() {
    // Force garbage collection
    developer.Timeline.startSync('GarbageCollection');
    
    // This is a hint to the VM to run garbage collection
    // The actual implementation depends on the platform
    
    developer.Timeline.finishSync();
  }

  /// Optimize memory usage for reward data
  void optimizeRewardData<T>(List<T> data, {int maxItems = 100}) {
    if (data.length > maxItems) {
      final itemsToRemove = data.length - maxItems;
      data.removeRange(0, itemsToRemove);
      debugPrint('Optimized memory: removed $itemsToRemove items');
    }
  }

  /// Get memory usage estimate
  Map<String, dynamic> getMemoryUsage() {
    return Map.from(_memoryUsage);
  }

  /// Track memory usage for specific component
  void trackMemoryUsage(String component, int bytes) {
    _memoryUsage[component] = bytes;
  }
}

/// Image cache optimizer for reward icons and images
class RewardImageCacheOptimizer {
  static final RewardImageCacheOptimizer _instance = RewardImageCacheOptimizer._internal();
  factory RewardImageCacheOptimizer() => _instance;
  RewardImageCacheOptimizer._internal();

  static const int _maxCacheSize = 50 * 1024 * 1024; // 50MB
  static const int _maxCacheItems = 200;

  /// Optimize image cache
  void optimizeImageCache() {
    // This would integrate with Flutter's image cache
    // PaintingBinding.instance.imageCache.clear();
    
    // Set cache limits
    PaintingBinding.instance.imageCache.maximumSize = _maxCacheItems;
    PaintingBinding.instance.imageCache.maximumSizeBytes = _maxCacheSize;
    
    debugPrint('Image cache optimized: max items=$_maxCacheItems, max size=${_maxCacheSize ~/ (1024 * 1024)}MB');
  }

  /// Preload important reward images
  Future<void> preloadRewardImages(List<String> imageUrls, BuildContext context) async {
    for (final url in imageUrls.take(10)) { // Limit preloading
      try {
        await precacheImage(NetworkImage(url), context);
      } catch (e) {
        debugPrint('Failed to preload image: $url - $e');
      }
    }
  }

  /// Clear image cache
  void clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
    debugPrint('Image cache cleared');
  }
}



/// Performance optimization utilities
class RewardPerformanceUtils {
  /// Debounce API calls to prevent excessive requests
  static final Map<String, Timer> _debounceTimers = {};
  
  static void debounceApiCall(String key, Duration delay, VoidCallback callback) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, () {
      callback();
      _debounceTimers.remove(key);
    });
  }

  /// Batch multiple operations together
  static void batchOperations(List<VoidCallback> operations, {Duration delay = const Duration(milliseconds: 100)}) {
    Timer(delay, () {
      for (final operation in operations) {
        try {
          operation();
        } catch (e) {
          debugPrint('Error in batched operation: $e');
        }
      }
    });
  }

  /// Lazy load data with pagination
  static List<T> paginateData<T>(List<T> data, int page, int pageSize) {
    final startIndex = page * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, data.length);
    
    if (startIndex >= data.length) return [];
    return data.sublist(startIndex, endIndex);
  }

  /// Efficient list comparison
  static bool areListsEqual<T>(List<T> list1, List<T> list2) {
    if (list1.length != list2.length) return false;
    
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    
    return true;
  }

  /// Clean up resources
  static void cleanup() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    
    RewardMemoryManager().stopMonitoring();
    RewardImageCacheOptimizer().clearImageCache();
  }
}
