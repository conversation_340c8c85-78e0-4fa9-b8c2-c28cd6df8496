import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../core/api/api_config.dart';

enum DeadlineWarningLevel { normal, warning, critical, expired }

class DeadlineStatus {
  final String matchId;
  final DateTime? deadline;
  final int timeRemaining; // milliseconds
  final bool hasExpired;
  final bool userHasSubmitted;
  final bool canSubmitResults;
  final int participantsSubmitted;
  final int totalParticipants;
  final String matchStatus;
  final DeadlineWarningLevel warningLevel;

  DeadlineStatus({
    required this.matchId,
    this.deadline,
    required this.timeRemaining,
    required this.hasExpired,
    required this.userHasSubmitted,
    required this.canSubmitResults,
    required this.participantsSubmitted,
    required this.totalParticipants,
    required this.matchStatus,
    required this.warningLevel,
  });

  factory DeadlineStatus.fromJson(Map<String, dynamic> json) {
    return DeadlineStatus(
      matchId: json['matchId'],
      deadline: json['deadline'] != null ? DateTime.parse(json['deadline']) : null,
      timeRemaining: json['timeRemaining'] ?? 0,
      hasExpired: json['hasExpired'] ?? false,
      userHasSubmitted: json['userHasSubmitted'] ?? false,
      canSubmitResults: json['canSubmitResults'] ?? false,
      participantsSubmitted: json['participantsSubmitted'] ?? 0,
      totalParticipants: json['totalParticipants'] ?? 0,
      matchStatus: json['matchStatus'] ?? '',
      warningLevel: _parseWarningLevel(json['deadlineWarningLevel']),
    );
  }

  static DeadlineWarningLevel _parseWarningLevel(String? level) {
    switch (level) {
      case 'warning':
        return DeadlineWarningLevel.warning;
      case 'critical':
        return DeadlineWarningLevel.critical;
      case 'expired':
        return DeadlineWarningLevel.expired;
      default:
        return DeadlineWarningLevel.normal;
    }
  }
}

class DeadlineCountdownWidget extends StatefulWidget {
  final String matchId;
  final VoidCallback? onDeadlineExpired;
  final VoidCallback? onSubmitPressed;

  const DeadlineCountdownWidget({
    Key? key,
    required this.matchId,
    this.onDeadlineExpired,
    this.onSubmitPressed,
  }) : super(key: key);

  @override
  State<DeadlineCountdownWidget> createState() => _DeadlineCountdownWidgetState();
}

class _DeadlineCountdownWidgetState extends State<DeadlineCountdownWidget>
    with TickerProviderStateMixin {
  Timer? _countdownTimer;
  Timer? _refreshTimer;
  DeadlineStatus? _deadlineStatus;
  bool _isLoading = true;
  String? _errorMessage;
  late AnimationController _blinkController;
  late Animation<double> _blinkAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _fetchDeadlineStatus();
    _startRefreshTimer();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    _refreshTimer?.cancel();
    _blinkController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _blinkController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _blinkAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _blinkController, curve: Curves.easeInOut),
    );
  }

  void _startRefreshTimer() {
    // Refresh deadline status every 30 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _fetchDeadlineStatus();
    });
  }

  Future<void> _fetchDeadlineStatus() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final token = await authProvider.token;

      if (token == null) {
        setState(() {
          _errorMessage = 'Not authenticated';
          _isLoading = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/matches/${widget.matchId}/deadline-status'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final newStatus = DeadlineStatus.fromJson(data);
        
        setState(() {
          _deadlineStatus = newStatus;
          _isLoading = false;
          _errorMessage = null;
        });

        _startCountdownTimer();
        _updateBlinkAnimation();

      } else if (response.statusCode == 403) {
        // User is not a participant - hide widget
        setState(() {
          _deadlineStatus = null;
          _isLoading = false;
          _errorMessage = null;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load deadline status';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Network error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  void _startCountdownTimer() {
    _countdownTimer?.cancel();
    
    if (_deadlineStatus?.hasExpired == true || _deadlineStatus?.userHasSubmitted == true) {
      return;
    }

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_deadlineStatus?.deadline != null) {
        final now = DateTime.now();
        final timeRemaining = _deadlineStatus!.deadline!.difference(now).inMilliseconds;
        
        if (timeRemaining <= 0) {
          // Deadline expired
          timer.cancel();
          widget.onDeadlineExpired?.call();
          _fetchDeadlineStatus(); // Refresh to get updated status
        } else {
          setState(() {
            _deadlineStatus = DeadlineStatus(
              matchId: _deadlineStatus!.matchId,
              deadline: _deadlineStatus!.deadline,
              timeRemaining: timeRemaining,
              hasExpired: false,
              userHasSubmitted: _deadlineStatus!.userHasSubmitted,
              canSubmitResults: _deadlineStatus!.canSubmitResults,
              participantsSubmitted: _deadlineStatus!.participantsSubmitted,
              totalParticipants: _deadlineStatus!.totalParticipants,
              matchStatus: _deadlineStatus!.matchStatus,
              warningLevel: _getWarningLevel(timeRemaining),
            );
          });
          _updateBlinkAnimation();
        }
      }
    });
  }

  DeadlineWarningLevel _getWarningLevel(int timeRemainingMs) {
    if (timeRemainingMs <= 0) return DeadlineWarningLevel.expired;
    if (timeRemainingMs <= 2 * 60 * 1000) return DeadlineWarningLevel.critical; // 2 minutes
    if (timeRemainingMs <= 10 * 60 * 1000) return DeadlineWarningLevel.warning; // 10 minutes
    return DeadlineWarningLevel.normal;
  }

  void _updateBlinkAnimation() {
    if (_deadlineStatus?.warningLevel == DeadlineWarningLevel.critical) {
      _blinkController.repeat(reverse: true);
    } else {
      _blinkController.stop();
      _blinkController.reset();
    }
  }

  String _formatTimeRemaining(int milliseconds) {
    if (milliseconds <= 0) return "00:00";
    
    final duration = Duration(milliseconds: milliseconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return "${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
    } else {
      return "${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
    }
  }

  Color _getWarningColor() {
    switch (_deadlineStatus?.warningLevel) {
      case DeadlineWarningLevel.critical:
        return Colors.red;
      case DeadlineWarningLevel.warning:
        return Colors.orange;
      case DeadlineWarningLevel.expired:
        return Colors.grey;
      default:
        return Colors.green;
    }
  }

  IconData _getWarningIcon() {
    switch (_deadlineStatus?.warningLevel) {
      case DeadlineWarningLevel.critical:
        return Icons.warning;
      case DeadlineWarningLevel.warning:
        return Icons.access_time;
      case DeadlineWarningLevel.expired:
        return Icons.cancel;
      default:
        return Icons.schedule;
    }
  }

  String _getWarningMessage() {
    if (_deadlineStatus?.userHasSubmitted == true) {
      return "✅ Results submitted successfully";
    }
    
    switch (_deadlineStatus?.warningLevel) {
      case DeadlineWarningLevel.critical:
        return "🚨 URGENT: Submit results now!";
      case DeadlineWarningLevel.warning:
        return "⚠️ Submit results soon";
      case DeadlineWarningLevel.expired:
        return "❌ Deadline passed. Results auto-finalized.";
      default:
        return "Submit results by:";
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        height: 60,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null) {
      return Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.error, color: Colors.red, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _errorMessage!,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.red,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _errorMessage = null;
                });
                _fetchDeadlineStatus();
              },
              child: Text('Retry', style: GoogleFonts.poppins(fontSize: 12)),
            ),
          ],
        ),
      );
    }

    // Don't show widget if user is not a participant or no deadline is set
    if (_deadlineStatus == null || _deadlineStatus!.deadline == null) {
      return const SizedBox.shrink();
    }

    // Don't show if user already submitted results (unless it's to show success)
    if (_deadlineStatus!.userHasSubmitted && _deadlineStatus!.warningLevel != DeadlineWarningLevel.expired) {
      return Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                "✅ Results submitted successfully",
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return AnimatedBuilder(
      animation: _blinkAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _deadlineStatus!.warningLevel == DeadlineWarningLevel.critical 
              ? _blinkAnimation.value 
              : 1.0,
          child: Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: _getWarningColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getWarningColor().withOpacity(0.3),
                width: 2,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      _getWarningIcon(),
                      color: _getWarningColor(),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getWarningMessage(),
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: _getWarningColor(),
                            ),
                          ),
                          if (!_deadlineStatus!.hasExpired && !_deadlineStatus!.userHasSubmitted) ...[
                            const SizedBox(height: 4),
                            Text(
                              _formatTimeRemaining(_deadlineStatus!.timeRemaining),
                              style: GoogleFonts.poppins(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: _getWarningColor(),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    if (_deadlineStatus!.canSubmitResults && widget.onSubmitPressed != null)
                      ElevatedButton(
                        onPressed: widget.onSubmitPressed,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _getWarningColor(),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        ),
                        child: Text(
                          'Submit Now',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
                if (_deadlineStatus!.totalParticipants > 0) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.people, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        "${_deadlineStatus!.participantsSubmitted}/${_deadlineStatus!.totalParticipants} submitted",
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
