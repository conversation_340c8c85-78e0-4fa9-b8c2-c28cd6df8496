import 'package:intl/intl.dart';
import 'package:flutter/material.dart';

/// Utility functions for match-related operations
class MatchUtils {
  /// Platform fee percentage (10%)
  static const double platformFeePercentage = 0.10;

  /// Calculate prize pool from entry fee and participant count
  /// Formula: (Entry Fee × Number of Participants) - Platform Fee (10%)
  static double calculatePrizePool(double entryFee, int participantCount) {
    if (entryFee <= 0 || participantCount <= 0) {
      return 0.0;
    }

    final totalCollected = entryFee * participantCount;
    final platformFee = totalCollected * platformFeePercentage;
    final prizePool = totalCollected - platformFee;

    return prizePool;
  }

  /// Calculate maximum prize pool from entry fee and max participants
  /// Formula: (Entry Fee × Max Participants) - Platform Fee (10%)
  static double calculateMaxPrizePool(double entryFee, int maxParticipants) {
    if (entryFee <= 0 || maxParticipants <= 0) {
      return 0.0;
    }

    final totalCollected = entryFee * maxParticipants;
    final platformFee = totalCollected * platformFeePercentage;
    final maxPrizePool = totalCollected - platformFee;

    return maxPrizePool;
  }

  /// Format prize pool for display
  static String formatPrizePool(double entryFee, int participantCount, {String currency = '₹'}) {
    if (entryFee <= 0) {
      return 'Free';
    }

    final prizePool = calculatePrizePool(entryFee, participantCount);
    if (prizePool <= 0) {
      return 'TBD';
    }

    return '$currency${prizePool.toStringAsFixed(0)}';
  }

  /// Format both current and maximum prize pools for display
  /// Format: "Current: ₹36 | Max: ₹90" or "Free" for zero entry fee
  static String formatCurrentAndMaxPrizePool(
    double entryFee,
    int currentParticipants,
    int maxParticipants,
    {String currency = '₹'}
  ) {
    if (entryFee <= 0) {
      return 'Free';
    }

    // If maxParticipants is 0 or invalid, use a reasonable default
    final effectiveMaxParticipants = maxParticipants > 0 ? maxParticipants : 10;

    // Only show single value if current participants equals max AND max is valid (> 0)
    if (maxParticipants > 0 && currentParticipants >= maxParticipants) {
      final prizePool = calculatePrizePool(entryFee, currentParticipants);
      if (prizePool <= 0) return 'TBD';
      return '$currency${prizePool.toStringAsFixed(0)}';
    }

    final currentPrize = calculatePrizePool(entryFee, currentParticipants);
    final maxPrize = calculateMaxPrizePool(entryFee, effectiveMaxParticipants);

    if (currentPrize <= 0 && maxPrize <= 0) {
      return 'TBD';
    }

    final currentText = currentPrize > 0 ? '$currency${currentPrize.toStringAsFixed(0)}' : 'TBD';
    final maxText = maxPrize > 0 ? '$currency${maxPrize.toStringAsFixed(0)}' : 'TBD';

    return 'Current: $currentText | Max: $maxText';
  }

  /// Generate match title from available data
  static String generateMatchTitle(String? title, String? gameName, String matchId) {
    if (title != null && title.isNotEmpty) {
      return title;
    }
    
    if (gameName != null && gameName.isNotEmpty) {
      return '$gameName Match';
    }
    
    // Fallback to short match ID
    final shortId = matchId.length > 8 ? matchId.substring(0, 8) : matchId;
    return 'Match #$shortId';
  }

  /// Convert UTC DateTime to local timezone and format for display
  static String formatLocalDateTime(DateTime? utcDateTime) {
    if (utcDateTime == null) {
      return 'TBD';
    }

    // Convert UTC to local timezone
    final localDateTime = utcDateTime.toLocal();
    final now = DateTime.now();
    
    // Check if it's today
    if (isSameDay(localDateTime, now)) {
      return 'Today, ${DateFormat('h:mm a').format(localDateTime)}';
    }
    
    // Check if it's tomorrow
    final tomorrow = now.add(const Duration(days: 1));
    if (isSameDay(localDateTime, tomorrow)) {
      return 'Tomorrow, ${DateFormat('h:mm a').format(localDateTime)}';
    }
    
    // Check if it's within this week
    final daysUntil = localDateTime.difference(now).inDays;
    if (daysUntil >= 0 && daysUntil <= 7) {
      return DateFormat('EEE, h:mm a').format(localDateTime); // Mon, 2:30 PM
    }
    
    // For dates further out, show full date
    return DateFormat('MMM d, h:mm a').format(localDateTime); // Jan 22, 2:30 PM
  }

  /// Format relative time for upcoming matches
  static String formatRelativeTime(DateTime? utcDateTime) {
    if (utcDateTime == null) {
      return '';
    }

    final localDateTime = utcDateTime.toLocal();
    final now = DateTime.now();
    final difference = localDateTime.difference(now);

    if (difference.isNegative) {
      // Past time
      final pastDifference = now.difference(localDateTime);
      if (pastDifference.inMinutes < 60) {
        return '${pastDifference.inMinutes}m ago';
      } else if (pastDifference.inHours < 24) {
        return '${pastDifference.inHours}h ago';
      } else {
        return '${pastDifference.inDays}d ago';
      }
    } else {
      // Future time
      if (difference.inMinutes < 60) {
        return 'In ${difference.inMinutes}m';
      } else if (difference.inHours < 24) {
        return 'In ${difference.inHours}h';
      } else if (difference.inDays < 7) {
        return 'In ${difference.inDays}d';
      } else {
        return formatLocalDateTime(utcDateTime);
      }
    }
  }

  /// Check if two DateTime objects are on the same day
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Format deadline time with urgency indicators
  static String formatDeadlineTime(DateTime? utcDeadline) {
    if (utcDeadline == null) {
      return 'No deadline';
    }

    final localDeadline = utcDeadline.toLocal();
    final now = DateTime.now();
    final difference = localDeadline.difference(now);

    if (difference.isNegative) {
      return 'Expired';
    }

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m left';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h left';
    } else {
      return formatLocalDateTime(utcDeadline);
    }
  }

  /// Get participant count from match participants list
  static int getParticipantCount(List<dynamic>? participants) {
    if (participants == null) return 0;
    
    // Count only players, not spectators
    return participants.where((p) => 
      p is Map<String, dynamic> && 
      p['participant_type'] == 'player'
    ).length;
  }

  /// Format currency amount
  static String formatCurrency(double amount, {String currency = '₹'}) {
    if (amount == 0) {
      return 'Free';
    }
    return '$currency${amount.toStringAsFixed(0)}';
  }

  /// Get match status color
  static Color getMatchStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'live':
      case 'in_progress':
        return const Color(0xFFFF4444); // Red
      case 'upcoming':
      case 'scheduled':
        return const Color(0xFFFF9800); // Orange
      case 'completed':
        return const Color(0xFF4CAF50); // Green
      case 'cancelled':
        return const Color(0xFF757575); // Grey
      default:
        return const Color(0xFF2196F3); // Blue
    }
  }

  /// Get time remaining badge text for upcoming matches
  static String getTimeRemainingBadge(DateTime? utcDateTime) {
    if (utcDateTime == null) return 'TBD';

    final localDateTime = utcDateTime.toLocal();
    final now = DateTime.now();
    final difference = localDateTime.difference(now);

    if (difference.isNegative) {
      return 'Started';
    }

    if (difference.inMinutes < 1) {
      return 'Starting soon';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }
}
