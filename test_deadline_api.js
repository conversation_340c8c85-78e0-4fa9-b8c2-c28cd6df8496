const http = require('http');

// Login to get a fresh token
const login = async () => {
  const loginData = JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  });

  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(loginData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('Login Status Code:', res.statusCode);
        console.log('Login Response:', data);
        try {
          const jsonData = JSON.parse(data);
          if (jsonData.access_token) {
            resolve(jsonData.access_token);
          } else {
            reject(new Error('No access token in response: ' + JSON.stringify(jsonData)));
          }
        } catch (e) {
          reject(new Error('Failed to parse login response: ' + data));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(loginData);
    req.end();
  });
};

// Test the deadline API endpoint
const testDeadlineAPI = async (token) => {
  const matchId = 'ffd2a47a-50c6-4eb1-befb-16f36fa90e6e';
  
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: `/api/v1/matches/${matchId}/deadline-status`,
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('Status Code:', res.statusCode);
        console.log('Response Headers:', res.headers);
        console.log('Response Body:', data);
        
        try {
          const jsonData = JSON.parse(data);
          console.log('Parsed JSON:', JSON.stringify(jsonData, null, 2));
          resolve(jsonData);
        } catch (e) {
          console.log('Failed to parse JSON:', e.message);
          resolve(data);
        }
      });
    });

    req.on('error', (error) => {
      console.error('Request error:', error);
      reject(error);
    });

    req.end();
  });
};

// Run the test
const runTest = async () => {
  try {
    console.log('🔐 Logging in to get fresh token...');
    const token = await login();
    console.log('✅ Login successful, token obtained');

    console.log('🧪 Testing deadline API...');
    const result = await testDeadlineAPI(token);
    console.log('✅ Test completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
};

runTest();
