/**
 * Comprehensive test suite for Razorpay integration
 * Tests payment flow, security measures, and error handling
 */
import request from 'supertest';
import { app } from '../server';
import { razorpayService } from '../services/razorpayService';
import { supabase } from '../config/supabase';

describe('Razorpay Integration Tests', () => {
  let authToken: string;
  let userId: string;

  beforeAll(async () => {
    // Setup test user and authentication
    const testUser = {
      email: '<EMAIL>',
      password: 'testpassword123',
      name: 'Test User'
    };

    // Register test user
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send(testUser);

    // Login to get auth token
    const loginResponse = await request(app)
      .post('/api/v1/auth/login')
      .send({
        email: testUser.email,
        password: testUser.password
      });

    authToken = loginResponse.body.data.accessToken;
    userId = loginResponse.body.data.user.id;
  });

  afterAll(async () => {
    // Cleanup test data
    await supabase
      .from('wallet_transactions')
      .delete()
      .eq('user_id', userId);

    await supabase
      .from('wallets')
      .delete()
      .eq('user_id', userId);

    await supabase
      .from('users')
      .delete()
      .eq('id', userId);
  });

  describe('Payment Initiation', () => {
    test('should create Razorpay order for valid top-up request', async () => {
      const topUpData = {
        amount: 100, // ₹100
        currency: 'INR',
        payment_method: 'upi'
      };

      const response = await request(app)
        .post('/api/v1/wallet/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send(topUpData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('order_id');
      expect(response.body.data).toHaveProperty('key_id');
      expect(response.body.data).toHaveProperty('transaction_id');
    });

    test('should reject invalid amount', async () => {
      const topUpData = {
        amount: 0.5, // Below minimum
        currency: 'INR',
        payment_method: 'upi'
      };

      const response = await request(app)
        .post('/api/v1/wallet/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send(topUpData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Amount too low');
    });

    test('should reject excessive amount', async () => {
      const topUpData = {
        amount: 1000000, // Above maximum
        currency: 'INR',
        payment_method: 'upi'
      };

      const response = await request(app)
        .post('/api/v1/wallet/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send(topUpData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Amount too high');
    });

    test('should enforce rate limiting', async () => {
      const topUpData = {
        amount: 100,
        currency: 'INR',
        payment_method: 'upi'
      };

      // Make multiple requests quickly
      const requests = Array(6).fill(null).map(() =>
        request(app)
          .post('/api/v1/wallet/topup')
          .set('Authorization', `Bearer ${authToken}`)
          .send(topUpData)
      );

      const responses = await Promise.all(requests);
      
      // At least one should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Payment Verification', () => {
    let orderId: string;
    let transactionId: string;

    beforeEach(async () => {
      // Create a test order
      const topUpResponse = await request(app)
        .post('/api/v1/wallet/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 100,
          currency: 'INR',
          payment_method: 'upi'
        });

      orderId = topUpResponse.body.data.order_id;
      transactionId = topUpResponse.body.data.transaction_id;
    });

    test('should verify valid payment signature', async () => {
      // Mock valid Razorpay response
      const paymentId = 'pay_test123';
      const signature = generateTestSignature(orderId, paymentId);

      const verificationData = {
        razorpay_order_id: orderId,
        razorpay_payment_id: paymentId,
        razorpay_signature: signature,
        transaction_id: transactionId
      };

      // Mock Razorpay service methods
      jest.spyOn(razorpayService, 'verifyPaymentSignature').mockReturnValue(true);
      jest.spyOn(razorpayService, 'fetchPayment').mockResolvedValue({
        id: paymentId,
        status: 'captured',
        amount: 10000, // ₹100 in paise
        currency: 'INR',
        method: 'upi'
      } as any);

      const response = await request(app)
        .post('/api/v1/wallet/verify-payment')
        .set('Authorization', `Bearer ${authToken}`)
        .send(verificationData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('newBalance');
    });

    test('should reject invalid payment signature', async () => {
      const verificationData = {
        razorpay_order_id: orderId,
        razorpay_payment_id: 'pay_test123',
        razorpay_signature: 'invalid_signature',
        transaction_id: transactionId
      };

      jest.spyOn(razorpayService, 'verifyPaymentSignature').mockReturnValue(false);

      const response = await request(app)
        .post('/api/v1/wallet/verify-payment')
        .set('Authorization', `Bearer ${authToken}`)
        .send(verificationData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Invalid payment signature');
    });

    test('should reject uncaptured payment', async () => {
      const paymentId = 'pay_test123';
      const signature = generateTestSignature(orderId, paymentId);

      const verificationData = {
        razorpay_order_id: orderId,
        razorpay_payment_id: paymentId,
        razorpay_signature: signature,
        transaction_id: transactionId
      };

      jest.spyOn(razorpayService, 'verifyPaymentSignature').mockReturnValue(true);
      jest.spyOn(razorpayService, 'fetchPayment').mockResolvedValue({
        id: paymentId,
        status: 'authorized', // Not captured
        amount: 10000,
        currency: 'INR',
        method: 'upi'
      } as any);

      const response = await request(app)
        .post('/api/v1/wallet/verify-payment')
        .set('Authorization', `Bearer ${authToken}`)
        .send(verificationData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Payment not completed');
    });
  });

  describe('Webhook Processing', () => {
    test('should process valid Razorpay webhook', async () => {
      const webhookPayload = {
        entity: 'event',
        account_id: 'acc_test123',
        event: 'payment.captured',
        contains: ['payment'],
        payload: {
          payment: {
            entity: {
              id: 'pay_test123',
              order_id: 'order_test123',
              amount: 10000,
              currency: 'INR',
              status: 'captured',
              method: 'upi',
              notes: {
                transaction_id: 'test-transaction-id'
              }
            }
          }
        },
        created_at: Math.floor(Date.now() / 1000)
      };

      const signature = generateWebhookSignature(JSON.stringify(webhookPayload));

      const response = await request(app)
        .post('/api/v1/wallet/webhook/razorpay')
        .set('X-Razorpay-Signature', signature)
        .send(webhookPayload);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('should reject webhook with invalid signature', async () => {
      const webhookPayload = {
        entity: 'event',
        account_id: 'acc_test123',
        event: 'payment.captured',
        contains: ['payment'],
        payload: {},
        created_at: Math.floor(Date.now() / 1000)
      };

      const response = await request(app)
        .post('/api/v1/wallet/webhook/razorpay')
        .set('X-Razorpay-Signature', 'invalid_signature')
        .send(webhookPayload);

      expect(response.status).toBe(401);
      expect(response.body.error).toContain('Invalid webhook signature');
    });

    test('should enforce webhook rate limiting', async () => {
      const webhookPayload = {
        entity: 'event',
        account_id: 'acc_test123',
        event: 'payment.captured',
        contains: ['payment'],
        payload: {},
        created_at: Math.floor(Date.now() / 1000)
      };

      const signature = generateWebhookSignature(JSON.stringify(webhookPayload));

      // Make many webhook requests
      const requests = Array(101).fill(null).map(() =>
        request(app)
          .post('/api/v1/wallet/webhook/razorpay')
          .set('X-Razorpay-Signature', signature)
          .send(webhookPayload)
      );

      const responses = await Promise.all(requests);
      
      // Should have rate limited responses
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Security Measures', () => {
    test('should block user after suspicious activity', async () => {
      // Simulate multiple failed payment verifications
      const failedVerifications = Array(4).fill(null).map(() =>
        request(app)
          .post('/api/v1/wallet/verify-payment')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            razorpay_order_id: 'invalid_order',
            razorpay_payment_id: 'invalid_payment',
            razorpay_signature: 'invalid_signature',
            transaction_id: 'invalid_transaction'
          })
      );

      await Promise.all(failedVerifications);

      // Next request should be blocked
      const response = await request(app)
        .post('/api/v1/wallet/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 100,
          currency: 'INR',
          payment_method: 'upi'
        });

      expect(response.status).toBe(403);
      expect(response.body.error).toContain('temporarily restricted');
    });

    test('should require authentication for all payment endpoints', async () => {
      const endpoints = [
        { method: 'post', path: '/api/v1/wallet/topup' },
        { method: 'post', path: '/api/v1/wallet/verify-payment' },
        { method: 'get', path: '/api/v1/wallet' },
        { method: 'get', path: '/api/v1/wallet/transactions' }
      ];

      for (const endpoint of endpoints) {
        const response = await request(app)[endpoint.method](endpoint.path);
        expect(response.status).toBe(401);
      }
    });
  });
});

// Helper functions for testing
function generateTestSignature(orderId: string, paymentId: string): string {
  const crypto = require('crypto');
  const secret = process.env.RAZORPAY_KEY_SECRET || 'test_secret';
  const body = `${orderId}|${paymentId}`;
  return crypto.createHmac('sha256', secret).update(body).digest('hex');
}

function generateWebhookSignature(body: string): string {
  const crypto = require('crypto');
  const secret = process.env.RAZORPAY_WEBHOOK_SECRET || 'test_webhook_secret';
  return crypto.createHmac('sha256', secret).update(body).digest('hex');
}
