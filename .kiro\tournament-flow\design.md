# Design Document

## Overview

The Tournament Match Flow system extends the existing match infrastructure to support tournament-style competitions with enhanced creation controls, participant management, and flexible winner assignment. The design leverages proven patterns from the current match system while introducing tournament-specific enhancements for multi-participant ranking and prize distribution.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend (Flutter)"
        A[Games Screen] --> B[Tournament Details]
        B --> C[Join Tournament]
        C --> D[Submit Results]
        D --> E[Verification Status]
    end
    
    subgraph "Backend API"
        F[Tournament Controller] --> G[Tournament Service]
        G --> H[Match Service Integration]
        G --> I[Wallet Service]
        G --> J[Result Verification]
    end
    
    subgraph "Admin Dashboard"
        K[Pending Tournament Results] --> L[Result Verification]
        L --> M[Winner Assignment]
        M --> N[Prize Distribution]
    end
    
    subgraph "Database"
        O[Tournaments Table]
        P[Tournament Participants]
        Q[Tournament Results]
        R[Wallet Transactions]
    end
    
    A --> F
    F --> O
    G --> P
    J --> Q
    I --> R
    K --> F
```

### Component Integration

The system integrates with existing components:

- **Match Service**: Reuses participant management, wallet integration, and result submission patterns
- **Authentication Middleware**: Extends existing role-based access control for tournament creation
- **File Upload Service**: Leverages existing screenshot upload and validation
- **Wallet Service**: Uses existing transaction processing for entry fees and prize distribution
- **Admin Dashboard**: Extends existing verification UI patterns for tournament results

## Components and Interfaces

### Backend Components

#### TournamentMatchController
```typescript
class TournamentMatchController {
  // Tournament lifecycle management
  async createTournament(req: AuthenticatedRequest, res: Response)
  async joinTournament(req: AuthenticatedRequest, res: Response)
  async startTournament(req: AuthenticatedRequest, res: Response)
  async submitTournamentResult(req: AuthenticatedRequest, res: Response)
  
  // Admin verification
  async getPendingTournamentResults(req: AuthenticatedRequest, res: Response)
  async verifyTournamentResult(req: AuthenticatedRequest, res: Response)
  async assignTournamentWinners(req: AuthenticatedRequest, res: Response)
  
  // User queries
  async getTournamentById(req: AuthenticatedRequest, res: Response)
  async getUserTournaments(req: AuthenticatedRequest, res: Response)
  async getTournamentVerificationStatus(req: AuthenticatedRequest, res: Response)
}
```

#### TournamentMatchService
```typescript
class TournamentMatchService {
  // Core tournament operations
  async createTournament(tournamentData: CreateTournamentData, creatorId: string): Promise<Tournament>
  async joinTournament(tournamentId: string, userId: string): Promise<TournamentParticipant>
  async submitResult(tournamentId: string, userId: string, resultData: TournamentResultData): Promise<TournamentResult>
  
  // Admin operations
  async assignWinners(tournamentId: string, winners: WinnerAssignment[], adminId: string): Promise<void>
  async distributePrizes(tournamentId: string, adminId: string): Promise<PrizeDistribution[]>
  
  // Integration with existing services
  private async processWalletTransaction(userId: string, amount: number, type: TransactionType): Promise<void>
  private async uploadScreenshot(file: Express.Multer.File, tournamentId: string, userId: string): Promise<string>
}
```

### Frontend Components

#### Tournament Screens
- **TournamentListWidget**: Displays tournaments in games screen (extends existing match list patterns)
- **TournamentDetailsScreen**: Shows tournament information and join options (mirrors match details)
- **JoinTournamentScreen**: Handles tournament joining with wallet validation (reuses match join logic)
- **TournamentResultSubmissionScreen**: Manages result and screenshot submission (extends match result submission)
- **TournamentVerificationStatusScreen**: Shows verification progress (mirrors match verification status)

#### Admin Dashboard Components
- **PendingTournamentResultsPage**: Lists tournaments awaiting verification (extends pending match results)
- **TournamentResultVerificationComponent**: Handles result review and winner assignment
- **TournamentWinnerAssignmentModal**: Manages different winner assignment modes (1v1, team, multi-player)

## Data Models

### Tournament Table Extensions
```sql
-- Extend existing tournaments table or create tournament_matches table
CREATE TABLE tournament_matches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_id UUID REFERENCES tournaments(id),
  game_id INTEGER REFERENCES games(id),
  created_by UUID REFERENCES users(id),
  status tournament_status DEFAULT 'scheduled',
  max_participants INTEGER NOT NULL,
  entry_fee DECIMAL(10,2) DEFAULT 0,
  prize_pool DECIMAL(10,2) DEFAULT 0,
  tournament_time TIMESTAMP WITH TIME ZONE,
  result_submission_deadline TIMESTAMP WITH TIME ZONE,
  room_id VARCHAR(255),
  room_password VARCHAR(255),
  winner_assignment_type winner_type DEFAULT 'single', -- single, team, multi_rank
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tournament participants (similar to match_participants)
CREATE TABLE tournament_match_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_match_id UUID REFERENCES tournament_matches(id),
  user_id UUID REFERENCES users(id),
  participant_type participant_type DEFAULT 'player',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status participant_status DEFAULT 'joined',
  final_position INTEGER,
  final_score INTEGER,
  prize_amount DECIMAL(10,2) DEFAULT 0
);

-- Tournament results (similar to match results)
CREATE TABLE tournament_match_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tournament_match_id UUID REFERENCES tournament_matches(id),
  user_id UUID REFERENCES users(id),
  result_position INTEGER NOT NULL,
  result_score INTEGER,
  result_kills INTEGER DEFAULT 0,
  screenshot_urls TEXT[],
  verification_status verification_status DEFAULT 'pending',
  admin_notes TEXT,
  verified_by UUID REFERENCES users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### TypeScript Interfaces
```typescript
interface CreateTournamentData {
  game_id: number;
  tournament_time: string;
  tournament_description: string;
  max_participants: number;
  entry_fee: number;
  prize_pool: number;
  room_id?: string;
  room_password?: string;
  result_submission_deadline: string;
  winner_assignment_type: 'single' | 'team' | 'multi_rank';
}

interface TournamentResultData {
  result_position: number;
  result_score?: number;
  result_kills?: number;
  screenshot_urls: string[];
}

interface WinnerAssignment {
  user_id: string;
  position: number;
  prize_amount: number;
}

interface PrizeDistribution {
  user_id: string;
  amount: number;
  transaction_id: string;
}
```

## Error Handling

### Tournament Creation Errors
- **Insufficient Permissions**: Return 403 with clear authorization message
- **Invalid Tournament Data**: Return 400 with field-specific validation errors
- **Wallet Integration Failures**: Return 500 with retry instructions

### Tournament Joining Errors
- **Insufficient Wallet Balance**: Return 400 with top-up redirect
- **Tournament Full**: Return 409 with alternative tournament suggestions
- **Already Joined**: Return 409 with current participation status

### Result Submission Errors
- **Invalid Screenshot**: Return 400 with format requirements
- **Deadline Exceeded**: Return 410 with deadline information
- **Duplicate Submission**: Return 409 with existing submission details

### Admin Verification Errors
- **Conflicting Winner Assignments**: Return 400 with conflict resolution options
- **Prize Distribution Failures**: Return 500 with manual intervention instructions

## Testing Strategy

### Unit Testing
- **Service Layer**: Test tournament creation, joining, result submission, and winner assignment logic
- **Controller Layer**: Test request validation, response formatting, and error handling
- **Integration Points**: Test wallet service integration, file upload service, and database transactions

### Integration Testing
- **End-to-End Tournament Flow**: Test complete tournament lifecycle from creation to prize distribution
- **Cross-Service Integration**: Test interaction with existing match services, wallet services, and admin dashboard
- **Database Consistency**: Test transaction atomicity and data integrity across related tables

### User Acceptance Testing
- **Tournament Creator Flow**: Verify admin/manager/spectator can create and manage tournaments
- **Participant Flow**: Verify users can discover, join, participate, and receive results
- **Admin Verification Flow**: Verify admin dashboard provides efficient result verification and winner assignment

### Performance Testing
- **Concurrent Tournament Joining**: Test system behavior with multiple users joining simultaneously
- **Large Tournament Management**: Test performance with tournaments having maximum participants
- **Prize Distribution Scalability**: Test wallet transaction processing for multiple winners

## Security Considerations

### Access Control
- **Tournament Creation**: Enforce role-based restrictions using existing authentication middleware
- **Result Submission**: Validate participant eligibility and prevent unauthorized submissions
- **Admin Operations**: Ensure only authorized admins can verify results and assign winners

### Data Validation
- **Input Sanitization**: Validate all tournament data, result submissions, and admin inputs
- **Screenshot Validation**: Reuse existing image validation and security scanning
- **Financial Transactions**: Implement atomic wallet operations with rollback capabilities

### Audit Trail
- **Tournament Actions**: Log all tournament creation, joining, and result submission activities
- **Admin Decisions**: Record all verification decisions and winner assignments with timestamps
- **Financial Transactions**: Maintain complete audit trail for all entry fees and prize distributions