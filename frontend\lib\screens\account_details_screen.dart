import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/widgets/shared/golden_app_bar.dart';
import 'package:wiggyz_app/widgets/shared/standard_form_components.dart';
import 'package:wiggyz_app/providers/user_provider.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';

class AccountDetailsScreen extends StatefulWidget {
  const AccountDetailsScreen({super.key});

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAccountDetails();
  }

  Future<void> _loadAccountDetails() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading account details
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: const GoldenAppBar(title: 'Account Details'),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFFFCC00),
              ),
            )
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildAccountInfoSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildVerificationSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildAccountStatsSection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildSecuritySection(isDarkMode),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildAccountInfoSection(bool isDarkMode) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, _) {
        final profile = userProvider.profile;
        
        return StandardCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.account_circle_outlined,
                    color: isDarkMode ? const Color(0xFFD4AF37) : Colors.blue[700],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Account Information',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? const Color(0xFFD4AF37) : Colors.blue[700],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildInfoRow('User ID', profile?.id ?? 'N/A', isDarkMode),
              _buildInfoRow('Username', profile?.username ?? userProvider.username, isDarkMode),
              _buildInfoRow('Email', profile?.email ?? userProvider.email, isDarkMode),
              _buildInfoRow('Phone', profile?.phone ?? 'Not provided', isDarkMode),
              _buildInfoRow('Account Type', profile?.role ?? 'Standard User', isDarkMode),
              _buildInfoRow('Registration Date', _formatDate(profile?.createdAt), isDarkMode),
              _buildInfoRow('Last Login', _formatDate(profile?.updatedAt), isDarkMode),
            ],
          ),
        );
      },
    );
  }

  Widget _buildVerificationSection(bool isDarkMode) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, _) {
        final profile = userProvider.profile;
        final isVerified = profile?.role == 'verified' || false;
        
        return StandardCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.verified_outlined,
                    color: isDarkMode ? const Color(0xFFD4AF37) : Colors.green[700],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Verification Status',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? const Color(0xFFD4AF37) : Colors.green[700],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: isVerified ? Colors.green : Colors.orange,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isVerified ? 'Account Verified' : 'Verification Pending',
                          style: GoogleFonts.poppins(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          isVerified 
                              ? 'Your account has been verified and you can participate in all tournaments.'
                              : 'Complete your profile and submit verification documents to unlock all features.',
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (!isVerified) ...[
                const SizedBox(height: 16),
                StandardPrimaryButton(
                  text: 'Start Verification',
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Verification process coming soon',
                          style: GoogleFonts.poppins(),
                        ),
                        backgroundColor: Colors.blue,
                      ),
                    );
                  },
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccountStatsSection(bool isDarkMode) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, _) {
        final profile = userProvider.profile;
        
        return StandardCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    color: isDarkMode ? const Color(0xFFD4AF37) : Colors.purple[700],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Account Statistics',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? const Color(0xFFD4AF37) : Colors.purple[700],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Level',
                      '${profile?.level ?? userProvider.level}',
                      Icons.trending_up,
                      isDarkMode,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'Experience',
                      '${profile?.experience ?? userProvider.experience}',
                      Icons.star,
                      isDarkMode,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Diamonds',
                      '${profile?.diamonds ?? userProvider.diamonds}',
                      Icons.diamond,
                      isDarkMode,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'Coins',
                      '${profile?.coins ?? userProvider.coins}',
                      Icons.monetization_on,
                      isDarkMode,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildInfoRow('Games Played', '${profile?.stats?['gamesPlayed'] ?? 0}', isDarkMode),
              _buildInfoRow('Tournaments Joined', '${profile?.stats?['tournamentsJoined'] ?? 0}', isDarkMode),
              _buildInfoRow('Total Wins', '${profile?.stats?['wins'] ?? 0}', isDarkMode),
              _buildInfoRow('Win Rate', '${_calculateWinRate(profile?.stats)}%', isDarkMode),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSecuritySection(bool isDarkMode) {
    return StandardCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security_outlined,
                color: isDarkMode ? const Color(0xFFD4AF37) : Colors.red[700],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Security Information',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? const Color(0xFFD4AF37) : Colors.red[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSecurityItem(
            'Two-Factor Authentication',
            'Disabled',
            Icons.security,
            Colors.orange,
            isDarkMode,
          ),
          _buildSecurityItem(
            'Login Notifications',
            'Enabled',
            Icons.notifications,
            Colors.green,
            isDarkMode,
          ),
          _buildSecurityItem(
            'Device Tracking',
            'Enabled',
            Icons.devices,
            Colors.green,
            isDarkMode,
          ),
          _buildSecurityItem(
            'Password Strength',
            'Strong',
            Icons.lock,
            Colors.green,
            isDarkMode,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityItem(
    String title,
    String status,
    IconData icon,
    Color statusColor,
    bool isDarkMode,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: statusColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  status,
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: statusColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'N/A';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'N/A';
    }
  }

  String _calculateWinRate(Map<String, dynamic>? stats) {
    if (stats == null) return '0';

    final gamesPlayed = stats['gamesPlayed'] as int? ?? 0;
    final wins = stats['wins'] as int? ?? 0;

    if (gamesPlayed == 0) return '0';

    final winRate = (wins / gamesPlayed * 100).round();
    return winRate.toString();
  }
}
