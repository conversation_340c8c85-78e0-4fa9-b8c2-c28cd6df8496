/**
 * Test file to verify the new withdrawal minimum fee structure
 * Tests the minimum ₹5 fee OR 2% (whichever is higher) logic
 */

void main() {
  print('🧪 Testing Withdrawal Minimum Fee Structure');
  print('=' * 50);
  
  testMinimumFeeLogic();
  testFeeTransitionPoint();
  testRealWorldScenarios();
  testFeeLabeling();
  
  print('\n✅ All withdrawal minimum fee tests passed!');
  print('The new fee structure is working correctly.');
}

void testMinimumFeeLogic() {
  print('\n💰 Testing Minimum Fee Logic');
  print('-' * 30);
  
  final testCases = [
    // Cases where minimum ₹5 fee applies (2% < ₹5)
    {'amount': 100.0, 'expectedFee': 5.0, 'expectedGST': 0.9, 'expectedNet': 94.1, 'feeType': 'minimum'},
    {'amount': 150.0, 'expectedFee': 5.0, 'expectedGST': 0.9, 'expectedNet': 144.1, 'feeType': 'minimum'},
    {'amount': 200.0, 'expectedFee': 5.0, 'expectedGST': 0.9, 'expectedNet': 194.1, 'feeType': 'minimum'},
    {'amount': 249.0, 'expectedFee': 5.0, 'expectedGST': 0.9, 'expectedNet': 243.1, 'feeType': 'minimum'},
    
    // Transition point (₹250 = 2% fee of ₹5, exactly at minimum)
    {'amount': 250.0, 'expectedFee': 5.0, 'expectedGST': 0.9, 'expectedNet': 244.1, 'feeType': 'minimum'},
    
    // Cases where 2% fee applies (2% > ₹5)
    {'amount': 300.0, 'expectedFee': 6.0, 'expectedGST': 1.08, 'expectedNet': 292.92, 'feeType': 'percentage'},
    {'amount': 500.0, 'expectedFee': 10.0, 'expectedGST': 1.8, 'expectedNet': 488.2, 'feeType': 'percentage'},
    {'amount': 1000.0, 'expectedFee': 20.0, 'expectedGST': 3.6, 'expectedNet': 976.4, 'feeType': 'percentage'},
  ];
  
  for (final testCase in testCases) {
    final amount = testCase['amount'] as double;
    final expectedFee = testCase['expectedFee'] as double;
    final expectedGST = testCase['expectedGST'] as double;
    final expectedNet = testCase['expectedNet'] as double;
    final feeType = testCase['feeType'] as String;
    
    final result = calculateWithdrawalFees(amount);
    
    print('Amount: ₹${amount.toStringAsFixed(0)}');
    print('  Expected Fee: ₹${expectedFee.toStringAsFixed(2)} ($feeType)');
    print('  Actual Fee: ₹${result.transactionFee.toStringAsFixed(2)}');
    print('  Expected GST: ₹${expectedGST.toStringAsFixed(2)}');
    print('  Actual GST: ₹${result.gstOnFee.toStringAsFixed(2)}');
    print('  Expected Net: ₹${expectedNet.toStringAsFixed(2)}');
    print('  Actual Net: ₹${result.finalAmount.toStringAsFixed(2)}');
    
    // Verify calculations
    assert((result.transactionFee - expectedFee).abs() < 0.01, 'Fee mismatch for ₹$amount');
    assert((result.gstOnFee - expectedGST).abs() < 0.01, 'GST mismatch for ₹$amount');
    assert((result.finalAmount - expectedNet).abs() < 0.01, 'Net amount mismatch for ₹$amount');
    
    print('  ✅ PASS\n');
  }
}

void testFeeTransitionPoint() {
  print('\n🎯 Testing Fee Transition Point (₹250)');
  print('-' * 40);
  
  // Test amounts around the transition point where 2% = ₹5
  final transitionTests = [
    249.0, 249.5, 249.9, 250.0, 250.1, 250.5, 251.0
  ];
  
  for (final amount in transitionTests) {
    final result = calculateWithdrawalFees(amount);
    final calculatedPercentage = amount * 0.02;
    final shouldUseMinimum = calculatedPercentage <= 5.0;
    final expectedFee = shouldUseMinimum ? 5.0 : calculatedPercentage;
    
    print('₹${amount.toStringAsFixed(1)}:');
    print('  2% = ₹${calculatedPercentage.toStringAsFixed(2)}');
    print('  Fee = ₹${result.transactionFee.toStringAsFixed(2)} (${shouldUseMinimum ? "Min ₹5" : "2%"})');
    
    assert((result.transactionFee - expectedFee).abs() < 0.01, 'Transition point error for ₹$amount');
    print('  ✅ PASS');
  }
}

void testRealWorldScenarios() {
  print('\n🌍 Testing Real-World Scenarios');
  print('-' * 35);
  
  // Common withdrawal amounts
  final scenarios = [
    {'name': 'Small withdrawal', 'amount': 100.0},
    {'name': 'Medium withdrawal', 'amount': 500.0},
    {'name': 'Large withdrawal', 'amount': 2000.0},
    {'name': 'Odd amount', 'amount': 347.50},
  ];
  
  for (final scenario in scenarios) {
    final name = scenario['name'] as String;
    final amount = scenario['amount'] as double;
    final result = calculateWithdrawalFees(amount);
    
    print('$name (₹${amount.toStringAsFixed(2)}):');
    print('  Transaction Fee: ₹${result.transactionFee.toStringAsFixed(2)}');
    print('  GST on Fee: ₹${result.gstOnFee.toStringAsFixed(2)}');
    print('  Total Deductions: ₹${result.totalDeductions.toStringAsFixed(2)}');
    print('  Amount to Bank: ₹${result.finalAmount.toStringAsFixed(2)}');
    print('  Fee Rate: ${(result.transactionFee / amount * 100).toStringAsFixed(2)}%');
    print('');
  }
  
  print('✅ Real-world scenarios calculated correctly');
}

void testFeeLabeling() {
  print('\n🏷️  Testing Fee Label Logic');
  print('-' * 25);
  
  final labelTests = [
    {'amount': 100.0, 'expectedLabel': 'Transaction Fee (Min ₹5)'},
    {'amount': 200.0, 'expectedLabel': 'Transaction Fee (Min ₹5)'},
    {'amount': 250.0, 'expectedLabel': 'Transaction Fee (Min ₹5)'},
    {'amount': 300.0, 'expectedLabel': 'Transaction Fee (2%)'},
    {'amount': 500.0, 'expectedLabel': 'Transaction Fee (2%)'},
  ];
  
  for (final test in labelTests) {
    final amount = test['amount'] as double;
    final expectedLabel = test['expectedLabel'] as String;
    final result = calculateWithdrawalFees(amount);
    final actualLabel = getTransactionFeeLabel(amount, result.transactionFee);
    
    print('₹${amount.toStringAsFixed(0)}: "$actualLabel"');
    assert(actualLabel == expectedLabel, 'Label mismatch for ₹$amount');
  }
  
  print('✅ Fee labeling is correct');
}

// Helper classes and functions
class WithdrawalResult {
  final double withdrawalAmount;
  final double transactionFee;
  final double gstOnFee;
  final double totalDeductions;
  final double finalAmount;
  
  WithdrawalResult(this.withdrawalAmount, this.transactionFee, this.gstOnFee,
                  this.totalDeductions, this.finalAmount);
  
  WithdrawalResult.empty() : this(0.0, 0.0, 0.0, 0.0, 0.0);
}

WithdrawalResult calculateWithdrawalFees(double withdrawalAmount) {
  if (withdrawalAmount <= 0) return WithdrawalResult.empty();
  
  // Minimum ₹5 transaction fee OR 2% of withdrawal amount (whichever is higher)
  final calculatedFee = withdrawalAmount * 0.02; // 2% transaction fee
  final transactionFee = calculatedFee > 5.0 ? calculatedFee : 5.0; // Minimum ₹5
  final gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
  final totalDeductions = transactionFee + gstOnFee;
  final finalAmount = withdrawalAmount - totalDeductions; // Amount user receives after fees
  
  return WithdrawalResult(withdrawalAmount, transactionFee, gstOnFee,
                         totalDeductions, finalAmount);
}

String getTransactionFeeLabel(double withdrawalAmount, double transactionFee) {
  if (withdrawalAmount == 0.0) return 'Transaction Fee';
  
  final calculatedFee = withdrawalAmount * 0.02;
  if (transactionFee <= calculatedFee + 0.01) { // Small tolerance for floating point comparison
    return 'Transaction Fee (2%)';
  } else {
    return 'Transaction Fee (Min ₹5)';
  }
}
