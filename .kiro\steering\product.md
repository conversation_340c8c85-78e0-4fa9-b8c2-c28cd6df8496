# WiggyZ Gaming Platform

WiggyZ is a comprehensive gaming tournament and match management platform that enables users to participate in competitive gaming tournaments, manage matches, and handle payments through an integrated wallet system.

## Core Features

- **Tournament Management**: Create, join, and manage gaming tournaments
- **Match System**: Real-time match creation, participation, and result submission
- **User Management**: Player profiles, authentication, and role-based access
- **Wallet & Payments**: Integrated payment system with Razorpay for entry fees and prize distribution
- **Admin Dashboard**: Complete administrative interface for tournament and match oversight
- **Real-time Features**: Live match updates and notifications
- **Mobile Support**: Cross-platform Flutter mobile application

## Target Users

- **Players**: Gamers participating in tournaments and matches
- **Administrators**: Platform managers overseeing tournaments and resolving disputes
- **Tournament Organizers**: Users creating and managing competitive events

## Business Model

The platform operates on entry fees for tournaments and matches, with prize pools distributed to winners. The system includes referral programs, daily rewards, and loyalty systems to encourage user engagement.