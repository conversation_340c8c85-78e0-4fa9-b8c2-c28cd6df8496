import { test, expect } from '@playwright/test';

// Test configuration
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123',
  name: 'Test User'
};

const ADMIN_USER = {
  email: '<EMAIL>',
  password: 'adminpassword123',
  name: 'Admin User'
};

test.describe('Support System End-to-End Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
  });

  test.describe('Help & Support Screen', () => {
    test('should navigate to help and support from profile', async ({ page }) => {
      // Login first
      await loginUser(page, TEST_USER);
      
      // Navigate to profile
      await page.click('[data-testid="profile-tab"]');
      await expect(page).toHaveURL('/profile');
      
      // Click Help & Support button
      await page.click('text=Help & Support');
      await expect(page).toHaveURL('/help-support');
      
      // Verify the screen loaded correctly
      await expect(page.locator('text=Help & Support')).toBeVisible();
      await expect(page.locator('text=FAQ')).toBeVisible();
      await expect(page.locator('text=Contact Us')).toBeVisible();
      await expect(page.locator('text=Chat')).toBeVisible();
    });

    test('should display FAQ items', async ({ page }) => {
      await loginUser(page, TEST_USER);
      await page.goto('/help-support');
      
      // Wait for FAQ items to load
      await page.waitForSelector('[data-testid="faq-item"]', { timeout: 10000 });
      
      // Check that FAQ items are displayed
      const faqItems = page.locator('[data-testid="faq-item"]');
      await expect(faqItems).toHaveCountGreaterThan(0);
      
      // Test expanding an FAQ item
      await faqItems.first().click();
      await expect(page.locator('text=Was this helpful?')).toBeVisible();
    });

    test('should submit FAQ feedback', async ({ page }) => {
      await loginUser(page, TEST_USER);
      await page.goto('/help-support');
      
      // Wait for FAQ items and expand first one
      await page.waitForSelector('[data-testid="faq-item"]');
      await page.locator('[data-testid="faq-item"]').first().click();
      
      // Click helpful button
      await page.click('[data-testid="helpful-yes"]');
      
      // Verify success message
      await expect(page.locator('text=Thank you for your feedback!')).toBeVisible();
    });
  });

  test.describe('Contact Form', () => {
    test('should submit contact form successfully', async ({ page }) => {
      await loginUser(page, TEST_USER);
      await page.goto('/help-support');
      
      // Switch to Contact Us tab
      await page.click('text=Contact Us');
      
      // Fill out the form
      await page.selectOption('[data-testid="category-select"]', 'general_inquiry');
      await page.fill('[data-testid="subject-input"]', 'Test Support Message');
      await page.fill('[data-testid="message-input"]', 'This is a test message for the support system. Please ignore this message as it is part of automated testing.');
      
      // Submit the form
      await page.click('[data-testid="submit-button"]');
      
      // Verify success message
      await expect(page.locator('text=Your message has been submitted successfully!')).toBeVisible();
      
      // Verify form is cleared
      await expect(page.locator('[data-testid="subject-input"]')).toHaveValue('');
      await expect(page.locator('[data-testid="message-input"]')).toHaveValue('');
    });

    test('should validate required fields', async ({ page }) => {
      await loginUser(page, TEST_USER);
      await page.goto('/help-support');
      
      // Switch to Contact Us tab
      await page.click('text=Contact Us');
      
      // Try to submit empty form
      await page.click('[data-testid="submit-button"]');
      
      // Verify validation message
      await expect(page.locator('text=Please fill in all required fields')).toBeVisible();
    });

    test('should enforce rate limiting', async ({ page }) => {
      await loginUser(page, TEST_USER);
      await page.goto('/help-support');
      
      // Switch to Contact Us tab
      await page.click('text=Contact Us');
      
      // Submit multiple messages quickly
      for (let i = 0; i < 4; i++) {
        await page.selectOption('[data-testid="category-select"]', 'general_inquiry');
        await page.fill('[data-testid="subject-input"]', `Test Message ${i + 1}`);
        await page.fill('[data-testid="message-input"]', `This is test message number ${i + 1}`);
        await page.click('[data-testid="submit-button"]');
        
        if (i < 3) {
          // First 3 should succeed
          await expect(page.locator('text=Your message has been submitted successfully!')).toBeVisible();
        } else {
          // 4th should be rate limited
          await expect(page.locator('text=Too many')).toBeVisible();
        }
        
        // Wait a bit between submissions
        await page.waitForTimeout(1000);
      }
    });
  });

  test.describe('Chat System', () => {
    test('should start a chat session', async ({ page }) => {
      await loginUser(page, TEST_USER);
      await page.goto('/help-support');
      
      // Switch to Chat tab
      await page.click('text=Chat');
      
      // Start chat
      await page.click('[data-testid="start-chat-button"]');
      
      // Verify navigation to chat screen
      await expect(page).toHaveURL('/chat');
      await expect(page.locator('text=Support Chat')).toBeVisible();
    });

    test('should send and receive messages', async ({ page }) => {
      await loginUser(page, TEST_USER);
      await page.goto('/chat');
      
      // Wait for chat to initialize
      await page.waitForSelector('[data-testid="message-input"]');
      
      // Send a message
      const testMessage = 'Hello, I need help with my account';
      await page.fill('[data-testid="message-input"]', testMessage);
      await page.click('[data-testid="send-button"]');
      
      // Verify message appears in chat
      await expect(page.locator(`text=${testMessage}`)).toBeVisible();
      
      // Verify input is cleared
      await expect(page.locator('[data-testid="message-input"]')).toHaveValue('');
    });

    test('should close chat session', async ({ page }) => {
      await loginUser(page, TEST_USER);
      await page.goto('/chat');
      
      // Wait for chat to initialize
      await page.waitForSelector('[data-testid="close-chat-button"]');
      
      // Close chat
      await page.click('[data-testid="close-chat-button"]');
      
      // Verify navigation back
      await expect(page).toHaveURL('/help-support');
    });
  });

  test.describe('Admin Dashboard Integration', () => {
    test('should display support messages in admin dashboard', async ({ page }) => {
      // First, create a support message as a user
      await loginUser(page, TEST_USER);
      await page.goto('/help-support');
      await page.click('text=Contact Us');
      
      await page.selectOption('[data-testid="category-select"]', 'bug_report');
      await page.fill('[data-testid="subject-input"]', 'Admin Test Message');
      await page.fill('[data-testid="message-input"]', 'This message should appear in the admin dashboard');
      await page.click('[data-testid="submit-button"]');
      
      await expect(page.locator('text=Your message has been submitted successfully!')).toBeVisible();
      
      // Logout and login as admin
      await logoutUser(page);
      await loginUser(page, ADMIN_USER);
      
      // Navigate to admin dashboard
      await page.goto('/admin/dashboard/support/messages');
      
      // Verify the message appears
      await expect(page.locator('text=Admin Test Message')).toBeVisible();
      await expect(page.locator('text=Bug Report')).toBeVisible();
    });

    test('should allow admin to reply to support messages', async ({ page }) => {
      await loginUser(page, ADMIN_USER);
      await page.goto('/admin/dashboard/support/messages');
      
      // Wait for messages to load
      await page.waitForSelector('[data-testid="message-card"]');
      
      // Click on first message to view details
      await page.click('[data-testid="view-message-button"]');
      
      // Fill reply form
      const replyText = 'Thank you for your message. We are looking into this issue.';
      await page.fill('[data-testid="reply-textarea"]', replyText);
      
      // Send reply
      await page.click('[data-testid="send-reply-button"]');
      
      // Verify reply appears
      await expect(page.locator(`text=${replyText}`)).toBeVisible();
    });

    test('should update message status', async ({ page }) => {
      await loginUser(page, ADMIN_USER);
      await page.goto('/admin/dashboard/support/messages');
      
      // Wait for messages to load
      await page.waitForSelector('[data-testid="message-card"]');
      
      // Click on first message
      await page.click('[data-testid="view-message-button"]');
      
      // Change status to resolved
      await page.selectOption('[data-testid="status-select"]', 'resolved');
      
      // Verify status change
      await expect(page.locator('text=resolved')).toBeVisible();
    });
  });

  test.describe('Data Persistence', () => {
    test('should persist support messages across sessions', async ({ page }) => {
      const uniqueSubject = `Persistence Test ${Date.now()}`;
      
      // Create a message
      await loginUser(page, TEST_USER);
      await page.goto('/help-support');
      await page.click('text=Contact Us');
      
      await page.selectOption('[data-testid="category-select"]', 'technical_support');
      await page.fill('[data-testid="subject-input"]', uniqueSubject);
      await page.fill('[data-testid="message-input"]', 'Testing data persistence');
      await page.click('[data-testid="submit-button"]');
      
      await expect(page.locator('text=Your message has been submitted successfully!')).toBeVisible();
      
      // Logout and login again
      await logoutUser(page);
      await loginUser(page, TEST_USER);
      
      // Check if message persists in admin dashboard
      await logoutUser(page);
      await loginUser(page, ADMIN_USER);
      await page.goto('/admin/dashboard/support/messages');
      
      // Search for the message
      await page.fill('[data-testid="search-input"]', uniqueSubject);
      await page.waitForTimeout(1000); // Wait for search to filter
      
      await expect(page.locator(`text=${uniqueSubject}`)).toBeVisible();
    });

    test('should persist chat messages', async ({ page }) => {
      const uniqueMessage = `Chat persistence test ${Date.now()}`;
      
      // Start chat and send message
      await loginUser(page, TEST_USER);
      await page.goto('/chat');
      
      await page.waitForSelector('[data-testid="message-input"]');
      await page.fill('[data-testid="message-input"]', uniqueMessage);
      await page.click('[data-testid="send-button"]');
      
      await expect(page.locator(`text=${uniqueMessage}`)).toBeVisible();
      
      // Refresh page and verify message persists
      await page.reload();
      await page.waitForSelector('[data-testid="message-input"]');
      
      await expect(page.locator(`text=${uniqueMessage}`)).toBeVisible();
    });
  });
});

// Helper functions
async function loginUser(page: any, user: typeof TEST_USER) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', user.email);
  await page.fill('[data-testid="password-input"]', user.password);
  await page.click('[data-testid="login-button"]');
  
  // Wait for successful login
  await expect(page).toHaveURL('/home');
}

async function logoutUser(page: any) {
  await page.goto('/profile');
  await page.click('[data-testid="logout-button"]');
  await expect(page).toHaveURL('/login');
}
