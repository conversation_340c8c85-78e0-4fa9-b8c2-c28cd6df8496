/**
 * Integration Tests for Match Deadline Automation System
 * 
 * Tests the complete flow from deadline expiry to automated result finalization
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { supabase } from '../../../config/supabase';
import { matchDeadlineService } from '../services/matchDeadlineService';
import { matchNotificationService } from '../services/matchNotificationService';

// Test data setup
const testUsers = [
  { id: 'user-1', email: '<EMAIL>', name: 'User One' },
  { id: 'user-2', email: '<EMAIL>', name: 'User Two' },
  { id: 'user-3', email: '<EMAIL>', name: 'User Three' }
];

const testGame = {
  id: 1,
  name: 'Test Game',
  image: 'test-game.png'
};

describe('Deadline Automation Integration Tests', () => {
  let testMatchId: string;
  let testParticipantIds: string[] = [];

  beforeAll(async () => {
    // Setup test data in database
    await setupTestData();
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
  });

  beforeEach(async () => {
    // Create a fresh test match for each test
    testMatchId = await createTestMatch();
    testParticipantIds = await addTestParticipants(testMatchId);
  });

  afterEach(async () => {
    // Clean up test match
    if (testMatchId) {
      await cleanupTestMatch(testMatchId);
    }
  });

  describe('No Submissions Scenario', () => {
    it('should automatically finalize match when no participants submit results', async () => {
      // Set deadline to past time
      await setMatchDeadline(testMatchId, new Date(Date.now() - 5 * 60 * 1000)); // 5 minutes ago

      // Trigger deadline processing
      const result = await matchDeadlineService.manualProcessExpiredMatches();

      expect(result.processed).toBe(1);
      expect(result.errors).toBe(0);

      // Verify match was completed
      const { data: match } = await supabase
        .from('matches')
        .select('status, auto_finalized, auto_finalized_reason')
        .eq('id', testMatchId)
        .single();

      expect(match.status).toBe('completed');
      expect(match.auto_finalized).toBe(true);
      expect(match.auto_finalized_reason).toContain('0/3 submitted');

      // Verify all participants marked as losers
      const { data: participants } = await supabase
        .from('match_participants')
        .select('is_winner, auto_assigned_result, auto_assignment_reason')
        .eq('match_id', testMatchId)
        .eq('participant_type', 'player');

      expect(participants).toHaveLength(3);
      participants?.forEach(p => {
        expect(p.is_winner).toBe(false);
        expect(p.auto_assigned_result).toBe(true);
        expect(p.auto_assignment_reason).toBe('No result submitted by deadline');
      });

      // Verify audit log entry
      const { data: auditLogs } = await supabase
        .from('match_audit_log')
        .select('*')
        .eq('match_id', testMatchId);

      expect(auditLogs).toHaveLength(1);
      expect(auditLogs?.[0].action_type).toBe('auto_finalized');
      expect(auditLogs?.[0].participants_submitted).toBe(0);
      expect(auditLogs?.[0].participants_defaulted).toBe(3);
    });
  });

  describe('Partial Submissions Scenario', () => {
    it('should handle partial submissions correctly', async () => {
      // Submit results for first two participants
      await submitTestResult(testMatchId, testParticipantIds[0], { position: 1, score: 100 });
      await submitTestResult(testMatchId, testParticipantIds[1], { position: 2, score: 80 });
      // Third participant doesn't submit

      // Set deadline to past time
      await setMatchDeadline(testMatchId, new Date(Date.now() - 5 * 60 * 1000));

      // Trigger deadline processing
      const result = await matchDeadlineService.manualProcessExpiredMatches();

      expect(result.processed).toBe(1);
      expect(result.errors).toBe(0);

      // Verify match was completed
      const { data: match } = await supabase
        .from('matches')
        .select('status, auto_finalized')
        .eq('id', testMatchId)
        .single();

      expect(match.status).toBe('completed');
      expect(match.auto_finalized).toBe(true);

      // Verify winner determination (position 1 should win)
      const { data: participants } = await supabase
        .from('match_participants')
        .select('user_id, is_winner, auto_assigned_result')
        .eq('match_id', testMatchId)
        .eq('participant_type', 'player');

      const winner = participants?.find(p => p.user_id === testParticipantIds[0]);
      const secondPlace = participants?.find(p => p.user_id === testParticipantIds[1]);
      const nonSubmitter = participants?.find(p => p.user_id === testParticipantIds[2]);

      expect(winner?.is_winner).toBe(true);
      expect(secondPlace?.is_winner).toBe(false);
      expect(nonSubmitter?.is_winner).toBe(false);
      expect(nonSubmitter?.auto_assigned_result).toBe(true);

      // Verify audit log
      const { data: auditLogs } = await supabase
        .from('match_audit_log')
        .select('*')
        .eq('match_id', testMatchId);

      expect(auditLogs).toHaveLength(1);
      expect(auditLogs?.[0].participants_submitted).toBe(2);
      expect(auditLogs?.[0].participants_defaulted).toBe(1);
    });
  });

  describe('All Submissions Scenario', () => {
    it('should handle case where all participants submitted', async () => {
      // Submit results for all participants
      await submitTestResult(testMatchId, testParticipantIds[0], { position: 2, score: 80 });
      await submitTestResult(testMatchId, testParticipantIds[1], { position: 1, score: 100 });
      await submitTestResult(testMatchId, testParticipantIds[2], { position: 3, score: 60 });

      // Set deadline to past time
      await setMatchDeadline(testMatchId, new Date(Date.now() - 5 * 60 * 1000));

      // Trigger deadline processing
      const result = await matchDeadlineService.manualProcessExpiredMatches();

      expect(result.processed).toBe(1);
      expect(result.errors).toBe(0);

      // Verify audit log shows all submitted scenario
      const { data: auditLogs } = await supabase
        .from('match_audit_log')
        .select('*')
        .eq('match_id', testMatchId);

      expect(auditLogs).toHaveLength(1);
      expect(auditLogs?.[0].action_type).toBe('match_auto_completed');
      expect(auditLogs?.[0].participants_submitted).toBe(3);
      expect(auditLogs?.[0].participants_defaulted).toBe(0);
    });
  });

  describe('Deadline Status Tracking', () => {
    it('should update deadline status correctly', async () => {
      const futureDeadline = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now
      await setMatchDeadline(testMatchId, futureDeadline);

      // Check deadline status was created
      const { data: deadlineStatus } = await supabase
        .from('match_deadline_status')
        .select('*')
        .eq('match_id', testMatchId)
        .single();

      expect(deadlineStatus).toBeTruthy();
      expect(deadlineStatus.total_participants).toBe(3);
      expect(deadlineStatus.participants_submitted).toBe(0);
      expect(deadlineStatus.deadline_expired).toBe(false);

      // Submit one result
      await submitTestResult(testMatchId, testParticipantIds[0], { position: 1, score: 100 });

      // Check status updated
      const { data: updatedStatus } = await supabase
        .from('match_deadline_status')
        .select('*')
        .eq('match_id', testMatchId)
        .single();

      expect(updatedStatus.participants_submitted).toBe(1);
      expect(updatedStatus.participants_remaining).toBe(2);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Create invalid match scenario
      const invalidMatchId = 'invalid-match-id';
      
      const expiredMatch = {
        match_id: invalidMatchId,
        deadline: new Date().toISOString(),
        participants_submitted: 0,
        total_participants: 2,
        match_status: 'live'
      };

      // Should not throw, but should log error
      await expect(matchDeadlineService['finalizeExpiredMatch'](expiredMatch))
        .rejects.toThrow();

      // Verify error was logged to deadline status
      const { data: deadlineStatus } = await supabase
        .from('match_deadline_status')
        .select('auto_finalization_error')
        .eq('match_id', invalidMatchId)
        .single();

      // Should either not exist or have error logged
      expect(deadlineStatus?.auto_finalization_error || 'error occurred').toContain('error');
    });
  });

  describe('Concurrent Processing', () => {
    it('should handle concurrent processing attempts safely', async () => {
      // Set deadline to past time
      await setMatchDeadline(testMatchId, new Date(Date.now() - 5 * 60 * 1000));

      // Trigger multiple concurrent processing attempts
      const promises = [
        matchDeadlineService.manualProcessExpiredMatches(),
        matchDeadlineService.manualProcessExpiredMatches(),
        matchDeadlineService.manualProcessExpiredMatches()
      ];

      const results = await Promise.all(promises);

      // Only one should have processed the match
      const totalProcessed = results.reduce((sum, result) => sum + result.processed, 0);
      expect(totalProcessed).toBe(1);

      // Verify match was only finalized once
      const { data: auditLogs } = await supabase
        .from('match_audit_log')
        .select('*')
        .eq('match_id', testMatchId);

      expect(auditLogs).toHaveLength(1);
    });
  });

  // Helper functions
  async function setupTestData() {
    // Insert test users if they don't exist
    for (const user of testUsers) {
      await supabase
        .from('users')
        .upsert(user, { onConflict: 'id' });
    }
  }

  async function cleanupTestData() {
    // Clean up test users
    await supabase
      .from('users')
      .delete()
      .in('id', testUsers.map(u => u.id));
  }

  async function createTestMatch(): Promise<string> {
    const { data: match, error } = await supabase
      .from('matches')
      .insert({
        game_id: testGame.id,
        title: 'Test Match',
        match_description: 'Integration test match',
        game_format: 'solo',
        match_mode: 'casual',
        max_participants: 3,
        status: 'live',
        match_time: new Date().toISOString(),
        result_submission_deadline: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
        created_by: testUsers[0].id
      })
      .select('id')
      .single();

    if (error) throw error;
    return match.id;
  }

  async function addTestParticipants(matchId: string): Promise<string[]> {
    const participants = testUsers.map(user => ({
      match_id: matchId,
      user_id: user.id,
      participant_type: 'player',
      joined_at: new Date().toISOString()
    }));

    const { data, error } = await supabase
      .from('match_participants')
      .insert(participants)
      .select('user_id');

    if (error) throw error;
    return data.map(p => p.user_id);
  }

  async function submitTestResult(matchId: string, userId: string, result: { position: number; score: number }) {
    await supabase
      .from('match_participants')
      .update({
        result_position: result.position,
        result_score: result.score,
        result_submitted_at: new Date().toISOString(),
        result_screenshot_url: 'test-screenshot.jpg'
      })
      .eq('match_id', matchId)
      .eq('user_id', userId);
  }

  async function setMatchDeadline(matchId: string, deadline: Date) {
    await supabase
      .from('matches')
      .update({
        result_submission_deadline: deadline.toISOString()
      })
      .eq('id', matchId);

    // Update deadline status
    await supabase
      .from('match_deadline_status')
      .upsert({
        match_id: matchId,
        result_submission_deadline: deadline.toISOString(),
        deadline_expired: deadline.getTime() <= Date.now(),
        total_participants: 3
      });
  }

  async function cleanupTestMatch(matchId: string) {
    // Delete in correct order due to foreign key constraints
    await supabase.from('match_audit_log').delete().eq('match_id', matchId);
    await supabase.from('match_deadline_status').delete().eq('match_id', matchId);
    await supabase.from('match_participants').delete().eq('match_id', matchId);
    await supabase.from('matches').delete().eq('id', matchId);
  }
});
