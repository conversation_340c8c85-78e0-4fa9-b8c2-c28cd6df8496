{"timestamp": "2025-07-31T07:27:51.374Z", "results": [{"timestamp": "2025-07-31T07:27:51.350Z", "message": "🔍 Running pre-deployment validation...", "type": "info", "category": "validation"}, {"timestamp": "2025-07-31T07:27:51.355Z", "message": "Validating environment variables...", "type": "info", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.356Z", "message": "Missing required environment variable: SUPABASE_URL", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.356Z", "message": "Missing required environment variable: SUPABASE_KEY", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.356Z", "message": "Missing required environment variable: JWT_REFRESH_SECRET", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.357Z", "message": "Missing required environment variable: RAZORPAY_KEY_ID", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.357Z", "message": "Missing required environment variable: RAZOR<PERSON>Y_KEY_SECRET", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.357Z", "message": "Missing optional environment variable: JWT_ACCESS_EXPIRY (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.359Z", "message": "Missing optional environment variable: JWT_REFRESH_EXPIRY (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.360Z", "message": "Missing optional environment variable: PAYMENT_TIMEOUT_MINUTES (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.360Z", "message": "Missing optional environment variable: DISABLE_FRAUD_DETECTION (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.361Z", "message": "❌ Missing 5 required environment variables", "type": "error", "category": "env"}, {"timestamp": "2025-07-31T07:27:51.361Z", "message": "Validating Supabase connection...", "type": "info", "category": "database"}, {"timestamp": "2025-07-31T07:27:51.362Z", "message": "Supabase credentials not found", "type": "error", "category": "database"}, {"timestamp": "2025-07-31T07:27:51.363Z", "message": "Validating Razorpay credentials...", "type": "info", "category": "payment"}, {"timestamp": "2025-07-31T07:27:51.363Z", "message": "<PERSON><PERSON><PERSON><PERSON> credentials not found", "type": "error", "category": "payment"}, {"timestamp": "2025-07-31T07:27:51.363Z", "message": "Validating build output...", "type": "info", "category": "build"}, {"timestamp": "2025-07-31T07:27:51.369Z", "message": "Build output is older than 1 hour, consider rebuilding", "type": "warn", "category": "build"}, {"timestamp": "2025-07-31T07:27:51.370Z", "message": "Build output validation passed", "type": "success", "category": "build"}, {"timestamp": "2025-07-31T07:27:51.372Z", "message": "❌ Some pre-deployment validations failed", "type": "error", "category": "validation"}]}