# Daily Reward Error Fix Documentation

## Problem Description

Users were experiencing a `RewardError` with `unknownError` type when attempting to claim daily rewards. The error had a null `statusCode` and provided minimal debugging information:

```
RewardError (claimDailyReward): RewardError(type: RewardErrorType.unknownError, message: Unexpected error occurred, statusCode: null)
```

## Root Cause Analysis

The issue was caused by inadequate error handling in the `claimDailyReward` method in `RewardService`. Specifically:

1. **JSON Parsing Failures**: When the server returned invalid JSON or empty responses, the `json.decode()` call would throw an exception
2. **Network Errors**: Network connectivity issues weren't properly categorized
3. **Generic Exception Handling**: All exceptions were caught and converted to generic `unknownError` types
4. **Missing Response Validation**: No validation of response format before JSON parsing
5. **Insufficient Logging**: Limited debugging information made it difficult to identify the actual cause

## Solution Implemented

### 1. Enhanced Error Handling in `claimDailyReward`

- **Response Validation**: Check for empty responses and invalid content types before JSON parsing
- **Detailed Logging**: Added comprehensive logging throughout the claim process
- **Specific Error Types**: Map HTTP status codes and exceptions to appropriate error types
- **Context Preservation**: Include response details and request context in error messages

### 2. Improved `RewardError` Class

- **Enhanced Exception Detection**: Better categorization of network, JSON, and HTTP errors
- **Response Context**: New helper methods to create errors with HTTP response context
- **Detailed Error Information**: Include endpoint, status code, and response body in error details

### 3. New Helper Methods

Added to `RewardErrorHandler`:
- `createHttpError()`: Create errors with HTTP response context
- `createNetworkError()`: Create errors with network exception context
- `_getErrorTypeFromStatusCode()`: Map status codes to error types
- `_extractMessageFromResponse()`: Extract error messages from API responses

## Key Improvements

### Before
```dart
catch (e) {
  final error = RewardErrorHandler.handleError(e, context: 'claimDailyReward');
  return DailyRewardClaimResult(success: false, error: error.type.userFriendlyMessage);
}
```

### After
```dart
// Validate response before JSON parsing
if (response.body.isEmpty) {
  return DailyRewardClaimResult(success: false, error: 'Server returned empty response');
}

// Check content type
final contentType = response.headers['content-type'] ?? '';
if (!contentType.contains('application/json')) {
  return DailyRewardClaimResult(success: false, error: 'Server returned invalid response format');
}

// Parse JSON with error handling
try {
  data = json.decode(response.body) as Map<String, dynamic>;
} catch (jsonError) {
  final error = RewardError(
    type: RewardErrorType.serverError,
    message: 'Invalid server response format',
    details: 'JSON parsing failed: $jsonError\nResponse: ${response.body}',
    statusCode: response.statusCode,
  );
  return DailyRewardClaimResult(success: false, error: error.type.userFriendlyMessage);
}
```

## Error Types Now Properly Detected

1. **Network Errors**: SocketException, TimeoutException, connection issues
2. **Authentication Errors**: 401, 403 status codes
3. **Validation Errors**: 400 status codes, malformed requests
4. **Server Errors**: 500+ status codes, JSON parsing failures
5. **Rate Limiting**: 429 status codes
6. **Not Found**: 404 status codes

## Debugging Information

The enhanced logging now provides:
- Request URL and headers (without sensitive data)
- Response status code and headers
- Response body content
- JSON parsing success/failure
- Exception details with stack traces
- Error categorization reasoning

## Testing

Created comprehensive tests in `test/daily_reward_error_test.dart` covering:
- Empty response handling
- Invalid JSON response handling
- Network exception handling
- HTTP status code mapping
- Error message extraction
- User-friendly message generation

## Usage Examples

### Creating Specific Errors

```dart
// HTTP error with context
final error = RewardErrorHandler.createHttpError(
  statusCode: 400,
  responseBody: '{"message": "Already claimed today"}',
  endpoint: '/rewards/claim-daily',
);

// Network error with context
final networkError = RewardErrorHandler.createNetworkError(
  exception: 'SocketException: Connection refused',
  endpoint: '/rewards/claim-daily',
);
```

### Enhanced Exception Detection

```dart
// Automatically detects error type from exception
final error = RewardError.fromException('SocketException: Connection refused');
// Results in: RewardErrorType.networkError

final jsonError = RewardError.fromException('FormatException: Unexpected character');
// Results in: RewardErrorType.serverError
```

## Benefits

1. **Better User Experience**: More specific and actionable error messages
2. **Improved Debugging**: Detailed logging helps identify issues quickly
3. **Proper Error Categorization**: Errors are now correctly typed instead of generic "unknown"
4. **Enhanced Reliability**: Better handling of edge cases and network issues
5. **Maintainability**: Clear error handling patterns for future development

## Future Considerations

1. **Retry Logic**: Consider implementing automatic retries for network errors
2. **Offline Support**: Handle offline scenarios gracefully
3. **Error Analytics**: Track error patterns to identify systemic issues
4. **User Guidance**: Provide more specific guidance based on error type
