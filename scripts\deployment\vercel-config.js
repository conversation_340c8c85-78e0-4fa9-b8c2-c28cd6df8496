#!/usr/bin/env node

/**
 * Vercel Configuration Generator for WiggyZ Backend
 * Dynamically generates vercel.json configuration based on environment and deployment settings
 */

const fs = require('fs');
const path = require('path');

class VercelConfigGenerator {
  constructor() {
    this.rootDir = path.resolve(__dirname, '../..');
    this.backendDir = path.join(this.rootDir, 'backend');
    this.configPath = path.join(this.rootDir, 'config', 'environments.json');
  }

  log(message, type = 'info') {
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} ${message}`);
  }

  loadEnvironments() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      this.log(`Failed to load environment configuration: ${error.message}`, 'error');
      return null;
    }
  }

  generateBaseConfig() {
    return {
      version: 2,
      name: "wiggyz-backend",
      builds: [
        {
          src: "dist/vercel.js",
          use: "@vercel/node",
          config: {
            maxLambdaSize: "50mb",
            maxDuration: 30
          }
        }
      ],
      routes: [
        {
          src: "/health",
          dest: "/dist/vercel.js"
        },
        {
          src: "/api/v1/(.*)",
          dest: "/dist/vercel.js"
        },
        {
          src: "/(.*)",
          dest: "/dist/vercel.js"
        }
      ]
    };
  }

  generateEnvironmentVariables(environment = 'production') {
    const baseEnvVars = {
      NODE_ENV: environment === 'production' ? 'production' : 'development'
    };

    // Add environment-specific variables
    if (environment === 'production') {
      baseEnvVars.USE_REDIS = 'false'; // Disable Redis in serverless
      baseEnvVars.DISABLE_BACKGROUND_SERVICES = 'true';
    }

    return baseEnvVars;
  }

  generateRegionConfig(environment = 'production') {
    // Default to Mumbai region for production (bom1)
    // Use Washington DC for staging (iad1)
    const regionMap = {
      production: ["bom1"], // Mumbai
      staging: ["iad1"],    // Washington DC
      development: ["iad1"] // Washington DC
    };

    return regionMap[environment] || ["bom1"];
  }

  generateConfig(options = {}) {
    const {
      environment = 'production',
      customName = null,
      customRegions = null,
      additionalEnvVars = {},
      enableFunctions = false
    } = options;

    this.log(`Generating Vercel configuration for ${environment} environment...`);

    const config = this.generateBaseConfig();

    // Set custom name if provided
    if (customName) {
      config.name = customName;
    }

    // Set environment variables
    config.env = {
      ...this.generateEnvironmentVariables(environment),
      ...additionalEnvVars
    };

    // Set regions
    config.regions = customRegions || this.generateRegionConfig(environment);

    // Add functions configuration if enabled
    if (enableFunctions) {
      config.functions = {
        "dist/vercel.js": {
          memory: 1024,
          maxDuration: 30
        }
      };
    }

    // Add headers for CORS and security
    config.headers = [
      {
        source: "/api/v1/(.*)",
        headers: [
          {
            key: "Access-Control-Allow-Origin",
            value: environment === 'production' 
              ? "https://app.wiggyz.com,https://admin.wiggyz.com"
              : "*"
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET,POST,PUT,DELETE,OPTIONS"
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "Content-Type,Authorization,X-Idempotency-Key"
          }
        ]
      }
    ];

    return config;
  }

  saveConfig(config, outputPath = null) {
    const targetPath = outputPath || path.join(this.backendDir, 'vercel.json');
    
    try {
      fs.writeFileSync(targetPath, JSON.stringify(config, null, 2));
      this.log(`Vercel configuration saved to: ${targetPath}`, 'success');
      return true;
    } catch (error) {
      this.log(`Failed to save configuration: ${error.message}`, 'error');
      return false;
    }
  }

  generateAndSave(options = {}) {
    const config = this.generateConfig(options);
    const success = this.saveConfig(config, options.outputPath);
    
    if (success) {
      this.log('Vercel configuration generated successfully!', 'success');
      this.log(`Environment: ${options.environment || 'production'}`, 'info');
      this.log(`Regions: ${config.regions.join(', ')}`, 'info');
      this.log(`Environment Variables: ${Object.keys(config.env).length} variables`, 'info');
    }
    
    return success;
  }

  validateConfig(configPath = null) {
    const targetPath = configPath || path.join(this.backendDir, 'vercel.json');
    
    try {
      const configData = fs.readFileSync(targetPath, 'utf8');
      const config = JSON.parse(configData);
      
      // Basic validation
      const requiredFields = ['version', 'builds', 'routes'];
      const missingFields = requiredFields.filter(field => !config[field]);
      
      if (missingFields.length > 0) {
        this.log(`Missing required fields: ${missingFields.join(', ')}`, 'error');
        return false;
      }
      
      // Validate builds
      if (!Array.isArray(config.builds) || config.builds.length === 0) {
        this.log('No builds configuration found', 'error');
        return false;
      }
      
      // Validate routes
      if (!Array.isArray(config.routes) || config.routes.length === 0) {
        this.log('No routes configuration found', 'error');
        return false;
      }
      
      this.log('Vercel configuration validation passed', 'success');
      return true;
      
    } catch (error) {
      this.log(`Configuration validation failed: ${error.message}`, 'error');
      return false;
    }
  }

  listTemplates() {
    this.log('Available configuration templates:', 'info');
    console.log('  production  - Production deployment with Mumbai region');
    console.log('  staging     - Staging deployment with Washington DC region');
    console.log('  development - Development-like deployment');
    console.log('  custom      - Custom configuration with additional options');
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const generator = new VercelConfigGenerator();
  
  if (args.length === 0) {
    console.log('🔧 WiggyZ Vercel Configuration Generator');
    console.log('Usage:');
    console.log('  node vercel-config.js generate [environment] [options]');
    console.log('  node vercel-config.js validate [config-path]');
    console.log('  node vercel-config.js templates');
    console.log('');
    console.log('Examples:');
    console.log('  node vercel-config.js generate production');
    console.log('  node vercel-config.js generate staging --name wiggyz-staging');
    console.log('  node vercel-config.js validate');
    process.exit(0);
  }
  
  const command = args[0];
  
  switch (command) {
    case 'generate':
      const environment = args[1] || 'production';
      const options = { environment };
      
      // Parse additional options
      for (let i = 2; i < args.length; i += 2) {
        const key = args[i];
        const value = args[i + 1];
        
        if (key === '--name' && value) {
          options.customName = value;
        } else if (key === '--regions' && value) {
          options.customRegions = value.split(',');
        } else if (key === '--output' && value) {
          options.outputPath = value;
        } else if (key === '--functions') {
          options.enableFunctions = true;
        }
      }
      
      const success = generator.generateAndSave(options);
      process.exit(success ? 0 : 1);
      break;
      
    case 'validate':
      const configPath = args[1];
      const isValid = generator.validateConfig(configPath);
      process.exit(isValid ? 0 : 1);
      break;
      
    case 'templates':
      generator.listTemplates();
      break;
      
    default:
      console.error(`❌ Unknown command: ${command}`);
      process.exit(1);
  }
}

module.exports = VercelConfigGenerator;
