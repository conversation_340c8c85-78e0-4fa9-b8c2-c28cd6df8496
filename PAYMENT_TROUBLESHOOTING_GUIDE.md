# WiggyZ Payment Processing Troubleshooting Guide

## Current Issue Analysis

Based on the error screenshot showing "FormatException: Unsupported encoding 'UTF-F-8'", the issue is likely caused by:

1. **Missing Razorpay Credentials**: Your `.env` file contains placeholder values
2. **Character Encoding Issues**: API responses not properly formatted
3. **Backend Service Initialization Failure**: Razorpay service failing to start

## Quick Fix Steps

### 1. Update Razorpay Credentials

**Current Status**: ❌ Placeholder values in `.env`
```env
RAZORPAY_KEY_ID=rzp_test_REPLACE_WITH_YOUR_ACTUAL_KEY_ID
RAZORPAY_KEY_SECRET=REPLACE_WITH_YOUR_ACTUAL_KEY_SECRET
RAZORPAY_WEBHOOK_SECRET=REPLACE_WITH_YOUR_ACTUAL_WEBHOOK_SECRET
```

**Required Action**: Replace with actual credentials from <PERSON><PERSON>pay Dashboard

### 2. Test Configuration

Run the test script to verify your setup:
```bash
cd backend
node test-razorpay.js
```

### 3. Restart Backend Server

After updating credentials:
```bash
cd backend
npm run dev
```

## Razorpay Account Setup Checklist

### ✅ Account Creation
- [ ] Sign up at https://dashboard.razorpay.com
- [ ] Verify email address
- [ ] Complete basic profile

### ✅ KYC Verification (for Live Mode)
- [ ] Upload PAN card
- [ ] Provide business details
- [ ] Add bank account details
- [ ] Submit for verification

### ✅ API Keys Generation
- [ ] Go to Settings → API Keys
- [ ] Generate Test Keys (for development)
- [ ] Copy Key ID (starts with `rzp_test_`)
- [ ] Copy Key Secret (long alphanumeric string)
- [ ] Store securely in `.env` file

### ✅ Webhook Configuration
- [ ] Go to Settings → Webhooks
- [ ] Create new webhook
- [ ] URL: `http://localhost:5000/api/v1/wallet/webhook/razorpay` (development)
- [ ] URL: `https://your-domain.com/api/v1/wallet/webhook/razorpay` (production)
- [ ] Events: `payment.authorized`, `payment.captured`, `payment.failed`, `order.paid`
- [ ] Generate webhook secret
- [ ] Store secret in `.env` file

### ✅ Payment Methods Configuration
- [ ] Enable Cards (Visa, Mastercard, RuPay)
- [ ] Enable UPI
- [ ] Enable Net Banking
- [ ] Enable Wallets (Paytm, PhonePe, etc.)
- [ ] Set payment limits

## Testing Guide

### Test Card Numbers (Razorpay Test Mode)
```
Success: ************** 1111
Failure: ************** 0002
CVV: Any 3 digits
Expiry: Any future date
```

### Test UPI IDs
```
Success: success@razorpay
Failure: failure@razorpay
```

### Test Flow
1. Start with small amount (₹10)
2. Use test credentials
3. Monitor backend logs
4. Check payment status in Razorpay dashboard

## Common Error Solutions

### "FormatException: Unsupported encoding 'UTF-F-8'"
**Cause**: Character encoding issues in API responses
**Solution**: 
- Updated Express.js to set proper charset headers
- Restart backend server after credential update

### "Missing required environment variable"
**Cause**: Placeholder values in `.env` file
**Solution**: Replace with actual Razorpay credentials

### "Authentication failed"
**Cause**: Incorrect API keys
**Solution**: 
- Verify Key ID and Secret from Razorpay dashboard
- Ensure using correct environment (test vs live)

### "Payment initialization failed"
**Cause**: Razorpay service not properly initialized
**Solution**: 
- Check environment variables
- Restart backend server
- Run test script to verify configuration

## Environment Variables Reference

```env
# Razorpay Test Mode (for development)
RAZORPAY_KEY_ID=rzp_test_1234567890abcdef
RAZORPAY_KEY_SECRET=abcdef1234567890abcdef1234567890abcdef12
RAZORPAY_WEBHOOK_SECRET=webhook_secret_1234567890abcdef

# Razorpay Live Mode (for production)
# RAZORPAY_KEY_ID=rzp_live_1234567890abcdef
# RAZORPAY_KEY_SECRET=abcdef1234567890abcdef1234567890abcdef12
# RAZORPAY_WEBHOOK_SECRET=webhook_secret_1234567890abcdef

# Payment Configuration
PAYMENT_TIMEOUT_MINUTES=15
PAYMENT_MAX_AMOUNT=10000000  # ₹100,000 in paise
PAYMENT_MIN_AMOUNT=100       # ₹1 in paise
```

## Next Steps After Setup

1. **Test Payment Flow**: Try adding ₹10 to wallet
2. **Monitor Logs**: Check backend console for errors
3. **Verify Database**: Ensure transactions are recorded
4. **Test Edge Cases**: Try failed payments, timeouts
5. **Production Setup**: Generate live keys when ready

## Support Resources

- **Razorpay Documentation**: https://razorpay.com/docs/
- **API Reference**: https://razorpay.com/docs/api/
- **Test Cards**: https://razorpay.com/docs/payment-gateway/test-card-details/
- **Webhooks Guide**: https://razorpay.com/docs/webhooks/

## Contact Information

If issues persist after following this guide:
1. Check Razorpay dashboard for error details
2. Review backend server logs
3. Test with minimal amount (₹1)
4. Contact Razorpay support if needed
