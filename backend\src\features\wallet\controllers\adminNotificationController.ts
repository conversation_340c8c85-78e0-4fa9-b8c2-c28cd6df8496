/**
 * Admin notification controller
 * Handles admin notification operations
 */
import { Request, Response } from 'express';
import Jo<PERSON> from 'joi';
import { supabase } from '../../../config/supabase';
import { successResponse, errorResponse } from '../../../utils/responseFormatter';

/**
 * Get admin notifications with pagination
 * @route GET /api/v1/admin/notifications
 */
export const getAdminNotifications = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'superadmin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    // Validate query parameters
    const schema = Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      type: Joi.string().optional(),
      severity: Joi.string().valid('info', 'warning', 'error', 'critical').optional(),
      is_read: Joi.boolean().optional(),
      action_required: Joi.boolean().optional()
    });

    const { error, value } = schema.validate(req.query);
    if (error) {
      return res.status(400).json(errorResponse(error.details[0].message));
    }

    const { page, limit, type, severity, is_read, action_required } = value;

    // Build query
    let query = supabase
      .from('admin_notifications')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Apply filters
    if (type) {
      query = query.eq('type', type);
    }
    if (severity) {
      query = query.eq('severity', severity);
    }
    if (is_read !== undefined) {
      query = query.eq('is_read', is_read);
    }
    if (action_required !== undefined) {
      query = query.eq('action_required', action_required);
    }

    // Apply pagination
    const { data: notifications, error: fetchError, count } = await query
      .range((page - 1) * limit, page * limit - 1);

    if (fetchError) {
      throw new Error(`Error fetching admin notifications: ${fetchError.message}`);
    }

    const totalPages = Math.ceil((count || 0) / limit);

    const result = {
      notifications: notifications || [],
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_items: count || 0,
        items_per_page: limit
      }
    };
    
    return res.status(200).json(successResponse(result, 'Admin notifications retrieved successfully'));
  } catch (err: any) {
    console.error('Error fetching admin notifications:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Get admin notification summary/counts
 * @route GET /api/v1/admin/notifications/summary
 */
export const getAdminNotificationSummary = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'superadmin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    // Get notification summary
    const { data: summary, error } = await supabase
      .from('admin_notifications_summary')
      .select('*')
      .single();

    if (error) {
      throw new Error(`Error fetching notification summary: ${error.message}`);
    }
    
    return res.status(200).json(successResponse(summary, 'Notification summary retrieved successfully'));
  } catch (err: any) {
    console.error('Error fetching notification summary:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Mark notification as read
 * @route PUT /api/v1/admin/notifications/:id/read
 */
export const markNotificationAsRead = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'superadmin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    const { id } = req.params;

    if (!id) {
      return res.status(400).json(errorResponse('Notification ID is required'));
    }

    // Update notification as read
    const { data: notification, error } = await supabase
      .from('admin_notifications')
      .update({ is_read: true })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Error marking notification as read: ${error.message}`);
    }

    if (!notification) {
      return res.status(404).json(errorResponse('Notification not found'));
    }
    
    return res.status(200).json(successResponse(notification, 'Notification marked as read'));
  } catch (err: any) {
    console.error('Error marking notification as read:', err);
    
    if (err.message.includes('not found')) {
      return res.status(404).json(errorResponse('Notification not found'));
    }
    
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Mark all notifications as read
 * @route PUT /api/v1/admin/notifications/read-all
 */
export const markAllNotificationsAsRead = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'superadmin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    // Update all unread notifications for this admin
    const { data: notifications, error } = await supabase
      .from('admin_notifications')
      .update({ is_read: true })
      .eq('is_read', false)
      .or(`target_admin_id.is.null,target_admin_id.eq.${req.user.userId}`)
      .select();

    if (error) {
      throw new Error(`Error marking notifications as read: ${error.message}`);
    }

    const updatedCount = notifications?.length || 0;
    
    return res.status(200).json(successResponse(
      { updated_count: updatedCount }, 
      `${updatedCount} notifications marked as read`
    ));
  } catch (err: any) {
    console.error('Error marking all notifications as read:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Delete a notification
 * @route DELETE /api/v1/admin/notifications/:id
 */
export const deleteNotification = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'superadmin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    const { id } = req.params;

    if (!id) {
      return res.status(400).json(errorResponse('Notification ID is required'));
    }

    // Delete notification
    const { error } = await supabase
      .from('admin_notifications')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Error deleting notification: ${error.message}`);
    }
    
    return res.status(200).json(successResponse(null, 'Notification deleted successfully'));
  } catch (err: any) {
    console.error('Error deleting notification:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};
