import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;
import 'package:wiggyz_app/services/reward_service.dart';
import 'package:wiggyz_app/utils/reward_error_handler.dart';

// Mock classes
class MockHttpClient extends Mock implements http.Client {}

void main() {
  group('Daily Reward Error Handling Tests', () {
    late RewardService rewardService;
    late MockHttpClient mockHttpClient;

    setUp(() {
      mockHttpClient = MockHttpClient();
      // Note: We would need to inject the mock client into RewardService
      // For now, this test demonstrates the expected behavior
    });

    test('should handle empty response body gracefully', () {
      // Test that empty response body is handled properly
      final error = RewardErrorHandler.createHttpError(
        statusCode: 200,
        responseBody: '',
        endpoint: '/rewards/claim-daily',
      );

      expect(error.type, RewardErrorType.unknownError);
      expect(error.statusCode, 200);
      expect(error.details, contains('Endpoint: /rewards/claim-daily'));
    });

    test('should handle invalid JSON response', () {
      // Test that invalid JSON is handled properly
      final error = RewardErrorHandler.createHttpError(
        statusCode: 200,
        responseBody: 'Invalid JSON response',
        endpoint: '/rewards/claim-daily',
      );

      expect(error.type, RewardErrorType.unknownError);
      expect(error.statusCode, 200);
      expect(error.details, contains('Invalid JSON response'));
    });

    test('should handle network exceptions properly', () {
      // Test network error handling
      final error = RewardErrorHandler.createNetworkError(
        exception: 'SocketException: Connection refused',
        endpoint: '/rewards/claim-daily',
      );

      expect(error.type, RewardErrorType.networkError);
      expect(error.message, 'Network connection failed');
      expect(error.details, contains('SocketException'));
    });

    test('should extract error message from API response', () {
      // Test API error message extraction
      final error = RewardErrorHandler.createHttpError(
        statusCode: 400,
        responseBody: '{"success": false, "message": "Daily reward already claimed today"}',
        endpoint: '/rewards/claim-daily',
      );

      expect(error.type, RewardErrorType.validationError);
      expect(error.message, 'Daily reward already claimed today');
      expect(error.statusCode, 400);
    });

    test('should handle different HTTP status codes correctly', () {
      // Test various status codes
      final testCases = [
        (401, RewardErrorType.authenticationError),
        (403, RewardErrorType.authenticationError),
        (404, RewardErrorType.rewardNotFound),
        (429, RewardErrorType.rateLimitError),
        (500, RewardErrorType.serverError),
        (502, RewardErrorType.serverError),
        (503, RewardErrorType.serverError),
        (504, RewardErrorType.serverError),
      ];

      for (final (statusCode, expectedType) in testCases) {
        final error = RewardErrorHandler.createHttpError(
          statusCode: statusCode,
          responseBody: '{"message": "Test error"}',
        );

        expect(error.type, expectedType, 
               reason: 'Status code $statusCode should map to $expectedType');
        expect(error.statusCode, statusCode);
      }
    });

    test('should provide user-friendly error messages', () {
      // Test user-friendly messages
      final networkError = RewardError(
        type: RewardErrorType.networkError,
        message: 'Network failed',
      );

      final authError = RewardError(
        type: RewardErrorType.authenticationError,
        message: 'Auth failed',
      );

      expect(networkError.type.userFriendlyMessage, 
             contains('Network connection issue'));
      expect(authError.type.userFriendlyMessage, 
             contains('Authentication failed'));
    });

    test('should detect exception types correctly', () {
      // Test exception type detection
      final networkException = RewardError.fromException('SocketException: Connection refused');
      expect(networkException.type, RewardErrorType.networkError);

      final jsonException = RewardError.fromException('FormatException: Unexpected character');
      expect(jsonException.type, RewardErrorType.serverError);

      final unknownException = RewardError.fromException('Some random error');
      expect(unknownException.type, RewardErrorType.unknownError);
    });
  });
}
