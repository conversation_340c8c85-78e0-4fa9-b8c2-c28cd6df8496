#!/usr/bin/env node

/**
 * Test Help & Support System Fixes
 * Tests both rate limiting and UI fixes
 */

const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:5000/api/v1';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Advanture101$'
};

let authToken = null;

async function login() {
  try {
    console.log('🔐 Logging in test user...');
    const response = await axios.post(`${BASE_URL}/auth/login`, TEST_USER);

    if (response.data && response.data.access_token) {
      authToken = response.data.access_token;
      console.log('✅ Login successful');
      console.log(`   User: ${response.data.user.name} (${response.data.user.role})`);
      return true;
    } else {
      console.log('❌ Login failed - no token received');
      console.log('   Response:', JSON.stringify(response.data, null, 2));
      return false;
    }
  } catch (error) {
    console.log('❌ Login failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testRateLimitingFix() {
  console.log('\n🚦 Testing Rate Limiting Fix...');
  
  const testMessage = {
    subject: 'Test Rate Limiting Fix',
    category: 'general_inquiry',
    message: 'Testing if the rate limiting has been fixed to allow legitimate support messages.'
  };

  let successCount = 0;
  let rateLimitHit = false;
  
  // Test multiple message submissions to verify rate limiting is reasonable
  for (let i = 1; i <= 5; i++) {
    try {
      console.log(`   📝 Attempting message ${i}/5...`);
      
      const response = await axios.post(
        `${BASE_URL}/support/messages`,
        {
          ...testMessage,
          subject: `${testMessage.subject} - Message ${i}`
        },
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.status === 201) {
        console.log(`   ✅ Message ${i} created successfully`);
        successCount++;
      }
      
      // Wait a bit between requests to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      const status = error.response?.status;
      const errorMsg = error.response?.data?.error || error.message;
      
      if (status === 429) {
        console.log(`   ⚠️  Rate limit hit at message ${i}: ${errorMsg}`);
        rateLimitHit = true;
        break;
      } else {
        console.log(`   ❌ Message ${i} failed (${status}): ${errorMsg}`);
      }
    }
  }

  console.log(`\n📊 Rate Limiting Test Results:`);
  console.log(`   ✅ Successful messages: ${successCount}/5`);
  console.log(`   🚦 Rate limit behavior: ${rateLimitHit ? 'Hit after ' + successCount + ' messages' : 'No rate limit hit'}`);
  
  // Evaluate results
  if (successCount >= 3) {
    console.log('🎉 Rate limiting fix successful! Users can submit multiple messages.');
    return true;
  } else if (successCount >= 1) {
    console.log('⚠️  Partial success - some messages went through but rate limiting may still be too strict.');
    return false;
  } else {
    console.log('❌ Rate limiting fix failed - no messages could be submitted.');
    return false;
  }
}

async function testPublicEndpoints() {
  console.log('\n🌐 Testing Public FAQ Endpoints...');
  
  try {
    // Test FAQ categories
    const categoriesResponse = await axios.get(`${BASE_URL}/support/faq/categories`);
    console.log('✅ FAQ categories endpoint working');
    console.log(`   Found ${categoriesResponse.data.data.length} categories`);

    // Test FAQ items
    const itemsResponse = await axios.get(`${BASE_URL}/support/faq/items`);
    console.log('✅ FAQ items endpoint working');
    console.log(`   Found ${itemsResponse.data.data.length} items`);

    return true;
  } catch (error) {
    console.log('❌ Public endpoints test failed:', error.message);
    return false;
  }
}

async function testUserMessageRetrieval() {
  console.log('\n📋 Testing User Message Retrieval...');
  
  try {
    const response = await axios.get(
      `${BASE_URL}/support/messages`,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.status === 200) {
      console.log('✅ User messages retrieved successfully');
      console.log(`📊 Total messages: ${response.data.data.length}`);
      
      if (response.data.data.length > 0) {
        const latestMessage = response.data.data[0];
        console.log('📄 Latest message:');
        console.log(`   Subject: ${latestMessage.subject}`);
        console.log(`   Status: ${latestMessage.status}`);
        console.log(`   Created: ${new Date(latestMessage.created_at).toLocaleString()}`);
      }
      
      return true;
    }
  } catch (error) {
    console.log('❌ Message retrieval failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function runComprehensiveTest() {
  console.log('🚀 Starting Help & Support System Fixes Test\n');
  console.log('Testing both rate limiting and backend functionality...\n');
  
  // Step 1: Login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }

  // Step 2: Test public endpoints
  const publicEndpointsWork = await testPublicEndpoints();

  // Step 3: Test rate limiting fix
  const rateLimitingFixed = await testRateLimitingFix();

  // Step 4: Test message retrieval
  const messageRetrievalWorks = await testUserMessageRetrieval();

  // Summary
  console.log('\n📊 Comprehensive Test Summary:');
  console.log('=====================================');
  console.log('✅ Authentication:', loginSuccess ? 'PASSED' : 'FAILED');
  console.log('✅ Public Endpoints:', publicEndpointsWork ? 'PASSED' : 'FAILED');
  console.log('✅ Rate Limiting Fix:', rateLimitingFixed ? 'PASSED' : 'FAILED');
  console.log('✅ Message Retrieval:', messageRetrievalWorks ? 'PASSED' : 'FAILED');
  
  const allTestsPassed = loginSuccess && publicEndpointsWork && rateLimitingFixed && messageRetrievalWorks;
  
  if (allTestsPassed) {
    console.log('\n🎉 ALL TESTS PASSED! Help & Support system fixes are working correctly.');
    console.log('\n📝 Fixes Applied:');
    console.log('   • Rate limiting increased from 3 to 10 messages per hour');
    console.log('   • Removed duplicate rate limiting middleware');
    console.log('   • Improved tab selection visibility in UI');
    console.log('   • Enhanced user experience for support message submission');
    
    console.log('\n🌐 Frontend Testing:');
    console.log('   • Visit http://localhost:3000 to test the UI fixes');
    console.log('   • Navigate to Help & Support to see improved tab visibility');
    console.log('   • Try submitting support messages through Contact Us form');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the fixes.');
  }
}

// Run the comprehensive test
runComprehensiveTest().catch(console.error);
