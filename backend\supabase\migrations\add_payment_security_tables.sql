-- Migration: Add payment security and audit tables
-- Created: 2024-01-XX
-- Description: Add tables for payment security, user blocks, and audit logging

-- User Security Blocks Table
-- Stores temporary blocks for suspicious payment activity
CREATE TABLE IF NOT EXISTS user_security_blocks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  reason TEXT NOT NULL,
  blocked_until TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id), -- Admin who created the block
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Indexes for performance
  INDEX idx_user_security_blocks_user_id (user_id),
  INDEX idx_user_security_blocks_blocked_until (blocked_until),
  INDEX idx_user_security_blocks_active (is_active)
);

-- Payment Audit Log Table
-- Comprehensive logging for all payment operations
CREATE TABLE IF NOT EXISTS payment_audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  transaction_id UUID, -- Reference to wallet_transactions.id
  operation VARCHAR(50) NOT NULL, -- payment_initiation, payment_verification, webhook_processing, etc.
  status VARCHAR(20) NOT NULL, -- success, failed, pending
  ip_address INET,
  user_agent TEXT,
  request_data JSONB,
  response_data JSONB,
  error_message TEXT,
  processing_time_ms INTEGER,
  gateway VARCHAR(20), -- razorpay, stripe, etc.
  amount DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'INR',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes for performance and analytics
  INDEX idx_payment_audit_logs_user_id (user_id),
  INDEX idx_payment_audit_logs_transaction_id (transaction_id),
  INDEX idx_payment_audit_logs_operation (operation),
  INDEX idx_payment_audit_logs_status (status),
  INDEX idx_payment_audit_logs_created_at (created_at),
  INDEX idx_payment_audit_logs_gateway (gateway),
  INDEX idx_payment_audit_logs_ip_address (ip_address)
);

-- Webhook Events Table
-- Store all webhook events for debugging and audit
CREATE TABLE IF NOT EXISTS webhook_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  gateway VARCHAR(20) NOT NULL,
  event_type VARCHAR(50) NOT NULL,
  event_id VARCHAR(100), -- Gateway's event ID for deduplication
  payload JSONB NOT NULL,
  signature VARCHAR(500),
  ip_address INET,
  processed BOOLEAN DEFAULT FALSE,
  processed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint for event deduplication
  UNIQUE(gateway, event_id),
  
  -- Indexes
  INDEX idx_webhook_events_gateway (gateway),
  INDEX idx_webhook_events_event_type (event_type),
  INDEX idx_webhook_events_processed (processed),
  INDEX idx_webhook_events_created_at (created_at),
  INDEX idx_webhook_events_event_id (event_id)
);

-- Payment Rate Limits Table
-- Track rate limiting for payment operations
CREATE TABLE IF NOT EXISTS payment_rate_limits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  identifier VARCHAR(100) NOT NULL, -- IP:UserID or similar
  operation VARCHAR(50) NOT NULL,
  request_count INTEGER DEFAULT 1,
  window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  window_end TIMESTAMP WITH TIME ZONE,
  blocked_until TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint for rate limiting
  UNIQUE(identifier, operation, window_start),
  
  -- Indexes
  INDEX idx_payment_rate_limits_identifier (identifier),
  INDEX idx_payment_rate_limits_operation (operation),
  INDEX idx_payment_rate_limits_window_end (window_end),
  INDEX idx_payment_rate_limits_blocked_until (blocked_until)
);

-- Update wallet_transactions table to add payment gateway specific fields
ALTER TABLE wallet_transactions 
ADD COLUMN IF NOT EXISTS payment_gateway_response JSONB,
ADD COLUMN IF NOT EXISTS error_message TEXT,
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP WITH TIME ZONE;

-- Add indexes to wallet_transactions for better performance
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_payment_gateway ON wallet_transactions(payment_gateway);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_payment_token ON wallet_transactions(payment_token);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_expires_at ON wallet_transactions(expires_at);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_status_created ON wallet_transactions(status, created_at);

-- Row Level Security Policies

-- User Security Blocks - Only admins can view/manage
ALTER TABLE user_security_blocks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can manage user security blocks" 
  ON user_security_blocks 
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' IN ('admin', 'super_admin')
    )
  );

-- Payment Audit Logs - Users can view their own, admins can view all
ALTER TABLE payment_audit_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own payment audit logs" 
  ON payment_audit_logs 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all payment audit logs" 
  ON payment_audit_logs 
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' IN ('admin', 'super_admin')
    )
  );

-- Webhook Events - Only system/admins can access
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Only admins can access webhook events" 
  ON webhook_events 
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' IN ('admin', 'super_admin')
    )
  );

-- Payment Rate Limits - Only system/admins can access
ALTER TABLE payment_rate_limits ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Only admins can access payment rate limits" 
  ON payment_rate_limits 
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' IN ('admin', 'super_admin')
    )
  );

-- Functions for cleanup and maintenance

-- Function to clean up expired rate limits
CREATE OR REPLACE FUNCTION cleanup_expired_rate_limits()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM payment_rate_limits 
  WHERE window_end < NOW() - INTERVAL '1 hour';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old audit logs (keep last 90 days)
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM payment_audit_logs 
  WHERE created_at < NOW() - INTERVAL '90 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up processed webhook events (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_webhook_events()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM webhook_events 
  WHERE processed = TRUE 
  AND created_at < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run cleanup functions (if pg_cron is available)
-- SELECT cron.schedule('cleanup-payment-data', '0 2 * * *', 'SELECT cleanup_expired_rate_limits(), cleanup_old_audit_logs(), cleanup_old_webhook_events();');

COMMENT ON TABLE user_security_blocks IS 'Stores temporary security blocks for users with suspicious payment activity';
COMMENT ON TABLE payment_audit_logs IS 'Comprehensive audit log for all payment operations';
COMMENT ON TABLE webhook_events IS 'Stores all webhook events from payment gateways for debugging and audit';
COMMENT ON TABLE payment_rate_limits IS 'Tracks rate limiting for payment operations to prevent abuse';
