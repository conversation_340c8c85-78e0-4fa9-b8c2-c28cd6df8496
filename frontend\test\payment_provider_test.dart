import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wiggyz_app/providers/payment_provider.dart';
import 'package:wiggyz_app/screens/payment_settings_screen.dart';

void main() {
  group('PaymentProvider Tests', () {
    late PaymentProvider paymentProvider;

    setUp(() {
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      paymentProvider = PaymentProvider();
    });

    tearDown(() {
      paymentProvider.dispose();
    });

    group('Initialization', () {
      test('should initialize with empty payment methods', () async {
        await paymentProvider.init();
        
        expect(paymentProvider.paymentMethods, isEmpty);
        expect(paymentProvider.defaultPaymentMethodId, isNull);
        expect(paymentProvider.isLoading, isFalse);
        expect(paymentProvider.error, isNull);
      });

      test('should load saved payment methods from SharedPreferences', () async {
        // Setup mock data
        final mockPaymentMethods = [
          {
            'id': '1',
            'type': 'upi',
            'name': 'UPI - test@okaxis',
            'isDefault': true,
            'lastUsed': 'Last used today',
          },
          {
            'id': '2',
            'type': 'creditCard',
            'name': 'Credit Card ••••1234',
            'isDefault': false,
            'lastUsed': 'Last used yesterday',
          },
        ];

        SharedPreferences.setMockInitialValues({
          'saved_payment_methods': '[${mockPaymentMethods.map((m) => m.toString().replaceAll("'", '"')).join(',')}]',
          'default_payment_method_id': '1',
        });

        await paymentProvider.init();

        expect(paymentProvider.paymentMethods.length, equals(2));
        expect(paymentProvider.defaultPaymentMethodId, equals('1'));
        expect(paymentProvider.defaultPaymentMethod?.type, equals(PaymentType.upi));
      });
    });

    group('Add Payment Method', () {
      test('should add new payment method successfully', () async {
        await paymentProvider.init();

        final newMethod = PaymentMethod(
          type: PaymentType.bitcoin,
          name: 'Bitcoin Wallet - bc1q...xyz',
          isDefault: false,
          lastUsed: 'Added today',
        );

        final result = await paymentProvider.addPaymentMethod(newMethod);

        expect(result, isTrue);
        expect(paymentProvider.paymentMethods.length, equals(1));
        expect(paymentProvider.paymentMethods.first.type, equals(PaymentType.bitcoin));
        expect(paymentProvider.paymentMethods.first.id, isNotNull);
      });

      test('should set first payment method as default', () async {
        await paymentProvider.init();

        final newMethod = PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: false,
          lastUsed: 'Added today',
        );

        await paymentProvider.addPaymentMethod(newMethod);

        expect(paymentProvider.defaultPaymentMethodId, isNotNull);
        expect(paymentProvider.defaultPaymentMethod?.type, equals(PaymentType.upi));
      });

      test('should not set subsequent methods as default', () async {
        await paymentProvider.init();

        // Add first method
        final firstMethod = PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: false,
          lastUsed: 'Added today',
        );
        await paymentProvider.addPaymentMethod(firstMethod);
        final firstDefaultId = paymentProvider.defaultPaymentMethodId;

        // Add second method
        final secondMethod = PaymentMethod(
          type: PaymentType.bitcoin,
          name: 'Bitcoin Wallet - bc1q...xyz',
          isDefault: false,
          lastUsed: 'Added today',
        );
        await paymentProvider.addPaymentMethod(secondMethod);

        expect(paymentProvider.paymentMethods.length, equals(2));
        expect(paymentProvider.defaultPaymentMethodId, equals(firstDefaultId));
      });
    });

    group('Update Payment Method', () {
      test('should update existing payment method', () async {
        await paymentProvider.init();

        // Add a method first
        final originalMethod = PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - old@bank',
          isDefault: false,
          lastUsed: 'Added today',
        );
        await paymentProvider.addPaymentMethod(originalMethod);
        final methodId = paymentProvider.paymentMethods.first.id!;

        // Update the method
        final updatedMethod = PaymentMethod(
          id: methodId,
          type: PaymentType.upi,
          name: 'UPI - new@bank',
          isDefault: false,
          lastUsed: 'Updated today',
        );

        final result = await paymentProvider.updatePaymentMethod(updatedMethod);

        expect(result, isTrue);
        expect(paymentProvider.paymentMethods.length, equals(1));
        expect(paymentProvider.paymentMethods.first.name, equals('UPI - new@bank'));
        expect(paymentProvider.paymentMethods.first.lastUsed, equals('Updated today'));
      });

      test('should fail to update non-existent payment method', () async {
        await paymentProvider.init();

        final nonExistentMethod = PaymentMethod(
          id: 'non-existent-id',
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: false,
          lastUsed: 'Today',
        );

        final result = await paymentProvider.updatePaymentMethod(nonExistentMethod);

        expect(result, isFalse);
        expect(paymentProvider.error, isNotNull);
      });
    });

    group('Delete Payment Method', () {
      test('should delete payment method successfully', () async {
        await paymentProvider.init();

        // Add a method first
        final method = PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: false,
          lastUsed: 'Added today',
        );
        await paymentProvider.addPaymentMethod(method);
        final methodId = paymentProvider.paymentMethods.first.id!;

        final result = await paymentProvider.deletePaymentMethod(methodId);

        expect(result, isTrue);
        expect(paymentProvider.paymentMethods, isEmpty);
        expect(paymentProvider.defaultPaymentMethodId, isNull);
      });

      test('should update default when deleting default method', () async {
        await paymentProvider.init();

        // Add two methods
        final firstMethod = PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: false,
          lastUsed: 'Added today',
        );
        await paymentProvider.addPaymentMethod(firstMethod);
        final firstId = paymentProvider.paymentMethods.first.id!;

        final secondMethod = PaymentMethod(
          type: PaymentType.bitcoin,
          name: 'Bitcoin Wallet',
          isDefault: false,
          lastUsed: 'Added today',
        );
        await paymentProvider.addPaymentMethod(secondMethod);

        // Delete the default (first) method
        await paymentProvider.deletePaymentMethod(firstId);

        expect(paymentProvider.paymentMethods.length, equals(1));
        expect(paymentProvider.defaultPaymentMethodId, isNotNull);
        expect(paymentProvider.defaultPaymentMethod?.type, equals(PaymentType.bitcoin));
      });
    });

    group('Set Default Payment Method', () {
      test('should set payment method as default', () async {
        await paymentProvider.init();

        // Add two methods
        final firstMethod = PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: false,
          lastUsed: 'Added today',
        );
        await paymentProvider.addPaymentMethod(firstMethod);

        final secondMethod = PaymentMethod(
          type: PaymentType.bitcoin,
          name: 'Bitcoin Wallet',
          isDefault: false,
          lastUsed: 'Added today',
        );
        await paymentProvider.addPaymentMethod(secondMethod);
        final secondId = paymentProvider.paymentMethods.last.id!;

        // Set second method as default
        final result = await paymentProvider.setDefaultPaymentMethod(secondId);

        expect(result, isTrue);
        expect(paymentProvider.defaultPaymentMethodId, equals(secondId));
        expect(paymentProvider.defaultPaymentMethod?.type, equals(PaymentType.bitcoin));
      });

      test('should fail to set non-existent method as default', () async {
        await paymentProvider.init();

        final result = await paymentProvider.setDefaultPaymentMethod('non-existent-id');

        expect(result, isFalse);
        expect(paymentProvider.error, isNotNull);
      });
    });

    group('Utility Methods', () {
      test('should get payment methods by type', () async {
        await paymentProvider.init();

        // Add methods of different types
        await paymentProvider.addPaymentMethod(PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: false,
          lastUsed: 'Today',
        ));

        await paymentProvider.addPaymentMethod(PaymentMethod(
          type: PaymentType.bitcoin,
          name: 'Bitcoin Wallet',
          isDefault: false,
          lastUsed: 'Today',
        ));

        await paymentProvider.addPaymentMethod(PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - another@bank',
          isDefault: false,
          lastUsed: 'Today',
        ));

        final upiMethods = paymentProvider.getPaymentMethodsByType(PaymentType.upi);
        final bitcoinMethods = paymentProvider.getPaymentMethodsByType(PaymentType.bitcoin);

        expect(upiMethods.length, equals(2));
        expect(bitcoinMethods.length, equals(1));
      });

      test('should check if payment type exists', () async {
        await paymentProvider.init();

        await paymentProvider.addPaymentMethod(PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: false,
          lastUsed: 'Today',
        ));

        expect(paymentProvider.hasPaymentMethodOfType(PaymentType.upi), isTrue);
        expect(paymentProvider.hasPaymentMethodOfType(PaymentType.bitcoin), isFalse);
      });
    });

    group('Error Handling', () {
      test('should clear error state', () async {
        await paymentProvider.init();

        // Trigger an error by trying to update non-existent method
        await paymentProvider.updatePaymentMethod(PaymentMethod(
          id: 'non-existent',
          type: PaymentType.upi,
          name: 'Test',
          isDefault: false,
          lastUsed: 'Today',
        ));

        expect(paymentProvider.error, isNotNull);

        paymentProvider.clearError();

        expect(paymentProvider.error, isNull);
      });
    });
  });
}
