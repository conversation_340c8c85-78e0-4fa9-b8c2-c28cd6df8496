import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:wiggyz_app/features/tournament_service.dart';

/// Service for real-time tournament status updates
/// Provides polling-based updates for tournament verification progress
class TournamentStatusService extends ChangeNotifier {
  static final TournamentStatusService _instance = TournamentStatusService._internal();
  factory TournamentStatusService() => _instance;
  TournamentStatusService._internal();

  final TournamentService _tournamentService = TournamentService();
  final Map<String, Timer> _activePollers = {};
  final Map<String, Map<String, dynamic>> _statusCache = {};
  
  // Polling configuration
  static const Duration _pollingInterval = Duration(seconds: 30);
  static const Duration _fastPollingInterval = Duration(seconds: 10);
  static const int _maxRetries = 3;

  /// Start polling for tournament status updates
  /// Uses fast polling for pending results, normal polling for other statuses
  void startPolling(String tournamentId, {bool fastPolling = false}) {
    // Stop existing polling for this tournament
    stopPolling(tournamentId);

    final interval = fastPolling ? _fastPollingInterval : _pollingInterval;
    
    print('Starting tournament status polling for $tournamentId (interval: ${interval.inSeconds}s)');
    
    _activePollers[tournamentId] = Timer.periodic(interval, (timer) async {
      await _pollTournamentStatus(tournamentId);
    });

    // Initial poll
    _pollTournamentStatus(tournamentId);
  }

  /// Stop polling for a specific tournament
  void stopPolling(String tournamentId) {
    final timer = _activePollers[tournamentId];
    if (timer != null) {
      timer.cancel();
      _activePollers.remove(tournamentId);
      print('Stopped tournament status polling for $tournamentId');
    }
  }

  /// Stop all active polling
  void stopAllPolling() {
    for (final timer in _activePollers.values) {
      timer.cancel();
    }
    _activePollers.clear();
    _statusCache.clear();
    print('Stopped all tournament status polling');
  }

  /// Get cached status for a tournament
  Map<String, dynamic>? getCachedStatus(String tournamentId) {
    return _statusCache[tournamentId];
  }

  /// Poll tournament status with retry logic
  Future<void> _pollTournamentStatus(String tournamentId) async {
    int retryCount = 0;
    
    while (retryCount < _maxRetries) {
      try {
        final result = await _tournamentService.getTournamentVerificationStatus(tournamentId);
        
        if (result['success'] == true) {
          final newStatus = result['data'];
          final oldStatus = _statusCache[tournamentId];
          
          // Update cache
          _statusCache[tournamentId] = newStatus;
          
          // Check if status changed
          if (_hasStatusChanged(oldStatus, newStatus)) {
            print('Tournament $tournamentId status changed: ${newStatus['verification_status']}');
            notifyListeners();
            
            // Adjust polling frequency based on status
            _adjustPollingFrequency(tournamentId, newStatus);
          }
          
          break; // Success, exit retry loop
        } else {
          throw Exception(result['error'] ?? 'Failed to get tournament status');
        }
      } catch (e) {
        retryCount++;
        print('Error polling tournament $tournamentId status (attempt $retryCount): $e');
        
        if (retryCount >= _maxRetries) {
          print('Max retries reached for tournament $tournamentId, stopping polling');
          stopPolling(tournamentId);
          break;
        }
        
        // Wait before retry
        await Future.delayed(Duration(seconds: retryCount * 2));
      }
    }
  }

  /// Check if tournament status has meaningfully changed
  bool _hasStatusChanged(Map<String, dynamic>? oldStatus, Map<String, dynamic> newStatus) {
    if (oldStatus == null) return true;
    
    // Check key fields that indicate status changes
    final oldVerificationStatus = oldStatus['verification_status'];
    final newVerificationStatus = newStatus['verification_status'];
    
    final oldIsWinner = oldStatus['is_winner'];
    final newIsWinner = newStatus['is_winner'];
    
    final oldTournamentStatus = oldStatus['tournament']?['status'];
    final newTournamentStatus = newStatus['tournament']?['status'];
    
    return oldVerificationStatus != newVerificationStatus ||
           oldIsWinner != newIsWinner ||
           oldTournamentStatus != newTournamentStatus;
  }

  /// Adjust polling frequency based on tournament status
  void _adjustPollingFrequency(String tournamentId, Map<String, dynamic> status) {
    final verificationStatus = status['verification_status'];
    final tournamentStatus = status['tournament']?['status'];
    
    // Use fast polling for pending results or active tournaments
    final needsFastPolling = verificationStatus == 'pending' || 
                            tournamentStatus == 'active';
    
    final currentTimer = _activePollers[tournamentId];
    if (currentTimer != null) {
      final currentInterval = needsFastPolling ? _fastPollingInterval : _pollingInterval;
      
      // Restart with appropriate interval if needed
      stopPolling(tournamentId);
      startPolling(tournamentId, fastPolling: needsFastPolling);
    }
  }

  /// Get status update stream for a tournament
  Stream<Map<String, dynamic>?> getStatusStream(String tournamentId) {
    late StreamController<Map<String, dynamic>?> controller;
    
    void onStatusUpdate() {
      if (!controller.isClosed) {
        controller.add(_statusCache[tournamentId]);
      }
    }
    
    controller = StreamController<Map<String, dynamic>?>(
      onListen: () {
        addListener(onStatusUpdate);
        // Send initial cached status
        onStatusUpdate();
      },
      onCancel: () {
        removeListener(onStatusUpdate);
      },
    );
    
    return controller.stream;
  }

  /// Start polling for result submission status
  void startResultSubmissionPolling(String tournamentId) {
    print('Starting fast polling for result submission: $tournamentId');
    startPolling(tournamentId, fastPolling: true);
  }

  /// Start polling for verification status
  void startVerificationPolling(String tournamentId) {
    print('Starting verification polling: $tournamentId');
    startPolling(tournamentId, fastPolling: true);
  }

  /// Check if tournament has completed verification
  bool isVerificationComplete(String tournamentId) {
    final status = _statusCache[tournamentId];
    if (status == null) return false;
    
    final verificationStatus = status['verification_status'];
    return verificationStatus == 'verified' || verificationStatus == 'rejected';
  }

  /// Check if tournament is completed
  bool isTournamentCompleted(String tournamentId) {
    final status = _statusCache[tournamentId];
    if (status == null) return false;
    
    final tournamentStatus = status['tournament']?['status'];
    return tournamentStatus == 'completed';
  }

  /// Get user-friendly status message
  String getStatusMessage(String tournamentId) {
    final status = _statusCache[tournamentId];
    if (status == null) return 'Loading status...';
    
    final verificationStatus = status['verification_status'];
    final tournamentStatus = status['tournament']?['status'];
    final isWinner = status['is_winner'];
    
    if (tournamentStatus == 'completed') {
      if (isWinner == true) {
        return 'Congratulations! You won this tournament!';
      } else {
        return 'Tournament completed. Thank you for participating!';
      }
    }
    
    switch (verificationStatus) {
      case 'pending':
        return 'Your result is being verified by administrators...';
      case 'verified':
        return 'Your result has been verified and approved!';
      case 'rejected':
        return 'Your result was rejected. Please contact support if you believe this is an error.';
      default:
        if (tournamentStatus == 'active') {
          return 'Tournament is active. Submit your results when ready.';
        } else if (tournamentStatus == 'upcoming') {
          return 'Tournament is starting soon. Get ready!';
        }
        return 'Tournament status: ${tournamentStatus ?? 'Unknown'}';
    }
  }

  @override
  void dispose() {
    stopAllPolling();
    super.dispose();
  }
}
