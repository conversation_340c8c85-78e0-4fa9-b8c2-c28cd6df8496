/**
 * Admin notification routes
 */
import express from 'express';
import {
  getAdminNotifications,
  getAdminNotificationSummary,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification
} from '../controllers/adminNotificationController';
import { authenticate } from '../../../middleware/auth';
import { auditPaymentOperation } from '../../../middleware/paymentSecurity';

const router = express.Router();

// All routes require authentication and admin role
// Role validation is done in each controller method

// Get notification summary/counts
router.get('/summary',
  authenticate,
  auditPaymentOperation('admin_notification_summary'),
  getAdminNotificationSummary
);

// Get all notifications with pagination and filtering
router.get('/',
  authenticate,
  auditPaymentOperation('admin_notification_list'),
  getAdminNotifications
);

// Mark all notifications as read
router.put('/read-all',
  authenticate,
  auditPaymentOperation('admin_notification_read_all'),
  markAllNotificationsAsRead
);

// Mark specific notification as read
router.put('/:id/read',
  authenticate,
  auditPaymentOperation('admin_notification_read'),
  markNotificationAsRead
);

// Delete notification
router.delete('/:id',
  authenticate,
  auditPaymentOperation('admin_notification_delete'),
  deleteNotification
);

export default router;
