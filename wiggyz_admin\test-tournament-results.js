const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://johjwarjusfahxidemjd.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpvaGp3YXJqdXNmYWh4aWRlbWpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMDM3NjcsImV4cCI6MjA2MTU3OTc2N30.F77CeDDNeQqRjhhUjhcB_322RLaP4pBx6-2L0VZ7gJY';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testTournamentPendingResults() {
  try {
    console.log('Testing tournament pending results fetch...');
    
    // First, test basic query
    console.log('Testing basic query...');
    const { data: basicResults, error: basicError } = await supabase
      .from('tournament_results')
      .select('*')
      .eq('verification_status', 'pending');

    console.log('Basic results:', basicResults?.length || 0);
    if (basicError) console.error('Basic error:', basicError);

    // Fetch pending tournament results directly
    const { data: results, error: fetchError, count } = await supabase
      .from('tournament_results')
      .select(`
        *,
        tournaments (
          id,
          name,
          status,
          start_date,
          end_date,
          game_id
        ),
        users!tournament_results_user_id_fkey (
          id,
          name,
          email
        )
      `, { count: 'exact' })
      .eq('verification_status', 'pending')
      .order('submitted_at', { ascending: false })
      .range(0, 19);

    if (fetchError) {
      console.error('Fetch error:', fetchError);
      return;
    }

    console.log(`Found ${results.length} pending tournament results`);
    console.log('Count:', count);

    results.forEach((result, index) => {
      console.log(`\nResult ${index + 1}:`);
      console.log('- ID:', result.id);
      console.log('- Tournament:', result.tournaments?.name);
      console.log('- User:', result.users?.name);
      console.log('- Position:', result.final_position);
      console.log('- Score:', result.final_score);
      console.log('- Screenshot URL:', result.screenshot_url);
      console.log('- Screenshot URLs:', result.screenshot_urls);
      console.log('- Submitted at:', result.submitted_at);
    });

    // Transform results like the admin dashboard does
    const transformedResults = results.map(result => {
      const screenshot_url = result.screenshot_urls && result.screenshot_urls.length > 0
        ? result.screenshot_urls[0]
        : result.screenshot_url;

      return {
        ...result,
        screenshot_url,
        screenshot_urls: result.screenshot_urls || (result.screenshot_url ? [result.screenshot_url] : []),
        tournament_name: result.tournaments?.name || 'Unknown Tournament',
        user_name: result.users?.name || 'Unknown User'
      };
    });

    console.log('\n=== Transformed Results ===');
    transformedResults.forEach((result, index) => {
      console.log(`\nTransformed Result ${index + 1}:`);
      console.log('- Tournament Name:', result.tournament_name);
      console.log('- User Name:', result.user_name);
      console.log('- Screenshot URL:', result.screenshot_url);
      console.log('- Screenshot URLs:', result.screenshot_urls);
      console.log('- Has Screenshots:', result.screenshot_urls && result.screenshot_urls.length > 0);
    });

  } catch (error) {
    console.error('Test error:', error);
  }
}

testTournamentPendingResults();
