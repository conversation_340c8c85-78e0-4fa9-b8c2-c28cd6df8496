/**
 * FAQ Controller
 * Handles FAQ related operations
 */

import { Request, Response } from 'express';
import { faqService } from '../services/faqService';
import { errorHandler } from '../../../utils/errorHandler';
import { logger } from '../../../utils/logger';

/**
 * Get FAQ categories
 */
export const getFaqCategories = async (req: Request, res: Response) => {
  try {
    const categories = await faqService.getFaqCategories();

    res.json({
      message: 'FAQ categories retrieved successfully',
      data: categories
    });
  } catch (error) {
    logger.error(`Error getting FAQ categories: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get FAQ items
 */
export const getFaqItems = async (req: Request, res: Response) => {
  try {
    const items = await faqService.getFaqItems();

    res.json({
      message: 'FAQ items retrieved successfully',
      data: items
    });
  } catch (error) {
    logger.error(`Error getting FAQ items: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get FAQ items by category
 */
export const getFaqItemsByCategory = async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.params;
    const items = await faqService.getFaqItemsByCategory(categoryId);

    res.json({
      message: 'FAQ items retrieved successfully',
      data: items
    });
  } catch (error) {
    logger.error(`Error getting FAQ items by category: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get FAQ item by ID
 */
export const getFaqItemById = async (req: Request, res: Response) => {
  try {
    const { itemId } = req.params;
    const item = await faqService.getFaqItemById(itemId);

    if (!item) {
      return res.status(404).json({ error: 'FAQ item not found' });
    }

    // Increment view count
    await faqService.incrementFaqViewCount(itemId);

    res.json({
      message: 'FAQ item retrieved successfully',
      data: item
    });
  } catch (error) {
    logger.error(`Error getting FAQ item by ID: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Submit FAQ feedback
 */
export const submitFaqFeedback = async (req: Request, res: Response) => {
  try {
    const { itemId } = req.params;
    const userId = req.user?.userId;
    const { is_helpful, feedback_text } = req.body;

    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const feedback = await faqService.submitFaqFeedback({
      faqItemId: itemId,
      userId,
      isHelpful: is_helpful,
      feedbackText: feedback_text
    });

    logger.info(`FAQ feedback submitted: ${feedback.id} by user ${userId}`);

    res.status(201).json({
      message: 'FAQ feedback submitted successfully',
      data: feedback
    });
  } catch (error) {
    logger.error(`Error submitting FAQ feedback: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Create FAQ category (admin only)
 */
export const createFaqCategory = async (req: Request, res: Response) => {
  try {
    const { name, description, display_order } = req.body;

    const category = await faqService.createFaqCategory({
      name,
      description,
      displayOrder: display_order
    });

    logger.info(`FAQ category created: ${category.id}`);

    res.status(201).json({
      message: 'FAQ category created successfully',
      data: category
    });
  } catch (error) {
    logger.error(`Error creating FAQ category: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Update FAQ category (admin only)
 */
export const updateFaqCategory = async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.params;
    const updates = req.body;

    const category = await faqService.updateFaqCategory(categoryId, updates);

    if (!category) {
      return res.status(404).json({ error: 'FAQ category not found' });
    }

    logger.info(`FAQ category updated: ${categoryId}`);

    res.json({
      message: 'FAQ category updated successfully',
      data: category
    });
  } catch (error) {
    logger.error(`Error updating FAQ category: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Delete FAQ category (admin only)
 */
export const deleteFaqCategory = async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.params;

    const success = await faqService.deleteFaqCategory(categoryId);

    if (!success) {
      return res.status(404).json({ error: 'FAQ category not found' });
    }

    logger.info(`FAQ category deleted: ${categoryId}`);

    res.json({
      message: 'FAQ category deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting FAQ category: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Create FAQ item (admin only)
 */
export const createFaqItem = async (req: Request, res: Response) => {
  try {
    const { category_id, question, answer, display_order } = req.body;

    const item = await faqService.createFaqItem({
      categoryId: category_id,
      question,
      answer,
      displayOrder: display_order
    });

    logger.info(`FAQ item created: ${item.id}`);

    res.status(201).json({
      message: 'FAQ item created successfully',
      data: item
    });
  } catch (error) {
    logger.error(`Error creating FAQ item: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Update FAQ item (admin only)
 */
export const updateFaqItem = async (req: Request, res: Response) => {
  try {
    const { itemId } = req.params;
    const updates = req.body;

    const item = await faqService.updateFaqItem(itemId, updates);

    if (!item) {
      return res.status(404).json({ error: 'FAQ item not found' });
    }

    logger.info(`FAQ item updated: ${itemId}`);

    res.json({
      message: 'FAQ item updated successfully',
      data: item
    });
  } catch (error) {
    logger.error(`Error updating FAQ item: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Delete FAQ item (admin only)
 */
export const deleteFaqItem = async (req: Request, res: Response) => {
  try {
    const { itemId } = req.params;

    const success = await faqService.deleteFaqItem(itemId);

    if (!success) {
      return res.status(404).json({ error: 'FAQ item not found' });
    }

    logger.info(`FAQ item deleted: ${itemId}`);

    res.json({
      message: 'FAQ item deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting FAQ item: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get FAQ analytics (admin only)
 */
export const getFaqAnalytics = async (req: Request, res: Response) => {
  try {
    const analytics = await faqService.getFaqAnalytics();

    res.json({
      message: 'FAQ analytics retrieved successfully',
      data: analytics
    });
  } catch (error) {
    logger.error(`Error getting FAQ analytics: ${error}`);
    return errorHandler(error as Error, res);
  }
};
