/**
 * Support System Rate Limiting Middleware
 * Prevents spam and abuse of support features
 */

import { Request, Response, NextFunction } from 'express';
import { supabase } from '../config/supabase';
import { logger } from '../utils/logger';

interface RateLimitConfig {
  maxRequests: number;
  windowHours: number;
  actionType: string;
}

// Rate limit configurations
const RATE_LIMITS: Record<string, RateLimitConfig> = {
  support_message: {
    maxRequests: 10, // Increased from 3 to 10
    windowHours: 1,
    actionType: 'support_message'
  },
  chat_message: {
    maxRequests: 50,
    windowHours: 1,
    actionType: 'chat_message'
  },
  faq_feedback: {
    maxRequests: 10,
    windowHours: 1,
    actionType: 'faq_feedback'
  }
};

/**
 * Generic rate limiting function
 */
async function checkRateLimit(
  userId: string,
  config: RateLimitConfig
): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
  try {
    const now = new Date();
    const windowStart = new Date(now.getTime() - (config.windowHours * 60 * 60 * 1000));
    const windowEnd = new Date(now.getTime() + (config.windowHours * 60 * 60 * 1000));

    // Check existing rate limit record for this user and action type
    const { data: existingRecord, error: fetchError } = await supabase
      .from('rate_limiting')
      .select('*')
      .eq('user_id', userId)
      .eq('action_type', config.actionType)
      .gte('window_end', now.toISOString())
      .order('window_start', { ascending: false })
      .limit(1)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = no rows returned
      logger.error(`Rate limit check error: ${fetchError.message}`);
      // Allow request on error to avoid blocking legitimate users
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: windowEnd
      };
    }

    if (existingRecord) {
      // Check if we're within the rate limit
      if (existingRecord.action_count >= config.maxRequests) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: new Date(existingRecord.window_end)
        };
      }

      // Increment the counter
      const { error: updateError } = await supabase
        .from('rate_limiting')
        .update({
          action_count: existingRecord.action_count + 1
        })
        .eq('id', existingRecord.id);

      if (updateError) {
        logger.error(`Rate limit update error: ${updateError.message}`);
      }

      return {
        allowed: true,
        remaining: config.maxRequests - (existingRecord.action_count + 1),
        resetTime: new Date(existingRecord.window_end)
      };
    } else {
      // Create new rate limit record
      const { error: insertError } = await supabase
        .from('rate_limiting')
        .insert({
          user_id: userId,
          action_type: config.actionType,
          action_count: 1,
          window_start: now.toISOString(),
          window_end: windowEnd.toISOString()
        });

      if (insertError) {
        logger.error(`Rate limit insert error: ${insertError.message}`);
      }

      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: windowEnd
      };
    }
  } catch (error) {
    logger.error(`Rate limit check error: ${error}`);
    // Allow request on error to avoid blocking legitimate users
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: new Date(Date.now() + (config.windowHours * 60 * 60 * 1000))
    };
  }
}

/**
 * Rate limiting middleware factory
 */
function createRateLimitMiddleware(actionType: keyof typeof RATE_LIMITS) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Skip rate limiting if user is not authenticated
      if (!req.user?.userId) {
        return next();
      }

      const config = RATE_LIMITS[actionType];
      if (!config) {
        logger.error(`Unknown rate limit action type: ${actionType}`);
        return next();
      }

      const result = await checkRateLimit(req.user.userId, config);

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': config.maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': Math.ceil(result.resetTime.getTime() / 1000).toString()
      });

      if (!result.allowed) {
        const resetTimeSeconds = Math.ceil((result.resetTime.getTime() - Date.now()) / 1000);
        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: `Too many ${actionType.replace('_', ' ')} requests. Please try again in ${Math.ceil(resetTimeSeconds / 60)} minutes.`,
          retryAfter: resetTimeSeconds
        });
      }

      next();
    } catch (error) {
      logger.error(`Rate limit middleware error: ${error}`);
      // Continue on error to avoid blocking legitimate requests
      next();
    }
  };
}

// Export specific rate limiting middlewares
export const supportRateLimit = createRateLimitMiddleware('support_message');
export const chatRateLimit = createRateLimitMiddleware('chat_message');
export const faqFeedbackRateLimit = createRateLimitMiddleware('faq_feedback');

/**
 * Clean up expired rate limit records
 * This should be called periodically (e.g., via a cron job)
 */
export async function cleanupExpiredRateLimits(): Promise<void> {
  try {
    const now = new Date();
    
    const { error } = await supabase
      .from('rate_limiting')
      .delete()
      .lt('window_end', now.toISOString());

    if (error) {
      logger.error(`Rate limit cleanup error: ${error.message}`);
    } else {
      logger.info('Expired rate limit records cleaned up successfully');
    }
  } catch (error) {
    logger.error(`Rate limit cleanup error: ${error}`);
  }
}

/**
 * Get rate limit status for a user and action type
 */
export async function getRateLimitStatus(
  userId: string,
  actionType: keyof typeof RATE_LIMITS
): Promise<{ remaining: number; resetTime: Date; isLimited: boolean }> {
  const config = RATE_LIMITS[actionType];
  if (!config) {
    throw new Error(`Unknown rate limit action type: ${actionType}`);
  }

  const result = await checkRateLimit(userId, config);
  
  return {
    remaining: result.remaining,
    resetTime: result.resetTime,
    isLimited: !result.allowed
  };
}
