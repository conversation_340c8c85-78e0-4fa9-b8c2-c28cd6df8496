/**
 * Tests for Daily Reward Service
 * Focuses on testing the fixes for streak calculation and reward claiming
 */

import { jest } from '@jest/globals';
import DailyRewardService from '../services/dailyRewardService';

// Mock dependencies
jest.mock('../../../config/supabase', () => ({
  supabase: {
    from: jest.fn(),
    rpc: jest.fn(),
  }
}));

jest.mock('../../../utils/supabaseHelpers', () => ({
  supabaseHelpers: {
    transaction: jest.fn(),
  }
}));

jest.mock('../services/rewardsService', () => ({
  default: {
    createRewardTransaction: jest.fn(),
  }
}));

jest.mock('../services/loyaltyService', () => ({
  loyaltyService: {
    awardLoyaltyPoints: jest.fn(),
  },
  LoyaltyAction: {
    DAILY_LOGIN: 'daily_login',
  }
}));

jest.mock('../../wallet/services/walletService', () => ({
  walletService: {
    addDiamonds: jest.fn(),
  }
}));

const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
  gte: jest.fn().mockReturnThis(),
  lt: jest.fn().mockReturnThis(),
  lte: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  rpc: jest.fn(),
};

describe('DailyRewardService', () => {
  let dailyRewardService: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the supabase import
    jest.doMock('../../../config/supabase', () => ({
      supabase: mockSupabase
    }));

    // Import the service instance (it's already instantiated)
    dailyRewardService = require('../services/dailyRewardService').default;
  });

  describe('getDailyRewardStatus', () => {
    it('should return correct status for user with no streak', async () => {
      // Mock no existing streak
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' } // No rows found
      });

      // Mock rewards data
      mockSupabase.order.mockReturnThis();
      mockSupabase.single.mockResolvedValueOnce({
        data: [
          { id: 'reward1', requirements: { day: 1 }, points: 100, diamond_value: 50 },
          { id: 'reward2', requirements: { day: 2 }, points: 150, diamond_value: 75 }
        ],
        error: null
      });

      // Mock no claimed rewards today
      mockSupabase.gte.mockReturnThis();
      mockSupabase.lt.mockReturnThis();
      mockSupabase.single.mockResolvedValueOnce({
        data: [],
        error: null
      });

      const result = await dailyRewardService.getDailyRewardStatus('user123');

      expect(result.streak.current_streak).toBe(0);
      expect(result.hasClaimedToday).toBe(false);
      expect(result.rewards).toHaveLength(2);
    });

    it('should return correct status for user with existing streak', async () => {
      const yesterday = new Date();
      yesterday.setUTCDate(yesterday.getUTCDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      // Mock existing streak from yesterday
      mockSupabase.single.mockResolvedValueOnce({
        data: {
          id: 'streak1',
          user_id: 'user123',
          current_streak: 3,
          longest_streak: 5,
          last_login_date: yesterdayStr
        },
        error: null
      });

      // Mock rewards data
      mockSupabase.order.mockReturnThis();
      mockSupabase.single.mockResolvedValueOnce({
        data: [
          { id: 'reward1', requirements: { day: 1 }, points: 100 },
          { id: 'reward2', requirements: { day: 2 }, points: 150 }
        ],
        error: null
      });

      // Mock no claimed rewards today
      mockSupabase.gte.mockReturnThis();
      mockSupabase.lt.mockReturnThis();
      mockSupabase.single.mockResolvedValueOnce({
        data: [],
        error: null
      });

      const result = await dailyRewardService.getDailyRewardStatus('user123');

      expect(result.streak.current_streak).toBe(3);
      expect(result.hasClaimedToday).toBe(false);
    });
  });

  describe('claimDailyReward - Streak Calculation Fixes', () => {
    beforeEach(() => {
      // Mock transaction helper
      const { supabaseHelpers } = require('../../../utils/supabaseHelpers');
      supabaseHelpers.transaction.mockImplementation(async (callback: any) => {
        return await callback();
      });
    });

    it('should correctly continue streak when user logged in yesterday', async () => {
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date();
      yesterday.setUTCDate(yesterday.getUTCDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      // Mock existing streak from yesterday
      mockSupabase.single
        .mockResolvedValueOnce({
          data: {
            id: 'streak1',
            current_streak: 3,
            longest_streak: 5,
            last_login_date: yesterdayStr
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: [],
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            id: 'reward1',
            points: 100,
            diamond_value: 50,
            requirements: { day: 4 }
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: { id: 'user_reward1' },
          error: null
        });

      mockSupabase.update.mockResolvedValue({ error: null });
      mockSupabase.insert.mockResolvedValue({ error: null });

      const rewardsService = require('../services/rewardsService').default;
      const loyaltyService = require('../services/loyaltyService').loyaltyService;
      const walletService = require('../../wallet/services/walletService').walletService;

      rewardsService.createRewardTransaction.mockResolvedValue({});
      loyaltyService.awardLoyaltyPoints.mockResolvedValue({});
      walletService.addDiamonds.mockResolvedValue({});

      const result = await dailyRewardService.claimDailyReward('user123', 'test-key');

      expect(result.streak.current_streak).toBe(4); // Should increment from 3 to 4
      expect(result.streak.longest_streak).toBe(5); // Should remain 5
    });

    it('should maintain streak when user already logged in today', async () => {
      const today = new Date().toISOString().split('T')[0];

      // Mock existing streak from today (user already logged in)
      mockSupabase.single
        .mockResolvedValueOnce({
          data: {
            id: 'streak1',
            current_streak: 3,
            longest_streak: 5,
            last_login_date: today
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: [],
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            id: 'reward1',
            points: 100,
            diamond_value: 50,
            requirements: { day: 3 }
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: { id: 'user_reward1' },
          error: null
        });

      mockSupabase.update.mockResolvedValue({ error: null });

      const rewardsService = require('../services/rewardsService').default;
      const loyaltyService = require('../services/loyaltyService').loyaltyService;
      const walletService = require('../../wallet/services/walletService').walletService;

      rewardsService.createRewardTransaction.mockResolvedValue({});
      loyaltyService.awardLoyaltyPoints.mockResolvedValue({});
      walletService.addDiamonds.mockResolvedValue({});

      const result = await dailyRewardService.claimDailyReward('user123', 'test-key');

      expect(result.streak.current_streak).toBe(3); // Should maintain current streak
      expect(result.streak.longest_streak).toBe(5); // Should remain 5
    });

    it('should reset streak when user missed days', async () => {
      const today = new Date().toISOString().split('T')[0];
      const twoDaysAgo = new Date();
      twoDaysAgo.setUTCDate(twoDaysAgo.getUTCDate() - 2);
      const twoDaysAgoStr = twoDaysAgo.toISOString().split('T')[0];

      // Mock existing streak from two days ago (missed yesterday)
      mockSupabase.single
        .mockResolvedValueOnce({
          data: {
            id: 'streak1',
            current_streak: 3,
            longest_streak: 5,
            last_login_date: twoDaysAgoStr
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: [],
          error: null
        })
        .mockResolvedValueOnce({
          data: {
            id: 'reward1',
            points: 100,
            diamond_value: 50,
            requirements: { day: 1 }
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: { id: 'user_reward1' },
          error: null
        });

      mockSupabase.update.mockResolvedValue({ error: null });

      const rewardsService = require('../services/rewardsService').default;
      const loyaltyService = require('../services/loyaltyService').loyaltyService;
      const walletService = require('../../wallet/services/walletService').walletService;

      rewardsService.createRewardTransaction.mockResolvedValue({});
      loyaltyService.awardLoyaltyPoints.mockResolvedValue({});
      walletService.addDiamonds.mockResolvedValue({});

      const result = await dailyRewardService.claimDailyReward('user123', 'test-key');

      expect(result.streak.current_streak).toBe(1); // Should reset to 1
      expect(result.streak.longest_streak).toBe(5); // Should remain 5
    });

    it('should throw error when reward already claimed today', async () => {
      const today = new Date().toISOString().split('T')[0];

      // Mock existing streak
      mockSupabase.single.mockResolvedValueOnce({
        data: {
          id: 'streak1',
          current_streak: 3,
          longest_streak: 5,
          last_login_date: today
        },
        error: null
      });

      // Mock already claimed today
      mockSupabase.gte.mockReturnThis();
      mockSupabase.lt.mockReturnThis();
      mockSupabase.single.mockResolvedValueOnce({
        data: [{ id: 'claimed_reward' }],
        error: null
      });

      await expect(
        dailyRewardService.claimDailyReward('user123', 'test-key')
      ).rejects.toThrow('Daily reward already claimed today');
    });

    it('should use fallback reward when no reward found for current streak', async () => {
      const today = new Date().toISOString().split('T')[0];

      // Mock existing streak with high number
      mockSupabase.single
        .mockResolvedValueOnce({
          data: {
            id: 'streak1',
            current_streak: 100, // Very high streak
            longest_streak: 100,
            last_login_date: today
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: [],
          error: null
        })
        .mockResolvedValueOnce({
          data: null,
          error: { message: 'No reward found' }
        })
        .mockResolvedValueOnce({
          data: {
            id: 'fallback_reward',
            points: 500,
            diamond_value: 250,
            requirements: { day: 7 }
          },
          error: null
        })
        .mockResolvedValueOnce({
          data: { id: 'user_reward1' },
          error: null
        });

      mockSupabase.update.mockResolvedValue({ error: null });

      const rewardsService = require('../services/rewardsService').default;
      const loyaltyService = require('../services/loyaltyService').loyaltyService;
      const walletService = require('../../wallet/services/walletService').walletService;

      rewardsService.createRewardTransaction.mockResolvedValue({});
      loyaltyService.awardLoyaltyPoints.mockResolvedValue({});
      walletService.addDiamonds.mockResolvedValue({});

      const result = await dailyRewardService.claimDailyReward('user123', 'test-key');

      expect(result.reward.id).toBe('fallback_reward');
      expect(result.streak.current_streak).toBe(100);
    });
  });
});
