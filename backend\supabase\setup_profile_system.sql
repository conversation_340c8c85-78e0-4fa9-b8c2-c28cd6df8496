-- Complete Profile System Setup for WiggyZ
-- This script ensures all necessary database fields, storage buckets, and RLS policies are in place

-- =====================================================
-- 1. ENSURE ALL PROFILE FIELDS EXIST IN USERS TABLE
-- =====================================================

-- Add missing Free Fire fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS ff_rank VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS ff_preferred_mode VARCHAR(50);

-- Add PUBG-specific fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_name VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_uid VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_level INTEGER DEFAULT 1;
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_server VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_rank VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_preferred_mode VARCHAR(50);

-- Add personal information fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(15);
ALTER TABLE users ADD COLUMN IF NOT EXISTS bio TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS location VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS date_of_birth DATE;

-- Add profile image fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_image TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_image_url TEXT;

-- =====================================================
-- 2. CREATE STORAGE BUCKET FOR PROFILE IMAGES
-- =====================================================

-- Create profile-images bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('profile-images', 'profile-images', true)
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 3. SET UP RLS POLICIES FOR PROFILE IMAGES STORAGE
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated users to upload profile images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access to profile images" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to update their own profile images" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to delete their own profile images" ON storage.objects;

-- Allow authenticated users to upload profile images
CREATE POLICY "Allow authenticated users to upload profile images" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (
  bucket_id = 'profile-images' AND
  name ~ '^profile_.*\.(jpg|jpeg|png|webp)$'
);

-- Allow public read access to profile images
CREATE POLICY "Allow public read access to profile images" ON storage.objects
FOR SELECT TO public
USING (bucket_id = 'profile-images');

-- Allow users to update their own profile images
CREATE POLICY "Allow users to update their own profile images" ON storage.objects
FOR UPDATE TO authenticated
USING (
  bucket_id = 'profile-images' AND
  name ~ ('^profile_' || auth.uid()::text || '_.*\.(jpg|jpeg|png|webp)$')
)
WITH CHECK (
  bucket_id = 'profile-images' AND
  name ~ ('^profile_' || auth.uid()::text || '_.*\.(jpg|jpeg|png|webp)$')
);

-- Allow users to delete their own profile images
CREATE POLICY "Allow users to delete their own profile images" ON storage.objects
FOR DELETE TO authenticated
USING (
  bucket_id = 'profile-images' AND
  name ~ ('^profile_' || auth.uid()::text || '_.*\.(jpg|jpeg|png|webp)$')
);

-- =====================================================
-- 4. UPDATE USERS TABLE RLS POLICIES
-- =====================================================

-- Ensure RLS is enabled
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own data" ON users;
DROP POLICY IF EXISTS "Users can update their own data" ON users;
DROP POLICY IF EXISTS "Admins can view all user data" ON users;
DROP POLICY IF EXISTS "Admins can update all user data" ON users;

-- Allow users to view their own profile data
CREATE POLICY "Users can view their own data" ON users
FOR SELECT TO authenticated
USING (auth.uid() = id);

-- Allow users to update their own profile data
CREATE POLICY "Users can update their own data" ON users
FOR UPDATE TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Allow admins to view all user data
CREATE POLICY "Admins can view all user data" ON users
FOR SELECT TO authenticated
USING (
  auth.jwt() ->> 'role' IN ('admin', 'manager', 'superadmin')
);

-- Allow admins to update all user data
CREATE POLICY "Admins can update all user data" ON users
FOR UPDATE TO authenticated
USING (
  auth.jwt() ->> 'role' IN ('admin', 'manager', 'superadmin')
)
WITH CHECK (
  auth.jwt() ->> 'role' IN ('admin', 'manager', 'superadmin')
);

-- =====================================================
-- 5. CREATE INDEXES FOR BETTER PERFORMANCE
-- =====================================================

-- Create indexes for game profile fields
CREATE INDEX IF NOT EXISTS idx_users_ff_name ON users(ff_name);
CREATE INDEX IF NOT EXISTS idx_users_ff_uid ON users(ff_uid);
CREATE INDEX IF NOT EXISTS idx_users_pubg_name ON users(pubg_name);
CREATE INDEX IF NOT EXISTS idx_users_pubg_uid ON users(pubg_uid);
CREATE INDEX IF NOT EXISTS idx_users_profile_image ON users(profile_image);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);

-- =====================================================
-- 6. ADD HELPFUL COMMENTS
-- =====================================================

-- Add comments for documentation
COMMENT ON COLUMN users.ff_rank IS 'Free Fire player rank/tier (Bronze, Silver, Gold, Platinum, Diamond, Master, Grandmaster, Heroic)';
COMMENT ON COLUMN users.ff_preferred_mode IS 'Free Fire preferred game mode (Solo, Duo, Squad, Ranked, Casual)';
COMMENT ON COLUMN users.pubg_name IS 'PUBG Mobile player username';
COMMENT ON COLUMN users.pubg_uid IS 'PUBG Mobile player unique identifier';
COMMENT ON COLUMN users.pubg_level IS 'PUBG Mobile player level';
COMMENT ON COLUMN users.pubg_server IS 'PUBG Mobile server region';
COMMENT ON COLUMN users.pubg_rank IS 'PUBG Mobile player rank/tier (Bronze, Silver, Gold, Platinum, Diamond, Crown, Ace, Conqueror)';
COMMENT ON COLUMN users.pubg_preferred_mode IS 'PUBG Mobile preferred game mode (Solo, Duo, Squad, Ranked, Casual)';
COMMENT ON COLUMN users.profile_image_url IS 'URL to user profile image stored in Supabase storage';
COMMENT ON COLUMN users.bio IS 'User biography/description';
COMMENT ON COLUMN users.location IS 'User location/address';
COMMENT ON COLUMN users.phone IS 'User phone number';

-- =====================================================
-- 7. VERIFY SETUP
-- =====================================================

-- Check if all columns exist
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'users' 
  AND table_schema = 'public'
  AND column_name IN (
    'ff_rank', 'ff_preferred_mode', 'pubg_name', 'pubg_uid', 'pubg_level', 
    'pubg_server', 'pubg_rank', 'pubg_preferred_mode', 'phone', 'bio', 
    'location', 'date_of_birth', 'profile_image', 'profile_image_url'
  )
ORDER BY column_name;

-- Check if storage bucket exists
SELECT id, name, public FROM storage.buckets WHERE id = 'profile-images';

-- Success message
SELECT 'Profile system setup completed successfully!' as status;
