/**
 * Complete Payment Flow Test Script
 * Tests the entire payment process from initiation to verification
 */

require('dotenv').config();
const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:5000/api/v1';

// Mock user token for testing (you'll need a real token for actual testing)
const MOCK_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.test';

async function testPaymentFlow() {
  console.log('🧪 Testing Complete Payment Flow\n');

  // Test 1: Health Check
  console.log('📡 Test 1: Health Check');
  try {
    const response = await axios.get('http://127.0.0.1:5000/health');
    console.log('✅ Health check passed');
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${JSON.stringify(response.data)}\n`);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return;
  }

  // Test 2: Payment Initiation (without auth - should fail)
  console.log('📡 Test 2: Payment Initiation (No Auth)');
  try {
    const response = await axios.post(`${BASE_URL}/wallet/topup`, {
      amount: 100,
      currency: 'INR',
      payment_method: 'upi'
    }, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      }
    });
    console.log('❌ Unexpected success - should have failed without auth');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Correctly rejected without authentication');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data)}\n`);
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }

  // Test 3: Payment Initiation (with mock auth - should fail gracefully)
  console.log('📡 Test 3: Payment Initiation (Mock Auth)');
  try {
    const response = await axios.post(`${BASE_URL}/wallet/topup`, {
      amount: 100,
      currency: 'INR',
      payment_method: 'upi'
    }, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': `Bearer ${MOCK_TOKEN}`
      }
    });
    console.log('❌ Unexpected success with mock token');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Correctly rejected mock authentication');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data)}\n`);
    } else {
      console.log('❌ Unexpected error:', error.message);
      console.log(`   Status: ${error.response?.status}`);
      console.log(`   Response: ${JSON.stringify(error.response?.data)}\n`);
    }
  }

  // Test 4: Test validation with invalid data
  console.log('📡 Test 4: Validation Test (Invalid Amount)');
  try {
    const response = await axios.post(`${BASE_URL}/wallet/topup`, {
      amount: -100, // Invalid negative amount
      currency: 'INR',
      payment_method: 'upi'
    }, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': `Bearer ${MOCK_TOKEN}`
      }
    });
    console.log('❌ Should have failed validation');
  } catch (error) {
    if (error.response?.status === 400 || error.response?.status === 401) {
      console.log('✅ Validation working correctly');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data)}\n`);
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }

  // Test 5: Test with valid data structure
  console.log('📡 Test 5: Valid Data Structure Test');
  try {
    const response = await axios.post(`${BASE_URL}/wallet/topup`, {
      amount: 100,
      currency: 'INR',
      payment_method: 'upi',
      amountInPaise: 10000 // This should now be allowed
    }, {
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': `Bearer ${MOCK_TOKEN}`
      }
    });
    console.log('❌ Should have failed auth but passed validation');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Data structure validation passed, auth failed as expected');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data)}\n`);
    } else if (error.response?.status === 400) {
      console.log('⚠️  Validation issue detected:');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data)}\n`);
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }

  console.log('📝 Test Summary:');
  console.log('1. ✅ Health check should pass');
  console.log('2. ✅ No auth should return 401');
  console.log('3. ✅ Mock auth should return 401');
  console.log('4. ✅ Invalid data should return 400 or 401');
  console.log('5. ✅ Valid structure should pass validation (fail on auth)');
  
  console.log('\n🎯 Next Steps:');
  console.log('1. If all tests show expected results, the backend is ready');
  console.log('2. Test with real authentication token in Flutter app');
  console.log('3. Monitor backend logs during Flutter payment testing');
  console.log('4. Use test card: 4111 1111 1111 1111');
}

// Run the test
testPaymentFlow().catch(console.error);
