/**
 * Test script to verify withdrawal API endpoints
 * Run with: node test-withdrawal-api.js
 */

const API_BASE_URL = 'http://127.0.0.1:8080/api/v1';

async function testApiConnection() {
  console.log('🔄 Testing API connection...');
  
  try {
    // Test basic connectivity
    const response = await fetch(`${API_BASE_URL}/auth/ping`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`📊 Ping response status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.text();
      console.log('✅ API is accessible:', data);
    } else {
      console.log('❌ API ping failed');
    }
  } catch (error) {
    console.error('❌ API connection failed:', error.message);
    console.log('💡 Make sure the backend server is running on port 8080');
  }
}

async function testWithdrawalEndpoints() {
  console.log('\n🔄 Testing withdrawal endpoints (without auth)...');
  
  try {
    // Test withdrawal stats endpoint (should fail with 401)
    const statsResponse = await fetch(`${API_BASE_URL}/admin/withdrawals/stats`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`📊 Withdrawal stats response status: ${statsResponse.status}`);
    
    if (statsResponse.status === 401) {
      console.log('✅ Withdrawal endpoints are accessible (401 expected without auth)');
    } else {
      const errorText = await statsResponse.text();
      console.log('❌ Unexpected response:', errorText);
    }
  } catch (error) {
    console.error('❌ Withdrawal endpoint test failed:', error.message);
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting API tests...\n');
  
  await testApiConnection();
  await testWithdrawalEndpoints();
  
  console.log('\n✅ Tests completed');
  console.log('\n💡 If API connection failed:');
  console.log('   1. Make sure backend server is running: cd backend && npm start');
  console.log('   2. Check if server is running on port 8080');
  console.log('   3. Check for any firewall or network issues');
}

runTests().catch(console.error);
