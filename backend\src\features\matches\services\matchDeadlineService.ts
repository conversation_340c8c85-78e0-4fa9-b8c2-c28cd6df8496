/**
 * Match Deadline Service
 * 
 * Handles automated result finalization when submission deadlines expire.
 * Includes Redis-based distributed locking for safe concurrent processing.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { supabase as sb } from '../../../config/supabase';
import { getRedisClient, isRedisConnectionAvailable } from '../../../config/redis';
import { Match, MatchParticipant } from '../types/matchTypes';
import { matchNotificationService } from './matchNotificationService';
import { matchAuditService } from './matchAuditService';
import { walletService, TransactionType } from '../../wallet/services/walletService';
import { logger } from '../../../utils/logger';

export interface ExpiredMatch {
  match_id: string;
  deadline: string;
  participants_submitted: number;
  total_participants: number;
  match_status: string;
}

export interface MatchAuditLogEntry {
  match_id: string;
  action_type: 'auto_finalized' | 'deadline_expired' | 'participant_defaulted' | 'auto_winner_assigned' | 'auto_loser_assigned' | 'match_auto_completed' | 'creator_refunded';
  triggered_by: 'system' | 'cron_job';
  affected_participants: string[];
  action_details: any;
  reason: string;
  deadline_time?: string;
  participants_submitted: number;
  participants_defaulted: number;
  total_participants: number;
}

export class MatchDeadlineService {
  private supabase: SupabaseClient;
  private redis: any;
  private isProcessing: boolean = false;
  private processingInterval: NodeJS.Timeout | null = null;
  
  // Configuration
  private readonly LOCK_KEY = 'match_deadline_processor_lock';
  private readonly LOCK_TIMEOUT = 4 * 60 * 1000; // 4 minutes
  private readonly PROCESSING_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private readonly BATCH_SIZE = 50;
  private readonly MAX_RETRIES = 3;

  constructor() {
    this.supabase = sb;
    this.redis = isRedisConnectionAvailable() ? getRedisClient() : null;
  }

  /**
   * Start the background deadline monitoring service
   */
  public startDeadlineMonitoring(): void {
    if (this.processingInterval) {
      logger.warn('Deadline monitoring is already running');
      return;
    }

    logger.info('Starting match deadline monitoring service');
    
    // Run immediately, then on interval
    this.processExpiredMatches();
    
    this.processingInterval = setInterval(() => {
      this.processExpiredMatches();
    }, this.PROCESSING_INTERVAL);

    logger.info(`Deadline monitoring started with ${this.PROCESSING_INTERVAL / 1000}s interval`);
  }

  /**
   * Stop the background deadline monitoring service
   */
  public stopDeadlineMonitoring(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      logger.info('Match deadline monitoring service stopped');
    }
  }

  /**
   * Process expired matches with distributed locking
   */
  private async processExpiredMatches(): Promise<void> {
    if (this.isProcessing) {
      logger.debug('Deadline processing already in progress, skipping');
      return;
    }

    let lockAcquired = false;
    
    try {
      // Acquire distributed lock
      lockAcquired = await this.acquireLock();
      if (!lockAcquired) {
        logger.debug('Could not acquire lock, another instance is processing');
        return;
      }

      this.isProcessing = true;
      logger.info('Starting expired match processing');

      // Get expired matches
      const expiredMatches = await this.getExpiredMatches();
      
      if (expiredMatches.length === 0) {
        logger.debug('No expired matches found');
        return;
      }

      logger.info(`Found ${expiredMatches.length} expired matches to process`);

      // Process each expired match
      let processedCount = 0;
      let errorCount = 0;

      for (const expiredMatch of expiredMatches.slice(0, this.BATCH_SIZE)) {
        try {
          await this.finalizeExpiredMatch(expiredMatch);
          processedCount++;
          logger.info(`Successfully finalized match ${expiredMatch.match_id}`);
        } catch (error) {
          errorCount++;
          logger.error(`Failed to finalize match ${expiredMatch.match_id}: ` + (error instanceof Error ? error.message : String(error)));
          
          // Log the error to audit trail
          await this.logAuditEntry({
            match_id: expiredMatch.match_id,
            action_type: 'auto_finalized',
            triggered_by: 'cron_job',
            affected_participants: [],
            action_details: { error: error instanceof Error ? error.message : String(error) },
            reason: 'Finalization failed due to system error',
            deadline_time: expiredMatch.deadline,
            participants_submitted: expiredMatch.participants_submitted,
            participants_defaulted: 0,
            total_participants: expiredMatch.total_participants
          });
        }
      }

      logger.info(`Deadline processing completed: ${processedCount} processed, ${errorCount} errors`);

    } catch (error) {
      logger.error('Error in deadline processing: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      this.isProcessing = false;
      
      if (lockAcquired) {
        await this.releaseLock();
      }
    }
  }

  /**
   * Get matches that have expired deadlines and need finalization
   */
  private async getExpiredMatches(): Promise<ExpiredMatch[]> {
    try {
      const { data, error } = await this.supabase
        .rpc('get_expired_matches_for_finalization');

      if (error) {
        throw new Error(`Failed to get expired matches: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      logger.error('Error getting expired matches: ' + (error instanceof Error ? error.message : String(error)));
      throw error;
    }
  }

  /**
   * Finalize an expired match with atomic transaction
   */
  private async finalizeExpiredMatch(expiredMatch: ExpiredMatch): Promise<void> {
    const { match_id, participants_submitted, total_participants } = expiredMatch;
    
    try {
      // Start transaction by getting match details with participants
      const { data: match, error: matchError } = await this.supabase
        .from('matches')
        .select(`
          *,
          match_participants (
            id,
            user_id,
            participant_type,
            result_submitted_at,
            result_score,
            result_position,
            is_winner
          )
        `)
        .eq('id', match_id)
        .single();

      if (matchError || !match) {
        throw new Error(`Failed to get match details: ${matchError?.message}`);
      }

      const participants = match.match_participants?.filter((p: any) => p.participant_type === 'player') || [];
      const submittedParticipants = participants.filter((p: any) => p.result_submitted_at);
      const nonSubmittedParticipants = participants.filter((p: any) => !p.result_submitted_at);

      logger.info(`Finalizing match ${match_id}: ${submittedParticipants.length}/${participants.length} submitted`);

      // Check for refund eligibility before processing match finalization
      await this.processCreatorRefundIfEligible(match, participants);

      // Handle different scenarios
      if (submittedParticipants.length === 0) {
        // No one submitted - all are losers
        await this.handleNoSubmissions(match, participants);
      } else if (submittedParticipants.length === participants.length) {
        // All submitted - should have been handled already, but mark as completed
        await this.handleAllSubmitted(match);
      } else {
        // Partial submissions - most common case
        await this.handlePartialSubmissions(match, submittedParticipants, nonSubmittedParticipants);
      }

      // Mark match as auto-finalized and completed
      await this.supabase
        .from('matches')
        .update({
          status: 'completed',
          auto_finalized: true,
          auto_finalized_at: new Date().toISOString(),
          auto_finalized_reason: `Deadline expired: ${submittedParticipants.length}/${participants.length} submitted`,
          finalized_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', match_id);

      // Update deadline status
      await this.supabase
        .from('match_deadline_status')
        .update({
          deadline_expired: true,
          auto_finalization_completed: true,
          finalized_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('match_id', match_id);

      // Send notifications to all participants
      await this.sendFinalizationNotifications(match, submittedParticipants, nonSubmittedParticipants);

      logger.info(`Successfully finalized match ${match_id}`);

    } catch (error) {
      logger.error(`Error finalizing match ${match_id}: ` + (error instanceof Error ? error.message : String(error)));
      
      // Update deadline status with error
      await this.supabase
        .from('match_deadline_status')
        .update({
          auto_finalization_attempted: true,
          auto_finalization_error: error instanceof Error ? error.message : String(error),
          updated_at: new Date().toISOString()
        })
        .eq('match_id', match_id);
      
      throw error;
    }
  }

  /**
   * Handle case where no participants submitted results
   */
  private async handleNoSubmissions(match: any, participants: any[]): Promise<void> {
    // Mark all participants as losers
    const participantIds = participants.map(p => p.user_id);
    
    await this.supabase
      .from('match_participants')
      .update({
        is_winner: false,
        auto_assigned_result: true,
        auto_assignment_reason: 'No result submitted by deadline'
      })
      .in('id', participants.map(p => p.id));

    // Log audit entry
    await this.logAuditEntry({
      match_id: match.id,
      action_type: 'auto_finalized',
      triggered_by: 'cron_job',
      affected_participants: participantIds,
      action_details: { scenario: 'no_submissions', all_marked_as_losers: true },
      reason: 'No participants submitted results by deadline',
      deadline_time: match.result_submission_deadline,
      participants_submitted: 0,
      participants_defaulted: participants.length,
      total_participants: participants.length
    });
  }

  /**
   * Handle case where all participants submitted results
   */
  private async handleAllSubmitted(match: any): Promise<void> {
    // This should have been handled by normal flow, just log
    await this.logAuditEntry({
      match_id: match.id,
      action_type: 'match_auto_completed',
      triggered_by: 'cron_job',
      affected_participants: [],
      action_details: { scenario: 'all_submitted', note: 'Match should have been completed already' },
      reason: 'All participants submitted results, completing via deadline service',
      deadline_time: match.result_submission_deadline,
      participants_submitted: match.match_participants?.length || 0,
      participants_defaulted: 0,
      total_participants: match.match_participants?.length || 0
    });
  }

  /**
   * Handle case where some participants submitted results
   */
  private async handlePartialSubmissions(match: any, submitted: any[], nonSubmitted: any[]): Promise<void> {
    // Mark non-submitted participants as losers
    const nonSubmittedIds = nonSubmitted.map(p => p.user_id);
    
    await this.supabase
      .from('match_participants')
      .update({
        is_winner: false,
        auto_assigned_result: true,
        auto_assignment_reason: 'No result submitted by deadline'
      })
      .in('id', nonSubmitted.map(p => p.id));

    // Determine winner from submitted participants
    const winner = this.determineWinner(submitted);
    
    if (winner) {
      // Mark winner
      await this.supabase
        .from('match_participants')
        .update({ is_winner: true })
        .eq('id', winner.id);

      // Mark other submitted participants as losers
      const otherSubmitted = submitted.filter(p => p.id !== winner.id);
      if (otherSubmitted.length > 0) {
        await this.supabase
          .from('match_participants')
          .update({ is_winner: false })
          .in('id', otherSubmitted.map(p => p.id));
      }
    }

    // Log audit entry
    await this.logAuditEntry({
      match_id: match.id,
      action_type: 'auto_finalized',
      triggered_by: 'cron_job',
      affected_participants: [...nonSubmittedIds, ...(winner ? [winner.user_id] : [])],
      action_details: { 
        scenario: 'partial_submissions',
        winner_id: winner?.user_id,
        defaulted_participants: nonSubmittedIds
      },
      reason: `Partial submissions: ${submitted.length}/${submitted.length + nonSubmitted.length} submitted`,
      deadline_time: match.result_submission_deadline,
      participants_submitted: submitted.length,
      participants_defaulted: nonSubmitted.length,
      total_participants: submitted.length + nonSubmitted.length
    });
  }

  /**
   * Determine winner from submitted participants
   */
  private determineWinner(participants: any[]): any | null {
    if (participants.length === 0) return null;
    if (participants.length === 1) return participants[0];

    // For position-based games (lower position = better)
    const hasPositions = participants.some(p => p.result_position != null);
    if (hasPositions) {
      return participants.reduce((best, current) => {
        if (current.result_position == null) return best;
        if (best.result_position == null) return current;
        return current.result_position < best.result_position ? current : best;
      });
    }

    // For score-based games (higher score = better)
    const hasScores = participants.some(p => p.result_score != null);
    if (hasScores) {
      return participants.reduce((best, current) => {
        if (current.result_score == null) return best;
        if (best.result_score == null) return current;
        return current.result_score > best.result_score ? current : best;
      });
    }

    // Fallback: first submitted wins
    return participants.reduce((earliest, current) => {
      const earliestTime = new Date(earliest.result_submitted_at).getTime();
      const currentTime = new Date(current.result_submitted_at).getTime();
      return currentTime < earliestTime ? current : earliest;
    });
  }

  /**
   * Send notifications to participants about match finalization
   */
  private async sendFinalizationNotifications(match: any, submitted: any[], nonSubmitted: any[]): Promise<void> {
    try {
      logger.info(`Sending finalization notifications for match ${match.id}`);

      // Send automated finalization notifications
      await matchNotificationService.notifyMatchAutoFinalized(match, submitted, nonSubmitted);

    } catch (error) {
      logger.error(`Error sending finalization notifications for match ${match.id}: ` + (error instanceof Error ? error.message : String(error)));
      // Don't throw - notifications are not critical for finalization
    }
  }

  /**
   * Log audit entry for tracking automated actions
   */
  private async logAuditEntry(entry: MatchAuditLogEntry): Promise<void> {
    try {
      await matchAuditService.logAction({
        match_id: entry.match_id,
        action_type: entry.action_type,
        triggered_by: entry.triggered_by,
        affected_participants: entry.affected_participants,
        action_details: entry.action_details,
        reason: entry.reason,
        deadline_time: entry.deadline_time,
        participants_submitted: entry.participants_submitted,
        participants_defaulted: entry.participants_defaulted,
        total_participants: entry.total_participants
      });
    } catch (error) {
      logger.error('Error logging audit entry: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * Acquire distributed lock using Redis
   */
  private async acquireLock(): Promise<boolean> {
    if (!this.redis) {
      // No Redis available, use in-memory flag (single instance only)
      return !this.isProcessing;
    }

    try {
      const result = await this.redis.set(
        this.LOCK_KEY,
        Date.now().toString(),
        'PX',
        this.LOCK_TIMEOUT,
        'NX'
      );
      return result === 'OK';
    } catch (error) {
      logger.error('Error acquiring Redis lock: ' + (error instanceof Error ? error.message : String(error)));
      return false;
    }
  }

  /**
   * Release distributed lock
   */
  private async releaseLock(): Promise<void> {
    if (!this.redis) return;

    try {
      await this.redis.del(this.LOCK_KEY);
    } catch (error) {
      logger.error('Error releasing Redis lock: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * Get deadline status for a specific match
   */
  public async getMatchDeadlineStatus(matchId: string): Promise<any> {
    try {
      const { data, error } = await this.supabase
        .from('match_deadline_status')
        .select('*')
        .eq('match_id', matchId)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found is OK
        throw new Error(`Failed to get deadline status: ${error.message}`);
      }

      return data;
    } catch (error) {
      logger.error(`Error getting deadline status for match ${matchId}: ` + (error instanceof Error ? error.message : String(error)));
      throw error;
    }
  }

  /**
   * Manual trigger for processing expired matches (admin use)
   */
  public async manualProcessExpiredMatches(): Promise<{ processed: number; errors: number }> {
    logger.info('Manual trigger for expired match processing');
    
    const expiredMatches = await this.getExpiredMatches();
    let processedCount = 0;
    let errorCount = 0;

    for (const expiredMatch of expiredMatches) {
      try {
        await this.finalizeExpiredMatch(expiredMatch);
        processedCount++;
      } catch (error) {
        errorCount++;
        logger.error(`Failed to manually finalize match ${expiredMatch.match_id}: ` + (error instanceof Error ? error.message : String(error)));
      }
    }

    return { processed: processedCount, errors: errorCount };
  }

  /**
   * Process creator refund if match is eligible (no participants joined and has entry fee)
   */
  private async processCreatorRefundIfEligible(match: any, participants: any[]): Promise<void> {
    try {
      // Check refund eligibility conditions
      const hasEntryFee = match.entry_fee && match.entry_fee > 0;
      const noParticipantsJoined = participants.filter(p => p.user_id !== match.created_by).length === 0;
      const refundNotProcessed = !match.refund_processed;

      if (!hasEntryFee || !noParticipantsJoined || !refundNotProcessed) {
        logger.info(`Match ${match.id} not eligible for creator refund: hasEntryFee=${hasEntryFee}, noParticipants=${noParticipantsJoined}, refundNotProcessed=${refundNotProcessed}`);
        return;
      }

      logger.info(`Processing creator refund for match ${match.id}: amount=${match.entry_fee}`);

      // Use the database function to process refund atomically
      const { data: refundResult, error: refundError } = await this.supabase
        .rpc('process_creator_refund', { match_id_param: match.id });

      if (refundError) {
        throw new Error(`Database refund function failed: ${refundError.message}`);
      }

      if (!refundResult?.success) {
        throw new Error(`Refund processing failed: ${refundResult?.error || 'Unknown error'}`);
      }

      // Process wallet refund using the wallet service
      await walletService.processRefund(
        match.created_by,
        match.entry_fee,
        `Match entry fee refund - Match expired with no participants`,
        { match_id: match.id, refund_reason: 'No participants joined before deadline' }
      );

      // Log audit entry
      await this.logAuditEntry({
        match_id: match.id,
        action_type: 'creator_refunded',
        triggered_by: 'cron_job',
        affected_participants: [match.created_by],
        action_details: {
          refund_amount: match.entry_fee,
          refund_reason: 'No participants joined before deadline'
        },
        reason: 'Creator refund processed for expired match with no participants',
        deadline_time: match.result_submission_deadline,
        participants_submitted: 0,
        participants_defaulted: 0,
        total_participants: participants.length
      });

      // Send refund notification to creator
      await matchNotificationService.notifyRefundProcessed(match, match.entry_fee);

      logger.info(`Successfully processed creator refund for match ${match.id}: ${match.entry_fee} refunded to user ${match.created_by}`);

    } catch (error) {
      logger.error(`Failed to process creator refund for match ${match.id}: ${error instanceof Error ? error.message : String(error)}`);
      // Don't throw error to prevent blocking match finalization
      // Log for manual review
      logger.security(`MANUAL REVIEW REQUIRED: Failed to process creator refund for match ${match.id}, user ${match.created_by}, amount ${match.entry_fee}`);
    }
  }
}

// Export singleton instance
export const matchDeadlineService = new MatchDeadlineService();
