/**
 * Global Setup for Support System Tests
 * Prepares test environment and creates necessary test data
 */

import { chromium, FullConfig } from '@playwright/test';
import { createClient } from '@supabase/supabase-js';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Setting up Support System tests...');

  // Initialize Supabase client
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseServiceKey) {
    console.warn('⚠️  SUPABASE_SERVICE_ROLE_KEY not found. Some test setup may fail.');
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Create test users if they don't exist
    await createTestUsers(supabase);
    
    // Create test FAQ data
    await createTestFaqData(supabase);
    
    // Clean up any existing test data
    await cleanupTestData(supabase);
    
    console.log('✅ Support System test setup completed successfully');
  } catch (error) {
    console.error('❌ Support System test setup failed:', error);
    throw error;
  }
}

async function createTestUsers(supabase: any) {
  console.log('👥 Creating test users...');

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'testpassword123',
      name: 'Test User',
      role: 'user'
    },
    {
      email: '<EMAIL>', 
      password: 'adminpassword123',
      name: 'Admin User',
      role: 'admin'
    }
  ];

  for (const user of testUsers) {
    try {
      // Check if user already exists
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', user.email)
        .single();

      if (!existingUser) {
        // Create auth user
        const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
          email: user.email,
          password: user.password,
          email_confirm: true
        });

        if (authError) {
          console.warn(`⚠️  Could not create auth user ${user.email}:`, authError.message);
          continue;
        }

        // Create user profile
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: authUser.user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            created_at: new Date().toISOString()
          });

        if (profileError) {
          console.warn(`⚠️  Could not create user profile ${user.email}:`, profileError.message);
        } else {
          console.log(`✅ Created test user: ${user.email}`);
        }
      } else {
        console.log(`ℹ️  Test user already exists: ${user.email}`);
      }
    } catch (error) {
      console.warn(`⚠️  Error creating user ${user.email}:`, error);
    }
  }
}

async function createTestFaqData(supabase: any) {
  console.log('❓ Creating test FAQ data...');

  try {
    // Check if FAQ categories exist
    const { data: existingCategories } = await supabase
      .from('faq_categories')
      .select('id')
      .limit(1);

    if (!existingCategories || existingCategories.length === 0) {
      console.log('ℹ️  No FAQ data found. FAQ data should be created by database migration.');
      return;
    }

    // Add test-specific FAQ items
    const testFaqItems = [
      {
        question: 'How do I test the support system?',
        answer: 'This is a test FAQ item created for automated testing. You can use this to test FAQ functionality.',
        category_name: 'Technical Support'
      },
      {
        question: 'What should I do if tests fail?',
        answer: 'Check the test logs and ensure all services are running properly. Make sure the database is properly seeded.',
        category_name: 'Technical Support'
      }
    ];

    for (const item of testFaqItems) {
      // Get category ID
      const { data: category } = await supabase
        .from('faq_categories')
        .select('id')
        .eq('name', item.category_name)
        .single();

      if (category) {
        // Check if item already exists
        const { data: existingItem } = await supabase
          .from('faq_items')
          .select('id')
          .eq('question', item.question)
          .single();

        if (!existingItem) {
          const { error } = await supabase
            .from('faq_items')
            .insert({
              category_id: category.id,
              question: item.question,
              answer: item.answer,
              display_order: 999, // Put test items at the end
              is_active: true
            });

          if (error) {
            console.warn(`⚠️  Could not create FAQ item:`, error.message);
          } else {
            console.log(`✅ Created test FAQ item: ${item.question}`);
          }
        }
      }
    }
  } catch (error) {
    console.warn('⚠️  Error creating test FAQ data:', error);
  }
}

async function cleanupTestData(supabase: any) {
  console.log('🧹 Cleaning up old test data...');

  try {
    // Clean up old support messages from test users
    const { error: messagesError } = await supabase
      .from('support_messages')
      .delete()
      .in('subject', [
        'Test Support Message',
        'Admin Test Message',
        'Persistence Test'
      ]);

    if (messagesError) {
      console.warn('⚠️  Could not clean up test support messages:', messagesError.message);
    }

    // Clean up old chat sessions from test users
    const { data: testUsers } = await supabase
      .from('users')
      .select('id')
      .in('email', ['<EMAIL>', '<EMAIL>']);

    if (testUsers && testUsers.length > 0) {
      const testUserIds = testUsers.map(user => user.id);
      
      const { error: chatError } = await supabase
        .from('chat_sessions')
        .delete()
        .in('user_id', testUserIds);

      if (chatError) {
        console.warn('⚠️  Could not clean up test chat sessions:', chatError.message);
      }
    }

    // Clean up rate limiting data for test users
    const { error: rateLimitError } = await supabase
      .from('rate_limiting')
      .delete()
      .lt('window_end', new Date().toISOString());

    if (rateLimitError) {
      console.warn('⚠️  Could not clean up rate limiting data:', rateLimitError.message);
    }

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.warn('⚠️  Error during test data cleanup:', error);
  }
}

export default globalSetup;
