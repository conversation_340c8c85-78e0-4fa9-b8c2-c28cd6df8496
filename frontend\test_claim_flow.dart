import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'lib/services/auth_service.dart';
import 'lib/services/reward_service.dart';
import 'lib/providers/reward_provider.dart';

/// Simple test to trigger the claim flow and see detailed logs
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 Testing Daily Reward Claim Flow...');
  
  try {
    // Initialize services
    final authService = AuthService();
    final rewardService = RewardService(authService);
    final rewardProvider = RewardProvider(rewardService);
    
    print('✅ Services initialized');
    
    // Check if user is authenticated
    final token = await authService.getToken();
    if (token == null) {
      print('❌ No authentication token found');
      print('ℹ️ Please log in through the app first');
      return;
    }
    
    print('✅ Authentication token found');
    
    // Fetch daily reward status first
    print('📡 Fetching daily reward status...');
    await rewardProvider.fetchDailyRewardStatus();
    
    print('📊 Can claim daily reward: ${rewardProvider.canClaimDailyReward}');
    print('📊 Daily rewards error: ${rewardProvider.dailyRewardsError}');
    
    if (!rewardProvider.canClaimDailyReward) {
      print('❌ Cannot claim daily reward - conditions not met');
      return;
    }
    
    // Attempt to claim daily reward
    print('🎁 Attempting to claim daily reward...');
    final success = await rewardProvider.claimDailyReward();
    
    print('🎯 Claim result: $success');
    print('📊 Error after claim: ${rewardProvider.dailyRewardsError}');
    
  } catch (e, stackTrace) {
    print('💥 Test exception: $e');
    print('📚 Stack trace: $stackTrace');
  }
  
  print('🏁 Test completed');
}
