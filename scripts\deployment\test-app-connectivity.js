#!/usr/bin/env node

/**
 * App Connectivity Testing Script
 * Tests if Flutter and Admin apps can connect to the deployed backend
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

class AppConnectivityTester {
  constructor() {
    this.rootDir = path.resolve(__dirname, '../..');
    this.testResults = [];
  }

  log(message, type = 'info', category = 'general') {
    const result = {
      timestamp: new Date().toISOString(),
      message,
      type,
      category
    };
    this.testResults.push(result);
    
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} [${category.toUpperCase()}] ${message}`);
  }

  readFlutterConfig() {
    try {
      const configPath = path.join(this.rootDir, 'frontend', 'lib', 'core', 'api', 'api_config.dart');
      const content = fs.readFileSync(configPath, 'utf8');
      
      // Extract the baseUrl from the Dart file
      const urlMatch = content.match(/return\s+['"`]([^'"`]+)['"`];/);
      if (urlMatch) {
        return urlMatch[1];
      }
      
      return null;
    } catch (error) {
      this.log(`Failed to read Flutter config: ${error.message}`, 'error', 'flutter');
      return null;
    }
  }

  readAdminConfig() {
    try {
      const configPath = path.join(this.rootDir, 'wiggyz_admin', '.env.local');
      const content = fs.readFileSync(configPath, 'utf8');
      
      // Extract the API URL from the env file
      const urlMatch = content.match(/NEXT_PUBLIC_API_URL=(.+)/);
      if (urlMatch) {
        return urlMatch[1].trim();
      }
      
      return null;
    } catch (error) {
      this.log(`Failed to read Admin config: ${error.message}`, 'error', 'admin');
      return null;
    }
  }

  async testUrlConnectivity(url, appName) {
    return new Promise((resolve) => {
      // Test basic connectivity to the URL
      const urlObj = new URL(url);
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: '/health', // Test health endpoint
        method: 'GET',
        timeout: 10000,
        headers: {
          'User-Agent': `WiggyZ-${appName}-Test/1.0`
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            success: res.statusCode < 500, // Accept any non-server-error response
            statusCode: res.statusCode,
            data: data.substring(0, 200) // First 200 chars
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          success: false,
          error: 'Connection timeout'
        });
      });

      req.end();
    });
  }

  async testFlutterConnectivity() {
    this.log('Testing Flutter app connectivity...', 'info', 'flutter');
    
    const flutterUrl = this.readFlutterConfig();
    if (!flutterUrl) {
      this.log('Could not read Flutter API URL', 'error', 'flutter');
      return false;
    }

    this.log(`Flutter API URL: ${flutterUrl}`, 'info', 'flutter');

    // Test connectivity
    const result = await this.testUrlConnectivity(flutterUrl, 'Flutter');
    
    if (result.success) {
      this.log(`Flutter app can connect to backend (Status: ${result.statusCode})`, 'success', 'flutter');
      return true;
    } else {
      this.log(`Flutter connectivity failed: ${result.error || `Status ${result.statusCode}`}`, 'error', 'flutter');
      return false;
    }
  }

  async testAdminConnectivity() {
    this.log('Testing Admin dashboard connectivity...', 'info', 'admin');
    
    const adminUrl = this.readAdminConfig();
    if (!adminUrl) {
      this.log('Could not read Admin API URL', 'error', 'admin');
      return false;
    }

    this.log(`Admin API URL: ${adminUrl}`, 'info', 'admin');

    // Test connectivity
    const result = await this.testUrlConnectivity(adminUrl, 'Admin');
    
    if (result.success) {
      this.log(`Admin dashboard can connect to backend (Status: ${result.statusCode})`, 'success', 'admin');
      return true;
    } else {
      this.log(`Admin connectivity failed: ${result.error || `Status ${result.statusCode}`}`, 'error', 'admin');
      return false;
    }
  }

  validateEnvironmentConsistency() {
    this.log('Validating environment consistency...', 'info', 'validation');
    
    const flutterUrl = this.readFlutterConfig();
    const adminUrl = this.readAdminConfig();
    
    if (!flutterUrl || !adminUrl) {
      this.log('Could not read configuration files', 'error', 'validation');
      return false;
    }

    // Extract base URLs (remove /api/v1 suffix)
    const flutterBase = flutterUrl.replace('/api/v1', '');
    const adminBase = adminUrl.replace('/api/v1', '');

    if (flutterBase === adminBase) {
      this.log('Flutter and Admin apps are configured for the same backend', 'success', 'validation');
      return true;
    } else {
      this.log(`Configuration mismatch: Flutter=${flutterBase}, Admin=${adminBase}`, 'error', 'validation');
      return false;
    }
  }

  checkEnvironmentMode() {
    this.log('Checking environment mode...', 'info', 'environment');
    
    const flutterUrl = this.readFlutterConfig();
    
    if (!flutterUrl) {
      this.log('Could not determine environment mode', 'warn', 'environment');
      return 'unknown';
    }

    if (flutterUrl.includes('127.0.0.1') || flutterUrl.includes('localhost')) {
      this.log('Environment mode: DEVELOPMENT', 'info', 'environment');
      return 'development';
    } else if (flutterUrl.includes('vercel.app')) {
      this.log('Environment mode: PRODUCTION', 'info', 'environment');
      return 'production';
    } else {
      this.log('Environment mode: CUSTOM', 'info', 'environment');
      return 'custom';
    }
  }

  async runAllTests() {
    this.log('🚀 Starting app connectivity tests...', 'info', 'test');
    
    // Check environment consistency
    const consistencyCheck = this.validateEnvironmentConsistency();
    const environmentMode = this.checkEnvironmentMode();
    
    // Test connectivity
    const flutterTest = await this.testFlutterConnectivity();
    const adminTest = await this.testAdminConnectivity();
    
    const allPassed = consistencyCheck && flutterTest && adminTest;
    const passedTests = [consistencyCheck, flutterTest, adminTest].filter(Boolean).length;
    
    this.log(`\n📊 Connectivity Test Summary:`, 'info', 'summary');
    this.log(`Environment Mode: ${environmentMode.toUpperCase()}`, 'info', 'summary');
    this.log(`Tests Passed: ${passedTests}/3`, 'info', 'summary');
    this.log(`Configuration Consistency: ${consistencyCheck ? 'PASS' : 'FAIL'}`, consistencyCheck ? 'success' : 'error', 'summary');
    this.log(`Flutter Connectivity: ${flutterTest ? 'PASS' : 'FAIL'}`, flutterTest ? 'success' : 'error', 'summary');
    this.log(`Admin Connectivity: ${adminTest ? 'PASS' : 'FAIL'}`, adminTest ? 'success' : 'error', 'summary');
    
    if (allPassed) {
      this.log('🎉 All connectivity tests passed! Apps are ready to use the deployed backend.', 'success', 'summary');
    } else if (passedTests >= 2) {
      this.log('⚠️ Most tests passed. Minor issues detected but apps should work.', 'warn', 'summary');
    } else {
      this.log('❌ Multiple connectivity issues detected. Apps may not work properly.', 'error', 'summary');
    }

    return {
      success: allPassed,
      passed: passedTests,
      total: 3,
      environmentMode,
      details: {
        consistency: consistencyCheck,
        flutter: flutterTest,
        admin: adminTest
      }
    };
  }
}

// CLI Interface
if (require.main === module) {
  console.log('🔗 WiggyZ App Connectivity Tester\n');
  
  const tester = new AppConnectivityTester();
  
  tester.runAllTests()
    .then(result => {
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Connectivity test crashed:', error.message);
      process.exit(1);
    });
}

module.exports = AppConnectivityTester;
