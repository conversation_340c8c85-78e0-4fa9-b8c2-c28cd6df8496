1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.wiggyz_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:2:5-67
15-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:2:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:40:5-45:15
24        <intent>
24-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:41:9-44:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:42:13-72
25-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:42:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:13-50
27-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:19-48
28        </intent>
29        <intent>
29-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:11:9-17:18
30            <action android:name="android.intent.action.VIEW" />
30-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:12:13-65
30-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:12:21-62
31
32            <data
32-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:13-50
33                android:mimeType="*/*"
33-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:19-48
34                android:scheme="*" />
35        </intent>
36        <intent>
36-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:18:9-27:18
37            <action android:name="android.intent.action.VIEW" />
37-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:12:13-65
37-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:12:21-62
38
39            <category android:name="android.intent.category.BROWSABLE" />
39-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:21:13-74
39-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:21:23-71
40
41            <data
41-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:13-50
42                android:host="pay"
43                android:mimeType="*/*"
43-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:19-48
44                android:scheme="upi" />
45        </intent>
46        <intent>
46-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:28:9-30:18
47            <action android:name="android.intent.action.MAIN" />
47-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:13-65
47-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:21-62
48        </intent>
49        <intent>
49-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:31:9-35:18
50            <action android:name="android.intent.action.SEND" />
50-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:13-65
50-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:21-62
51
52            <data android:mimeType="*/*" />
52-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:13-50
52-->C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:19-48
53        </intent>
54        <intent>
54-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:36:9-38:18
55            <action android:name="rzp.device_token.share" />
55-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:13-61
55-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:21-58
56        </intent>
57    </queries>
58
59    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
59-->[:connectivity_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
59-->[:connectivity_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
60
61    <uses-feature
61-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
62        android:glEsVersion="0x00020000"
62-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
63        android:required="true" />
63-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
64
65    <permission
65-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
66        android:name="com.example.wiggyz_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
66-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
67        android:protectionLevel="signature" />
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
68
69    <uses-permission android:name="com.example.wiggyz_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
70
71    <application
72        android:name="android.app.Application"
73        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
73-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
74        android:debuggable="true"
75        android:extractNativeLibs="false"
76        android:icon="@mipmap/ic_launcher"
77        android:label="wiggyz_app" >
78        <activity
79            android:name="com.example.wiggyz_app.MainActivity"
80            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
81            android:exported="true"
82            android:hardwareAccelerated="true"
83            android:launchMode="singleTop"
84            android:taskAffinity=""
85            android:theme="@style/LaunchTheme"
86            android:windowSoftInputMode="adjustResize" >
87
88            <!--
89                 Specifies an Android theme to apply to this Activity as soon as
90                 the Android process has started. This theme is visible to the user
91                 while the Flutter UI initializes. After that, this theme continues
92                 to determine the Window background behind the Flutter UI.
93            -->
94            <meta-data
95                android:name="io.flutter.embedding.android.NormalTheme"
96                android:resource="@style/NormalTheme" />
97
98            <intent-filter>
99                <action android:name="android.intent.action.MAIN" />
99-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:13-65
99-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:21-62
100
101                <category android:name="android.intent.category.LAUNCHER" />
102            </intent-filter>
103        </activity>
104        <!--
105             Don't delete the meta-data below.
106             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
107        -->
108        <meta-data
109            android:name="flutterEmbedding"
110            android:value="2" />
111        <!--
112           Declares a provider which allows us to store files to share in
113           '.../caches/share_plus' and grant the receiving action access
114        -->
115        <provider
115-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
116            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
116-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
117            android:authorities="com.example.wiggyz_app.flutter.share_provider"
117-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
118            android:exported="false"
118-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
119            android:grantUriPermissions="true" >
119-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
120            <meta-data
120-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
121                android:name="android.support.FILE_PROVIDER_PATHS"
121-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
122                android:resource="@xml/flutter_share_file_paths" />
122-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
123        </provider>
124        <!--
125           This manifest declared broadcast receiver allows us to use an explicit
126           Intent when creating a PendingItent to be informed of the user's choice
127        -->
128        <receiver
128-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
129            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
129-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
130            android:exported="false" >
130-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
131            <intent-filter>
131-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
132                <action android:name="EXTRA_CHOSEN_COMPONENT" />
132-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
132-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
133            </intent-filter>
134        </receiver>
135
136        <activity
136-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:42:9-50:20
137            android:name="com.razorpay.CheckoutActivity"
137-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:43:13-57
138            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
138-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:44:13-83
139            android:exported="false"
139-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:45:13-37
140            android:theme="@style/CheckoutTheme" >
140-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:46:13-49
141            <intent-filter>
141-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:47:13-49:29
142                <action android:name="android.intent.action.MAIN" />
142-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:13-65
142-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:21-62
143            </intent-filter>
144        </activity>
145
146        <provider
146-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:52:9-60:20
147            android:name="androidx.startup.InitializationProvider"
147-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:53:13-67
148            android:authorities="com.example.wiggyz_app.androidx-startup"
148-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:54:13-68
149            android:exported="false" >
149-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:55:13-37
150            <meta-data
150-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:57:13-59:52
151                android:name="com.razorpay.RazorpayInitializer"
151-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:58:17-64
152                android:value="androidx.startup" />
152-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:59:17-49
153            <meta-data
153-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
154                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
154-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
155                android:value="androidx.startup" />
155-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
156            <meta-data
156-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
157                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
157-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
158                android:value="androidx.startup" />
158-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
159        </provider>
160
161        <activity
161-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:62:9-65:75
162            android:name="com.razorpay.MagicXActivity"
162-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:63:13-55
163            android:exported="false"
163-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:64:13-37
164            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
164-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:65:13-72
165
166        <meta-data
166-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:67:9-69:58
167            android:name="com.razorpay.plugin.googlepay_all"
167-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:68:13-61
168            android:value="com.razorpay.RzpGpayMerged" />
168-->[com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:69:13-55
169
170        <provider
170-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
171            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
171-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
172            android:authorities="com.example.wiggyz_app.flutter.image_provider"
172-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
173            android:exported="false"
173-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
174            android:grantUriPermissions="true" >
174-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
175            <meta-data
175-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
176                android:name="android.support.FILE_PROVIDER_PATHS"
176-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
177                android:resource="@xml/flutter_image_picker_file_paths" />
177-->[:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
178        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
179        <service
179-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
180            android:name="com.google.android.gms.metadata.ModuleDependencies"
180-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
181            android:enabled="false"
181-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
182            android:exported="false" >
182-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
183            <intent-filter>
183-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
184                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
184-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
184-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
185            </intent-filter>
186
187            <meta-data
187-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
188                android:name="photopicker_activity:0:required"
188-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
189                android:value="" />
189-->[:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
190        </service>
191
192        <activity
192-->[:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
193            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
193-->[:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
194            android:exported="false"
194-->[:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
195            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
195-->[:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
196        <activity
196-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
197            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
197-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
198            android:excludeFromRecents="true"
198-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
199            android:exported="false"
199-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
200            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
200-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
201        <!--
202            Service handling Google Sign-In user revocation. For apps that do not integrate with
203            Google Sign-In, this service will never be started.
204        -->
205        <service
205-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
206            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
206-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
207            android:exported="true"
207-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
208            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
208-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
209            android:visibleToInstantApps="true" /> <!-- Needs to be explicitly declared on P+ -->
209-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
210        <uses-library
210-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
211            android:name="org.apache.http.legacy"
211-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
212            android:required="false" />
212-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
213
214        <activity
214-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
215            android:name="com.google.android.gms.common.api.GoogleApiActivity"
215-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
216            android:exported="false"
216-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
217            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
217-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
218
219        <meta-data
219-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
220            android:name="com.google.android.gms.version"
220-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
221            android:value="@integer/google_play_services_version" />
221-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
222
223        <uses-library
223-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
224            android:name="androidx.window.extensions"
224-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
225            android:required="false" />
225-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
226        <uses-library
226-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
227            android:name="androidx.window.sidecar"
227-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
228            android:required="false" />
228-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
229
230        <receiver
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
231            android:name="androidx.profileinstaller.ProfileInstallReceiver"
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
232            android:directBootAware="false"
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
233            android:enabled="true"
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
234            android:exported="true"
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
235            android:permission="android.permission.DUMP" >
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
237                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
240                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
243                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
244            </intent-filter>
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
246                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
247            </intent-filter>
248        </receiver>
249    </application>
250
251</manifest>
