// Move to backend directory and run: node ../test_daily_rewards.js
const axios = require('axios');

// Test the daily rewards API
async function testDailyRewards() {
  const baseURL = 'http://localhost:8080/api/v1';
  
  try {
    // First, let's login to get a token
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log('Login response:', JSON.stringify(loginResponse.data, null, 2));
    const token = loginResponse.data.access_token;
    console.log('✅ Login successful, token:', token ? 'Found' : 'Not found');
    
    // Test daily reward status
    console.log('\n📊 Testing daily reward status...');
    const statusResponse = await axios.get(`${baseURL}/rewards/daily-status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ Daily reward status response:');
    console.log('hasClaimedToday:', statusResponse.data.data.hasClaimedToday);
    console.log('current_streak:', statusResponse.data.data.streak.current_streak);

    // Debug timezone
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    console.log('Server timezone check:');
    console.log('Current time:', now.toISOString());
    console.log('Today string:', today);
    console.log('Range start:', `${today}T00:00:00Z`);
    console.log('Range end:', `${today}T23:59:59Z`);
    
    // Test daily reward claim
    console.log('\n🎁 Testing daily reward claim...');
    const claimResponse = await axios.post(`${baseURL}/rewards/claim-daily`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Idempotency-Key': `test-${Date.now()}`
      }
    });
    
    console.log('✅ Daily reward claim response:');
    console.log(JSON.stringify(claimResponse.data, null, 2));
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testDailyRewards();
