#!/usr/bin/env node

/**
 * Flutter Connectivity Test Script
 * Tests if the deployed backend is accessible from Flutter app perspective
 */

const https = require('https');

class FlutterConnectivityTest {
  constructor() {
    this.baseUrl = 'https://wiggyz-public-75pt8bev8-tausifraja977-gmailcoms-projects.vercel.app';
  }

  log(message, type = 'info') {
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} ${message}`);
  }

  makeRequest(path, options = {}) {
    return new Promise((resolve) => {
      const url = `${this.baseUrl}${path}`;
      const urlObj = new URL(url);
      
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Flutter/3.0 (WiggyZ Mobile App)',
          'Origin': 'http://localhost:3000', // Simulate Flutter app origin
          ...options.headers
        },
        timeout: 15000
      };

      const req = https.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            success: res.statusCode < 500,
            statusCode: res.statusCode,
            headers: res.headers,
            data: data,
            isJson: res.headers['content-type']?.includes('application/json')
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message,
          statusCode: 0
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          success: false,
          error: 'Request timeout',
          statusCode: 0
        });
      });

      if (options.body) {
        req.write(JSON.stringify(options.body));
      }

      req.end();
    });
  }

  async testHealthEndpoint() {
    this.log('Testing health endpoint...', 'info');
    
    const result = await this.makeRequest('/health');
    
    if (result.success && result.isJson) {
      this.log('Health endpoint accessible via API call!', 'success');
      this.log(`Status: ${result.statusCode}`, 'info');
      this.log(`Response: ${result.data.substring(0, 200)}`, 'info');
      return true;
    } else if (result.statusCode === 401) {
      this.log('Health endpoint requires authentication (Vercel protection active)', 'warn');
      return false;
    } else {
      this.log(`Health endpoint failed: ${result.error || `Status ${result.statusCode}`}`, 'error');
      return false;
    }
  }

  async testCORSPreflight() {
    this.log('Testing CORS preflight...', 'info');
    
    const result = await this.makeRequest('/api/v1/auth/login', {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
      }
    });

    if (result.success) {
      this.log('CORS preflight successful!', 'success');
      this.log(`CORS headers: ${JSON.stringify(result.headers)}`, 'info');
      return true;
    } else {
      this.log(`CORS preflight failed: ${result.error || `Status ${result.statusCode}`}`, 'error');
      return false;
    }
  }

  async testAuthEndpoint() {
    this.log('Testing auth endpoint with POST request...', 'info');
    
    const result = await this.makeRequest('/api/v1/auth/login', {
      method: 'POST',
      body: {
        email: '<EMAIL>',
        password: 'testpassword'
      }
    });

    if (result.success) {
      this.log('Auth endpoint accessible via API call!', 'success');
      this.log(`Status: ${result.statusCode}`, 'info');
      this.log(`Response: ${result.data.substring(0, 200)}`, 'info');
      return true;
    } else if (result.statusCode === 401) {
      this.log('Auth endpoint requires authentication (Vercel protection active)', 'warn');
      return false;
    } else {
      this.log(`Auth endpoint failed: ${result.error || `Status ${result.statusCode}`}`, 'error');
      return false;
    }
  }

  async testFlutterCompatibility() {
    this.log('🧪 Testing Flutter App Compatibility', 'info');
    this.log(`🎯 Target URL: ${this.baseUrl}`, 'info');
    this.log('=' * 60, 'info');

    const tests = [
      { name: 'Health Endpoint', test: () => this.testHealthEndpoint() },
      { name: 'CORS Preflight', test: () => this.testCORSPreflight() },
      { name: 'Auth Endpoint', test: () => this.testAuthEndpoint() }
    ];

    let passedTests = 0;
    const results = {};

    for (const { name, test } of tests) {
      this.log(`\n--- Testing ${name} ---`, 'info');
      try {
        const result = await test();
        results[name] = result;
        if (result) passedTests++;
      } catch (error) {
        this.log(`Test ${name} crashed: ${error.message}`, 'error');
        results[name] = false;
      }
    }

    this.log('\n📊 Flutter Compatibility Test Results', 'info');
    this.log(`Tests Passed: ${passedTests}/${tests.length}`, 'info');

    if (passedTests === tests.length) {
      this.log('🎉 All tests passed! Flutter app should be able to connect.', 'success');
      this.log('✅ The backend is accessible via API calls despite browser protection.', 'success');
    } else if (passedTests > 0) {
      this.log('⚠️ Some tests passed. Flutter app may work with limitations.', 'warn');
      this.log('💡 Try testing with the actual Flutter app to confirm.', 'info');
    } else {
      this.log('❌ All tests failed. Vercel protection is blocking API access.', 'error');
      this.log('🔧 You need to disable Vercel protection in project settings.', 'error');
    }

    this.log('\n🔗 Flutter App Configuration:', 'info');
    this.log(`API Base URL: ${this.baseUrl}/api/v1`, 'info');
    this.log('File: frontend/lib/core/api/api_config.dart', 'info');

    return {
      success: passedTests > 0,
      passed: passedTests,
      total: tests.length,
      results: results
    };
  }
}

// CLI Interface
if (require.main === module) {
  console.log('📱 WiggyZ Flutter Connectivity Test\n');
  
  const tester = new FlutterConnectivityTest();
  
  tester.testFlutterCompatibility()
    .then(result => {
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test crashed:', error.message);
      process.exit(1);
    });
}

module.exports = FlutterConnectivityTest;
