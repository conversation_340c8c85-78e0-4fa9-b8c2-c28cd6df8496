import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Service to handle image compression and validation
class ImageCompressionService {
  // Image constraints
  static const int maxFileSizeBytes = 2 * 1024 * 1024; // 2MB
  static const int maxWidth = 1024;
  static const int maxHeight = 1024;
  static const int compressionQuality = 85;

  /// Validate image file size and dimensions
  static Future<bool> validateImage(File imageFile) async {
    try {
      // Check file size
      final fileSize = await imageFile.length();
      if (fileSize > maxFileSizeBytes) {
        debugPrint('Image file too large: ${fileSize / (1024 * 1024)} MB');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error validating image: $e');
      return false;
    }
  }

  /// Compress image file to meet size and dimension requirements
  static Future<File?> compressImage(File imageFile) async {
    try {
      debugPrint('=== COMPRESS IMAGE START ===');
      debugPrint('Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
      debugPrint('Input file path: ${imageFile.path}');
      debugPrint('File exists: ${await imageFile.exists()}');

      // Check if we're on web and handle differently
      if (kIsWeb) {
        debugPrint('Web platform detected - using alternative compression');
        return await _compressImageForWeb(imageFile);
      }

      // Get temporary directory for compressed image
      final tempDir = await getTemporaryDirectory();
      final fileName = path.basename(imageFile.path);
      final nameWithoutExtension = path.basenameWithoutExtension(fileName);
      final compressedPath = path.join(
        tempDir.path,
        '${nameWithoutExtension}_compressed.jpg',
      );

      debugPrint('Temp directory: ${tempDir.path}');
      debugPrint('Output path: $compressedPath');
      debugPrint('Compression quality: $compressionQuality');

      // Compress the image
      debugPrint('Calling FlutterImageCompress.compressAndGetFile...');
      debugPrint('Source path: ${imageFile.absolute.path}');
      debugPrint('Target path: $compressedPath');

      XFile? compressedFile;
      try {
        compressedFile = await FlutterImageCompress.compressAndGetFile(
          imageFile.absolute.path,
          compressedPath,
          quality: compressionQuality,
          minWidth: 100,
          minHeight: 100,
          format: CompressFormat.jpeg,
        );
      } catch (compressionError) {
        debugPrint('FlutterImageCompress threw error: $compressionError');
        return null;
      }

      debugPrint(
        'FlutterImageCompress result: ${compressedFile != null ? 'XFile object' : 'null'}',
      );

      if (compressedFile == null) {
        debugPrint('COMPRESSION FAILED: FlutterImageCompress returned null');
        return null;
      }

      // Convert XFile to File
      final resultFile = File(compressedFile.path);

      // Validate compressed image
      final isValid = await validateImage(resultFile);
      if (!isValid) {
        debugPrint('Compressed image still doesn\'t meet requirements');
        return null;
      }

      debugPrint('Image compressed successfully: ${resultFile.path}');
      return resultFile;
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return null;
    }
  }

  /// Compress image from XFile (for web compatibility)
  static Future<Uint8List?> compressImageFromXFile(XFile imageFile) async {
    try {
      // Read image bytes
      final imageBytes = await imageFile.readAsBytes();

      // Compress the image
      final compressedBytes = await FlutterImageCompress.compressWithList(
        imageBytes,
        quality: compressionQuality,
        minWidth: 100,
        minHeight: 100,
        format: CompressFormat.jpeg,
      );

      // Check compressed size
      if (compressedBytes.length > maxFileSizeBytes) {
        debugPrint(
          'Compressed image still too large: ${compressedBytes.length / (1024 * 1024)} MB',
        );
        return null;
      }

      debugPrint(
        'Image compressed successfully: ${compressedBytes.length / 1024} KB',
      );
      return compressedBytes;
    } catch (e) {
      debugPrint('Error compressing image from XFile: $e');
      return null;
    }
  }

  /// Get image format validation
  static bool isValidImageFormat(String fileName, [String? mimeType]) {
    final extension = path.extension(fileName).toLowerCase();
    final validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];

    // Check by file extension first
    if (validExtensions.contains(extension)) {
      return true;
    }

    // If no valid extension, check MIME type as fallback
    if (mimeType != null) {
      final validMimeTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
      ];
      return validMimeTypes.contains(mimeType.toLowerCase());
    }

    return false;
  }

  /// Get human-readable file size
  static String getFileSizeString(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// Validate and compress image from XFile (web-compatible)
  static Future<File?> processImageFromXFile(
    XFile imageFile,
    String originalFileName,
    String? mimeType,
  ) async {
    try {
      debugPrint('=== IMAGE COMPRESSION SERVICE DEBUG (XFile) ===');
      debugPrint('Input file: ${imageFile.path}');
      debugPrint('Original filename: $originalFileName');
      debugPrint('MIME type: $mimeType');
      debugPrint('Platform: ${kIsWeb ? 'Web' : 'Mobile'}');

      // Validate format using original filename and MIME type
      final isValidFormat = isValidImageFormat(originalFileName, mimeType);
      debugPrint('Format validation result: $isValidFormat');

      if (!isValidFormat) {
        debugPrint(
          'VALIDATION FAILED: Invalid image format: $originalFileName, $mimeType',
        );
        return null;
      }

      debugPrint('Format validation PASSED - proceeding to compression');

      if (kIsWeb) {
        // For web, handle XFile directly
        final result = await _processXFileForWeb(imageFile);
        debugPrint(
          'Web compression result: ${result != null ? 'SUCCESS' : 'FAILED'}',
        );
        return result;
      } else {
        // For mobile, convert to File and process normally
        final file = File(imageFile.path);
        final result = await _processImageInternal(file);
        debugPrint(
          'Mobile compression result: ${result != null ? 'SUCCESS' : 'FAILED'}',
        );
        return result;
      }
    } catch (e) {
      debugPrint('ERROR in processImageFromXFile: $e');
      return null;
    }
  }

  /// Validate and compress image in one step (with validation) - Legacy method
  static Future<File?> processImageWithValidation(
    File imageFile,
    String originalFileName,
    String? mimeType,
  ) async {
    try {
      debugPrint('=== IMAGE COMPRESSION SERVICE DEBUG ===');
      debugPrint('Input file: ${imageFile.path}');
      debugPrint('Original filename: $originalFileName');
      debugPrint('MIME type: $mimeType');

      // Validate format using original filename and MIME type
      final isValidFormat = isValidImageFormat(originalFileName, mimeType);
      debugPrint('Format validation result: $isValidFormat');

      if (!isValidFormat) {
        debugPrint(
          'VALIDATION FAILED: Invalid image format: $originalFileName, $mimeType',
        );
        return null;
      }

      debugPrint('Format validation PASSED - proceeding to compression');
      final result = await _processImageInternal(imageFile);
      debugPrint(
        'Compression result: ${result != null ? 'SUCCESS' : 'FAILED'}',
      );
      return result;
    } catch (e) {
      debugPrint('ERROR in processImageWithValidation: $e');
      return null;
    }
  }

  /// Validate and compress image in one step (legacy method)
  static Future<File?> processImage(File imageFile) async {
    try {
      // For processImage method, we'll be more lenient with validation
      // since we don't have access to original filename or MIME type
      // The validation should have been done before calling this method
      debugPrint('Processing image: ${imageFile.path}');
      return await _processImageInternal(imageFile);
    } catch (e) {
      debugPrint('Error processing image: $e');
      return null;
    }
  }

  /// Internal method to handle the actual image processing
  static Future<File?> _processImageInternal(File imageFile) async {
    try {
      debugPrint('=== INTERNAL PROCESSING START ===');

      // Check if compression is needed
      final fileSize = await imageFile.length();
      debugPrint('Original image size: ${getFileSizeString(fileSize)}');
      debugPrint('Max allowed size: ${getFileSizeString(maxFileSizeBytes)}');

      if (fileSize <= maxFileSizeBytes) {
        // Image is already within size limits, but still compress for consistency
        debugPrint('Image within size limits, applying light compression');
      } else {
        debugPrint('Image exceeds size limits, compression required');
      }

      // Compress the image
      debugPrint('Calling compressImage...');
      final compressedFile = await compressImage(imageFile);
      debugPrint(
        'compressImage returned: ${compressedFile != null ? 'File object' : 'null'}',
      );

      if (compressedFile != null) {
        final compressedSize = await compressedFile.length();
        debugPrint(
          'Compressed image size: ${getFileSizeString(compressedSize)}',
        );
        debugPrint('Compression successful!');
      } else {
        debugPrint('Compression FAILED - compressImage returned null');
      }

      debugPrint('=== INTERNAL PROCESSING END ===');
      return compressedFile;
    } catch (e) {
      debugPrint('ERROR in _processImageInternal: $e');
      return null;
    }
  }

  /// Process XFile for web platform (handles blob URLs)
  static Future<File?> _processXFileForWeb(XFile imageFile) async {
    try {
      debugPrint('=== WEB XFILE PROCESSING START ===');

      // Read image bytes directly from XFile
      final imageBytes = await imageFile.readAsBytes();
      debugPrint('Read ${imageBytes.length} bytes from XFile');
      debugPrint('Original size: ${getFileSizeString(imageBytes.length)}');

      if (imageBytes.length <= maxFileSizeBytes) {
        debugPrint(
          'Image within size limits, applying light compression for consistency',
        );
      } else {
        debugPrint('Image exceeds size limits, compression required');
      }

      // Compress the image bytes
      final compressedBytes = await FlutterImageCompress.compressWithList(
        imageBytes,
        quality: compressionQuality,
        minWidth: 100,
        minHeight: 100,
        format: CompressFormat.jpeg,
      );

      if (compressedBytes.isEmpty) {
        debugPrint('Web compression failed - empty result');
        return null;
      }

      debugPrint(
        'Web compression successful: ${getFileSizeString(compressedBytes.length)}',
      );

      // Create a temporary file with the compressed bytes
      final tempDir = await getTemporaryDirectory();
      final compressedFile = File(
        '${tempDir.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      await compressedFile.writeAsBytes(compressedBytes);

      debugPrint('=== WEB XFILE PROCESSING END ===');
      return compressedFile;
    } catch (e) {
      debugPrint('ERROR in _processXFileForWeb: $e');
      return null;
    }
  }

  /// Compress image for web platform (handles blob URLs) - Legacy method
  static Future<File?> _compressImageForWeb(File imageFile) async {
    try {
      debugPrint('=== WEB COMPRESSION START ===');

      // For web, we'll use a simpler approach
      // Check if the file size is acceptable
      final fileSize = await imageFile.length();
      debugPrint('Web file size: ${getFileSizeString(fileSize)}');

      if (fileSize <= maxFileSizeBytes) {
        debugPrint('Web file within size limits, returning original');
        return imageFile;
      }

      debugPrint('Web file too large, attempting compression...');

      // Try to read the file as bytes and compress using compressWithList
      final imageBytes = await imageFile.readAsBytes();
      debugPrint('Read ${imageBytes.length} bytes from web file');

      final compressedBytes = await FlutterImageCompress.compressWithList(
        imageBytes,
        quality: compressionQuality,
        minWidth: 100,
        minHeight: 100,
        format: CompressFormat.jpeg,
      );

      if (compressedBytes.isEmpty) {
        debugPrint('Web compression failed - empty result');
        return null;
      }

      debugPrint('Web compression successful: ${compressedBytes.length} bytes');

      // Create a temporary file with the compressed bytes
      final tempDir = await getTemporaryDirectory();
      final compressedFile = File(
        path.join(tempDir.path, 'compressed_web_image.jpg'),
      );
      await compressedFile.writeAsBytes(compressedBytes);

      debugPrint('Web compressed file created: ${compressedFile.path}');
      return compressedFile;
    } catch (e) {
      debugPrint('Error in web compression: $e');
      return null;
    }
  }

  /// Validate image dimensions (simplified version since maxWidth/maxHeight aren't supported)
  static Future<bool> _validateImageDimensions(
    File imageFile,
    int maxWidth,
    int maxHeight,
  ) async {
    try {
      // For now, we'll rely on the compression library to handle sizing
      // In a production app, you might want to use a package like 'image' to check actual dimensions
      return true;
    } catch (e) {
      debugPrint('Error validating image dimensions: $e');
      return false;
    }
  }
}
