-- Migration: Add game-specific profile fields for comprehensive profile management
-- Date: 2025-07-17
-- Description: Adds PUBG fields and enhances Free Fire fields with rank and preferred mode

-- Add missing Free Fire fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS ff_rank VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS ff_preferred_mode VARCHAR(50);

-- Add PUBG-specific fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_name VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_uid VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_level INTEGER DEFAULT 1;
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_server VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_rank VARCHAR(50);
ALTER TABLE users ADD COLUMN IF NOT EXISTS pubg_preferred_mode VARCHAR(50);

-- Add phone number field if not exists
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(15);

-- Add bio and location fields if not exists
ALTER TABLE users ADD COLUMN IF NOT EXISTS bio TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS location VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS date_of_birth DATE;

-- Add profile image URL field if not exists (for enhanced image storage)
ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_image_url TEXT;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_ff_name ON users(ff_name);
CREATE INDEX IF NOT EXISTS idx_users_pubg_name ON users(pubg_name);
CREATE INDEX IF NOT EXISTS idx_users_profile_image ON users(profile_image);

-- Add comments for documentation
COMMENT ON COLUMN users.ff_rank IS 'Free Fire player rank/tier (Bronze, Silver, Gold, Platinum, Diamond, Master, Grandmaster, Heroic)';
COMMENT ON COLUMN users.ff_preferred_mode IS 'Free Fire preferred game mode (Solo, Duo, Squad, Ranked, Casual)';
COMMENT ON COLUMN users.pubg_name IS 'PUBG Mobile player username';
COMMENT ON COLUMN users.pubg_uid IS 'PUBG Mobile player unique identifier';
COMMENT ON COLUMN users.pubg_level IS 'PUBG Mobile player level';
COMMENT ON COLUMN users.pubg_server IS 'PUBG Mobile server region';
COMMENT ON COLUMN users.pubg_rank IS 'PUBG Mobile player rank/tier (Bronze, Silver, Gold, Platinum, Diamond, Crown, Ace, Conqueror)';
COMMENT ON COLUMN users.pubg_preferred_mode IS 'PUBG Mobile preferred game mode (Solo, Duo, Squad, Ranked, Classic, Arena)';
COMMENT ON COLUMN users.profile_image_url IS 'URL to user profile image stored in Supabase storage';
