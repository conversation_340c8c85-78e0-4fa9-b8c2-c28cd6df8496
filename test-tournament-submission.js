const axios = require('axios');
const { createClient } = require('@supabase/supabase-js');

// Configuration
const BACKEND_URL = 'http://localhost:5000/api/v1';
const supabaseUrl = 'https://johjwarjusfahxidemjd.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpvaGp3YXJqdXNmYWh4aWRlbWpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMDM3NjcsImV4cCI6MjA2MTU3OTc2N30.F77CeDDNeQqRjhhUjhcB_322RLaP4pBx6-2L0VZ7gJY';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testTournamentResultSubmission() {
  try {
    console.log('🧪 Testing Tournament Result Submission Flow...\n');

    // Step 1: Get an active tournament
    console.log('📋 Step 1: Finding an active tournament...');
    const { data: tournaments, error: tournamentsError } = await supabase
      .from('tournaments')
      .select('id, name, status')
      .eq('status', 'active')
      .limit(1);

    if (tournamentsError || !tournaments || tournaments.length === 0) {
      console.log('❌ No active tournaments found. Creating test tournament...');
      
      // Create a test tournament
      const { data: newTournament, error: createError } = await supabase
        .from('tournaments')
        .insert({
          name: 'Test Tournament for Screenshot Verification',
          description: 'Testing tournament result submission with screenshots',
          game_id: 1,
          status: 'active',
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          min_participants: 2,
          max_participants: 10,
          entry_fee: 0,
          prize_pool: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        throw new Error(`Failed to create test tournament: ${createError.message}`);
      }
      
      console.log(`✅ Created test tournament: ${newTournament.name} (${newTournament.id})`);
      tournaments[0] = newTournament;
    } else {
      console.log(`✅ Found active tournament: ${tournaments[0].name} (${tournaments[0].id})`);
    }

    const tournament = tournaments[0];

    // Step 2: Get a test user
    console.log('\n📋 Step 2: Finding a test user...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, name, email')
      .limit(1);

    if (usersError || !users || users.length === 0) {
      throw new Error('No users found in database');
    }

    const testUser = users[0];
    console.log(`✅ Using test user: ${testUser.name} (${testUser.id})`);

    // Step 3: Check if user is a tournament participant
    console.log('\n📋 Step 3: Checking tournament participation...');
    let { data: participant, error: participantError } = await supabase
      .from('tournament_participants')
      .select('*')
      .eq('tournament_id', tournament.id)
      .eq('user_id', testUser.id)
      .single();

    if (participantError || !participant) {
      console.log('❌ User not a participant. Adding user to tournament...');
      
      const { data: newParticipant, error: addError } = await supabase
        .from('tournament_participants')
        .insert({
          tournament_id: tournament.id,
          user_id: testUser.id,
          status: 'registered',
          participant_type: 'player',
          joined_at: new Date().toISOString()
        })
        .select()
        .single();

      if (addError) {
        throw new Error(`Failed to add user to tournament: ${addError.message}`);
      }
      
      participant = newParticipant;
      console.log(`✅ Added user as tournament participant`);
    } else {
      console.log(`✅ User is already a tournament participant`);
    }

    // Step 4: Test tournament result submission endpoint
    console.log('\n📋 Step 4: Testing tournament result submission...');
    
    const resultData = {
      final_position: 1,
      final_score: 2500,
      kills_count: 15,
      screenshot_url: 'https://example.com/tournament-screenshot-1.jpg',
      screenshot_urls: [
        'https://example.com/tournament-screenshot-1.jpg',
        'https://example.com/tournament-screenshot-2.jpg',
        'https://example.com/tournament-screenshot-3.jpg'
      ]
    };

    // Note: This will fail without proper authentication, but we can see the endpoint response
    try {
      const response = await axios.post(
        `${BACKEND_URL}/tournaments/${tournament.id}/submit-result`,
        resultData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token' // Mock token
          },
          timeout: 10000,
          validateStatus: () => true // Don't throw on error status codes
        }
      );

      console.log(`📊 Response Status: ${response.status}`);
      console.log(`📊 Response Data:`, JSON.stringify(response.data, null, 2));

      if (response.status === 401) {
        console.log('✅ Endpoint exists and correctly requires authentication');
      } else if (response.status === 201) {
        console.log('✅ Tournament result submitted successfully!');
        
        // Verify the data was stored
        const { data: storedResult, error: verifyError } = await supabase
          .from('tournament_results')
          .select('*')
          .eq('tournament_id', tournament.id)
          .eq('user_id', testUser.id)
          .single();

        if (verifyError) {
          console.log('❌ Failed to verify stored result:', verifyError.message);
        } else {
          console.log('✅ Result stored in database:');
          console.log(`   - Position: ${storedResult.final_position}`);
          console.log(`   - Score: ${storedResult.final_score}`);
          console.log(`   - Screenshot URL: ${storedResult.screenshot_url}`);
          console.log(`   - Screenshot URLs: ${JSON.stringify(storedResult.screenshot_urls)}`);
        }
      } else {
        console.log(`❌ Unexpected response status: ${response.status}`);
      }

    } catch (error) {
      console.log(`❌ Request failed: ${error.message}`);
    }

    // Step 5: Check if there are any pending tournament results
    console.log('\n📋 Step 5: Checking for pending tournament results...');
    const { data: pendingResults, error: pendingError } = await supabase
      .from('tournament_results')
      .select('id, tournament_id, user_id, final_position, screenshot_url, screenshot_urls, verification_status')
      .eq('verification_status', 'pending');

    if (pendingError) {
      console.log('❌ Failed to fetch pending results:', pendingError.message);
    } else {
      console.log(`✅ Found ${pendingResults.length} pending tournament results`);
      pendingResults.forEach((result, index) => {
        console.log(`   Result ${index + 1}:`);
        console.log(`     - ID: ${result.id}`);
        console.log(`     - Position: ${result.final_position}`);
        console.log(`     - Has screenshot_url: ${!!result.screenshot_url}`);
        console.log(`     - Has screenshot_urls: ${!!result.screenshot_urls && result.screenshot_urls.length > 0}`);
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testTournamentResultSubmission();
