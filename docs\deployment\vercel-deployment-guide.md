# WiggyZ Backend Vercel Deployment Guide

## Overview

This guide covers the complete deployment process for the WiggyZ Node.js/TypeScript backend to Vercel serverless platform.

## Prerequisites

1. **Vercel CLI**: Install globally with `npm install -g vercel`
2. **Node.js**: Version 18+ recommended
3. **TypeScript**: Installed in the backend project
4. **Environment Variables**: All required environment variables configured

## Project Structure

```
wiggyz_backup/
├── backend/                    # Node.js/TypeScript backend
│   ├── src/                   # Source code
│   ├── dist/                  # Compiled JavaScript (generated)
│   ├── vercel.json           # Vercel configuration
│   └── package.json          # Updated with Vercel scripts
├── config/
│   ├── environments.json     # Environment configurations
│   └── current-environment.json  # Current environment state
├── scripts/deployment/
│   ├── deploy.js             # Main deployment script
│   ├── environment-manager.js # Environment management
│   └── validators.js         # Pre/post deployment validation
└── docs/deployment/          # This documentation
```

## Configuration Files

### 1. Vercel Configuration (`backend/vercel.json`)

```json
{
  "version": 2,
  "name": "wiggyz-backend",
  "builds": [
    {
      "src": "dist/server.js",
      "use": "@vercel/node",
      "config": {
        "maxLambdaSize": "50mb"
      }
    }
  ],
  "routes": [
    {
      "src": "/health",
      "dest": "/dist/server.js"
    },
    {
      "src": "/api/v1/(.*)",
      "dest": "/dist/server.js"
    }
  ],
  "env": {
    "NODE_ENV": "production",
    "SUPABASE_URL": "@supabase-url",
    "SUPABASE_KEY": "@supabase-key",
    "JWT_REFRESH_SECRET": "@jwt-refresh-secret",
    "RAZORPAY_KEY_ID": "@razorpay-key-id",
    "RAZORPAY_KEY_SECRET": "@razorpay-key-secret"
  },
  "functions": {
    "dist/server.js": {
      "maxDuration": 30
    }
  }
}
```

### 2. Environment Configuration (`config/environments.json`)

Contains development, staging, and production environment settings:

- **Development**: `http://127.0.0.1:8080/api/v1`
- **Production**: `https://wiggyz-backend.vercel.app/api/v1`
- **Staging**: `https://wiggyz-backend-staging.vercel.app/api/v1`

### 3. Package.json Scripts

```json
{
  "scripts": {
    "build": "tsc -p .",
    "build:vercel": "npm run build",
    "deploy": "npm run build && vercel --prod",
    "deploy:staging": "npm run build && vercel",
    "env:switch": "node scripts/deployment/environment-manager.js",
    "env:validate": "node scripts/deployment/validators.js"
  }
}
```

## Deployment Process

### Method 1: Automated Deployment Script

```bash
# Deploy to production
node scripts/deployment/deploy.js

# Deploy to staging
node scripts/deployment/deploy.js --env staging

# Deploy with options
node scripts/deployment/deploy.js --env production --skip-validation
```

### Method 2: Manual Deployment

```bash
# 1. Navigate to backend directory
cd backend

# 2. Install dependencies
npm install

# 3. Build TypeScript
npm run build

# 4. Deploy to Vercel
vercel --prod  # Production
# or
vercel         # Staging
```

### Method 3: Package.json Scripts

```bash
cd backend

# Deploy to production
npm run deploy

# Deploy to staging
npm run deploy:staging
```

## Environment Management

### Switch Environments

```bash
# Switch to production
node scripts/deployment/environment-manager.js switch production

# Switch to development
node scripts/deployment/environment-manager.js switch development

# Check current environment
node scripts/deployment/environment-manager.js current

# List all environments
node scripts/deployment/environment-manager.js list
```

### Environment Variables

Required environment variables for Vercel:

```bash
# Set Vercel environment variables
vercel env add SUPABASE_URL
vercel env add SUPABASE_KEY
vercel env add JWT_REFRESH_SECRET
vercel env add RAZORPAY_KEY_ID
vercel env add RAZORPAY_KEY_SECRET
```

## Validation

### Pre-deployment Validation

```bash
# Validate environment and configuration
node scripts/deployment/validators.js pre
```

### Post-deployment Validation

```bash
# Validate deployed endpoint
node scripts/deployment/validators.js post https://your-deployment-url.vercel.app
```

## Frontend Configuration Updates

### Flutter App (`frontend/lib/core/api/api_config.dart`)

The deployment system automatically updates the Flutter app's API configuration:

```dart
class ApiConfig {
  static String get baseUrl {
    // Updated by deployment system
    return 'https://wiggyz-backend.vercel.app/api/v1';
  }
}
```

### Admin Dashboard (`wiggyz_admin/.env.local`)

```env
NEXT_PUBLIC_API_URL=https://wiggyz-backend.vercel.app/api/v1
NODE_ENV=production
NEXT_PUBLIC_ENVIRONMENT=production
```

## API Endpoints

After deployment, the following endpoints will be available:

- **Health Check**: `https://your-deployment.vercel.app/health`
- **Authentication**: `https://your-deployment.vercel.app/api/v1/auth/*`
- **Matches**: `https://your-deployment.vercel.app/api/v1/matches/*`
- **Tournaments**: `https://your-deployment.vercel.app/api/v1/tournaments/*`
- **Wallet**: `https://your-deployment.vercel.app/api/v1/wallet/*`
- **Admin**: `https://your-deployment.vercel.app/api/v1/admin/*`

## Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clean and rebuild
   rm -rf dist
   npm run build
   ```

2. **Environment Variable Issues**
   ```bash
   # Check Vercel environment variables
   vercel env ls
   ```

3. **Timeout Issues**
   - Increase `maxDuration` in `vercel.json`
   - Optimize database queries
   - Check Supabase connection

4. **API URL Inconsistencies**
   ```bash
   # Reset environment configuration
   node scripts/deployment/environment-manager.js switch development
   node scripts/deployment/environment-manager.js switch production
   ```

### Logs and Monitoring

```bash
# View deployment logs
vercel logs

# View function logs
vercel logs --follow
```

## CI/CD Integration

For automated deployments, integrate with your CI/CD pipeline:

```yaml
# Example GitHub Actions workflow
name: Deploy to Vercel
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: cd backend && npm install
      - name: Deploy to Vercel
        run: cd backend && npm run deploy
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
```

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to version control
2. **CORS Configuration**: Ensure proper CORS settings for production
3. **Rate Limiting**: Configure appropriate rate limits
4. **JWT Secrets**: Use strong, unique secrets for production

## Performance Optimization

1. **Cold Start Optimization**: Keep bundle size under 50MB
2. **Database Connections**: Use connection pooling
3. **Caching**: Implement appropriate caching strategies
4. **Monitoring**: Set up performance monitoring

## Support

For deployment issues:

1. Check deployment logs: `vercel logs`
2. Validate configuration: `node scripts/deployment/validators.js pre`
3. Test endpoints manually
4. Review environment variables
5. Check Supabase connectivity

## Deployment URLs

After successful deployment, your backend is available at:

- **Production**: `https://wiggyz-backend-cas6rsy6m-tausifraja977-gmailcoms-projects.vercel.app`
- **Staging**: `https://wiggyz-backend-fxui5976h-tausifraja977-gmailcoms-projects.vercel.app`

### API Endpoints

- **Health Check**: `/health`
- **API Base**: `/api/v1/`
- **Authentication**: `/api/v1/auth/`
- **Matches**: `/api/v1/matches/`
- **Tournaments**: `/api/v1/tournaments/`
- **Wallet**: `/api/v1/wallet/`
- **Admin**: `/api/v1/admin/`

---

**Last Updated**: July 2025
**Version**: 1.0.0
