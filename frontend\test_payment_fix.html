<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WiggyZ Payment Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #FFCC00;
            color: black;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background-color: #e6b800; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WiggyZ Payment System Fix Test</h1>
        <p>This page tests the fixes for the critical payment system errors.</p>

        <div class="test-section">
            <h3>📋 System Status</h3>
            <div id="systemStatus" class="status info">Checking system status...</div>
            <div id="razorpayStatus" class="status info">Checking Razorpay SDK...</div>
            <div id="jsIntegrationStatus" class="status info">Checking JavaScript integration...</div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Payment Functions</h3>
            <button onclick="testRazorpayAvailability()">Test Razorpay SDK</button>
            <button onclick="testJavaScriptFunctions()">Test JS Functions</button>
            <button onclick="testPaymentFlow()">Test Payment Flow</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>📝 Console Logs</h3>
            <div id="consoleLogs" class="log">Logs will appear here...</div>
        </div>
    </div>

    <!-- Include the fixed Razorpay web integration -->
    <script src="razorpay_web.js"></script>

    <script>
        let testResults = [];
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('consoleLogs');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[Payment Test] ${message}`);
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function addTestResult(test, status, message) {
            testResults.push({ test, status, message, timestamp: new Date().toLocaleTimeString() });
            updateTestResults();
        }

        function updateTestResults() {
            const resultsElement = document.getElementById('testResults');
            resultsElement.innerHTML = testResults.map(result => 
                `<div class="status ${result.status}">
                    [${result.timestamp}] ${result.test}: ${result.message}
                </div>`
            ).join('');
        }

        function testRazorpayAvailability() {
            addLog('Testing Razorpay SDK availability...');
            
            if (typeof window.Razorpay !== 'undefined') {
                addLog('✅ Razorpay SDK is available', 'success');
                addTestResult('Razorpay SDK', 'success', 'SDK loaded successfully');
                return true;
            } else {
                addLog('❌ Razorpay SDK not found', 'error');
                addTestResult('Razorpay SDK', 'error', 'SDK not loaded');
                return false;
            }
        }

        function testJavaScriptFunctions() {
            addLog('Testing JavaScript function availability...');
            
            const tests = [
                { name: 'window.razorpayWeb', obj: window.razorpayWeb },
                { name: 'window.razorpayWeb.startPayment', obj: window.razorpayWeb?.startPayment },
                { name: 'window.startRazorpayWebPayment', obj: window.startRazorpayWebPayment },
                { name: 'loadRazorpayScript', obj: window.razorpayWeb?.loadScript },
            ];

            let allPassed = true;
            tests.forEach(test => {
                if (typeof test.obj === 'function' || (test.obj && typeof test.obj === 'object')) {
                    addLog(`✅ ${test.name} is available`, 'success');
                    addTestResult(`JS Function: ${test.name}`, 'success', 'Available');
                } else {
                    addLog(`❌ ${test.name} is missing`, 'error');
                    addTestResult(`JS Function: ${test.name}`, 'error', 'Missing');
                    allPassed = false;
                }
            });

            return allPassed;
        }

        function testPaymentFlow() {
            addLog('Testing payment flow initialization...');
            
            const testOptions = {
                key: 'rzp_test_iVqMylHzm5WcBc',
                amount: 10000, // ₹100 in paise
                currency: 'INR',
                name: 'WiggyZ Test',
                description: 'Test Payment - Fix Verification',
                order_id: 'test_order_' + Date.now(),
                prefill: {
                    name: 'Test User',
                    email: '<EMAIL>',
                    contact: '9999999999'
                },
                theme: {
                    color: '#FFCC00'
                },
                onSuccess: function(response) {
                    addLog('✅ Payment Success Callback Triggered', 'success');
                    addTestResult('Payment Flow', 'success', 'Success callback working');
                    console.log('Test Payment Success:', response);
                },
                onError: function(response) {
                    addLog('⚠️ Payment Error Callback Triggered', 'warning');
                    addTestResult('Payment Flow', 'warning', 'Error callback working');
                    console.log('Test Payment Error:', response);
                },
                onDismiss: function() {
                    addLog('ℹ️ Payment Dismiss Callback Triggered', 'info');
                    addTestResult('Payment Flow', 'info', 'Dismiss callback working');
                    console.log('Test Payment Dismissed');
                }
            };

            try {
                // Test the fixed function call
                if (window.razorpayWeb && window.razorpayWeb.startPayment) {
                    addLog('Testing razorpayWeb.startPayment...');
                    const result1 = window.razorpayWeb.startPayment(testOptions);
                    addLog(`razorpayWeb.startPayment returned: ${result1}`, result1 ? 'success' : 'error');
                    
                    if (result1) {
                        addTestResult('Payment Flow (razorpayWeb)', 'success', 'Function call successful');
                    } else {
                        addTestResult('Payment Flow (razorpayWeb)', 'error', 'Function call failed');
                    }
                }

                // Test the global function fallback
                if (window.startRazorpayWebPayment) {
                    addLog('Testing global startRazorpayWebPayment...');
                    const result2 = window.startRazorpayWebPayment(testOptions);
                    addLog(`startRazorpayWebPayment returned: ${result2}`, result2 ? 'success' : 'error');
                    
                    if (result2) {
                        addTestResult('Payment Flow (global)', 'success', 'Function call successful');
                    } else {
                        addTestResult('Payment Flow (global)', 'error', 'Function call failed');
                    }
                }

            } catch (error) {
                addLog(`❌ Payment flow test failed: ${error.message}`, 'error');
                addTestResult('Payment Flow', 'error', `Exception: ${error.message}`);
            }
        }

        function clearLogs() {
            document.getElementById('consoleLogs').textContent = 'Logs cleared...\n';
            testResults = [];
            updateTestResults();
        }

        // Initialize on page load
        window.onload = function() {
            addLog('🚀 Payment fix test page loaded');
            
            // Check system status
            updateStatus('systemStatus', 'System initialized', 'success');
            
            // Check Razorpay
            if (testRazorpayAvailability()) {
                updateStatus('razorpayStatus', 'Razorpay SDK loaded', 'success');
            } else {
                updateStatus('razorpayStatus', 'Razorpay SDK missing', 'error');
            }
            
            // Check JS integration
            if (testJavaScriptFunctions()) {
                updateStatus('jsIntegrationStatus', 'JavaScript integration working', 'success');
            } else {
                updateStatus('jsIntegrationStatus', 'JavaScript integration issues', 'error');
            }
            
            addLog('✅ All system checks completed');
        };
    </script>
</body>
</html>
