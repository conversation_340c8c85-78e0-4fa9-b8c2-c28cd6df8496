/**
 * Payment Security Middleware
 * Enhanced security measures specifically for payment-related endpoints
 */
import { Request, Response, NextFunction } from 'express';
import { RateLimiterRedis, RateLimiterMemory } from 'rate-limiter-flexible';
import { supabase } from '../config/supabase';
import { logger } from '../utils/logger';
import { getRedisClient } from '../config/redis';

// Payment-specific rate limiters
const redisClient = getRedisClient();

// Use Redis if available, otherwise fallback to memory
const paymentInitiationLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'payment_init',
      points: 5, // Number of requests
      duration: 300, // Per 5 minutes
      blockDuration: 900, // Block for 15 minutes
    })
  : new RateLimiterMemory({
      keyPrefix: 'payment_init',
      points: 5,
      duration: 300,
      blockDuration: 900,
    });

const paymentVerificationLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'payment_verify',
      points: 10, // Number of requests
      duration: 300, // Per 5 minutes
      blockDuration: 600, // Block for 10 minutes
    })
  : new RateLimiterMemory({
      keyPrefix: 'payment_verify',
      points: 10,
      duration: 300,
      blockDuration: 600,
    });

const webhookLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'webhook',
      points: 100, // Number of requests
      duration: 60, // Per minute
      blockDuration: 300, // Block for 5 minutes
    })
  : new RateLimiterMemory({
      keyPrefix: 'webhook',
      points: 100,
      duration: 60,
      blockDuration: 300,
    });

// Suspicious activity detection
const suspiciousActivityLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'suspicious_payment',
      points: 3, // Number of failed attempts
      duration: 3600, // Per hour
      blockDuration: 7200, // Block for 2 hours
    })
  : new RateLimiterMemory({
      keyPrefix: 'suspicious_payment',
      points: 3,
      duration: 3600,
      blockDuration: 7200,
    });

/**
 * Rate limiting for payment initiation
 */
export const paymentInitiationRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = `${req.ip}:${req.user?.userId || 'anonymous'}`;
    await paymentInitiationLimiter.consume(key);
    next();
  } catch (rejRes: any) {
    const remainingTime = Math.round(rejRes.msBeforeNext / 1000);
    logger.warn(`Payment initiation rate limit exceeded`, {
      ip: req.ip,
      userId: req.user?.userId,
      remainingTime
    });
    
    res.status(429).json({
      error: 'Too many payment requests',
      message: 'Please wait before initiating another payment',
      retryAfter: remainingTime
    });
  }
};

/**
 * Rate limiting for payment verification
 */
export const paymentVerificationRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = `${req.ip}:${req.user?.userId || 'anonymous'}`;
    await paymentVerificationLimiter.consume(key);
    next();
  } catch (rejRes: any) {
    const remainingTime = Math.round(rejRes.msBeforeNext / 1000);
    logger.warn(`Payment verification rate limit exceeded`, {
      ip: req.ip,
      userId: req.user?.userId,
      remainingTime
    });
    
    res.status(429).json({
      error: 'Too many verification requests',
      message: 'Please wait before verifying another payment',
      retryAfter: remainingTime
    });
  }
};

/**
 * Rate limiting for webhook endpoints
 */
export const webhookRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = req.ip || 'unknown';
    await webhookLimiter.consume(key);
    next();
  } catch (rejRes: any) {
    const remainingTime = Math.round(rejRes.msBeforeNext / 1000);
    logger.warn(`Webhook rate limit exceeded`, {
      ip: req.ip,
      remainingTime
    });
    
    res.status(429).json({
      error: 'Too many webhook requests',
      retryAfter: remainingTime
    });
  }
};

/**
 * Detect and prevent suspicious payment activity
 */
export const detectSuspiciousPaymentActivity = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.userId;
    const ip = req.ip;
    
    if (!userId) {
      return next();
    }

    // Check for multiple failed payments from same user/IP
    const key = `${ip}:${userId}`;
    
    // This middleware should be called after payment processing
    // to track failed attempts
    if (res.locals.paymentFailed) {
      try {
        await suspiciousActivityLimiter.consume(key);
      } catch (rejRes: any) {
        // User has exceeded suspicious activity threshold
        logger.error(`Suspicious payment activity detected`, {
          userId,
          ip,
          failedAttempts: rejRes.totalHits
        });

        // Block user temporarily and notify security team
        await blockUserTemporarily(userId, 'Suspicious payment activity');
        
        return res.status(403).json({
          error: 'Account temporarily restricted',
          message: 'Your account has been temporarily restricted due to suspicious activity. Please contact support.'
        });
      }
    }
    
    next();
  } catch (error: any) {
    logger.error(`Error in suspicious activity detection`, {
      error: error.message,
      userId: req.user?.userId,
      ip: req.ip
    });
    next(); // Continue even if detection fails
  }
};

/**
 * Validate payment amount and prevent manipulation
 */
export const validatePaymentAmount = (req: Request, res: Response, next: NextFunction) => {
  try {
    const { amount } = req.body;
    
    if (!amount || typeof amount !== 'number') {
      return res.status(400).json({
        error: 'Invalid amount',
        message: 'Amount must be a valid number'
      });
    }

    // Convert to paise for validation
    const amountInPaise = Math.round(amount * 100);
    
    // Validate amount limits with fee consideration
    const minAmount = parseInt(process.env.PAYMENT_MIN_AMOUNT || '2500'); // ₹25 (2500 paise)
    const maxAmount = parseInt(process.env.PAYMENT_MAX_AMOUNT || '10000000'); // ₹100,000

    // Calculate fees to ensure meaningful net amount
    const amountInRupees = amountInPaise / 100;
    const transactionFee = amountInRupees * 0.02; // 2% transaction fee
    const gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
    const totalDeductions = transactionFee + gstOnFee;
    const netAmount = amountInRupees - totalDeductions;

    if (amountInPaise < minAmount) {
      return res.status(400).json({
        error: 'Amount too low',
        message: `Minimum amount is ₹${minAmount / 100}`
      });
    }

    // Ensure net amount after fees is at least ₹20
    if (netAmount < 20) {
      return res.status(400).json({
        error: 'Amount too low after fees',
        message: `After deducting fees (₹${totalDeductions.toFixed(2)}), you would receive ₹${netAmount.toFixed(2)}. Please enter a higher amount to receive at least ₹20.`
      });
    }
    
    if (amountInPaise > maxAmount) {
      return res.status(400).json({
        error: 'Amount too high',
        message: `Maximum amount is ₹${maxAmount / 100}`
      });
    }

    // Store validated amount in paise
    req.body.amountInPaise = amountInPaise;
    next();
  } catch (error: any) {
    logger.error(`Payment amount validation failed`, {
      error: error.message,
      amount: req.body.amount,
      userId: req.user?.userId
    });
    
    res.status(400).json({
      error: 'Invalid payment data',
      message: 'Please check your payment details and try again'
    });
  }
};

/**
 * Validate withdrawal amount with specific withdrawal requirements
 */
export const validateWithdrawalAmount = (req: Request, res: Response, next: NextFunction) => {
  try {
    const { amount } = req.body;

    if (!amount || typeof amount !== 'number') {
      return res.status(400).json({
        error: 'Invalid amount',
        message: 'Amount must be a valid number'
      });
    }

    // Convert to paise for validation
    const amountInPaise = Math.round(amount * 100);

    // Validate withdrawal amount limits with fee consideration
    const minWithdrawalAmount = parseInt(process.env.WITHDRAWAL_MIN_AMOUNT || '10000'); // ₹100 (10000 paise)
    const maxWithdrawalAmount = parseInt(process.env.WITHDRAWAL_MAX_AMOUNT || '10000000'); // ₹100,000

    // Calculate fees to ensure meaningful net amount
    const amountInRupees = amountInPaise / 100;
    const transactionFee = amountInRupees * 0.02; // 2% transaction fee
    const gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
    const totalDeductions = transactionFee + gstOnFee;
    const netAmount = amountInRupees - totalDeductions;

    if (amountInPaise < minWithdrawalAmount) {
      return res.status(400).json({
        error: 'Amount too low',
        message: `Minimum withdrawal amount is ₹${minWithdrawalAmount / 100}`
      });
    }

    // Ensure net amount after fees is at least ₹80 for withdrawals
    if (netAmount < 80) {
      return res.status(400).json({
        error: 'Amount too low after fees',
        message: `After deducting fees (₹${totalDeductions.toFixed(2)}), you would receive ₹${netAmount.toFixed(2)}. Please enter a higher amount to receive at least ₹80.`
      });
    }

    if (amountInPaise > maxWithdrawalAmount) {
      return res.status(400).json({
        error: 'Amount too high',
        message: `Maximum withdrawal amount is ₹${maxWithdrawalAmount / 100}`
      });
    }

    // Store validated amount in paise
    req.body.amountInPaise = amountInPaise;
    next();
  } catch (error: any) {
    logger.error(`Withdrawal amount validation failed`, {
      error: error.message,
      amount: req.body.amount,
      userId: req.user?.userId
    });

    res.status(400).json({
      error: 'Invalid withdrawal data',
      message: 'Please check your withdrawal details and try again'
    });
  }
};

/**
 * Log payment operations for audit trail
 */
export const auditPaymentOperation = (operation: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    
    // Log request
    logger.info(`Payment operation started: ${operation}`, {
      operation,
      userId: req.user?.userId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      amount: req.body.amount,
      currency: req.body.currency,
      paymentMethod: req.body.payment_method,
      timestamp: new Date().toISOString()
    });

    // Override res.json to log response
    const originalJson = res.json;
    res.json = function(body: any) {
      const duration = Date.now() - startTime;
      const success = res.statusCode >= 200 && res.statusCode < 300;
      
      logger.info(`Payment operation completed: ${operation}`, {
        operation,
        userId: req.user?.userId,
        ip: req.ip,
        statusCode: res.statusCode,
        success,
        duration,
        timestamp: new Date().toISOString()
      });

      // Store payment result for suspicious activity detection
      if (!success && operation === 'payment_verification') {
        res.locals.paymentFailed = true;
      }

      return originalJson.call(this, body);
    };

    next();
  };
};

/**
 * Temporarily block a user account
 */
async function blockUserTemporarily(userId: string, reason: string): Promise<void> {
  try {
    const blockUntil = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours
    
    await supabase
      .from('user_security_blocks')
      .insert({
        user_id: userId,
        reason,
        blocked_until: blockUntil.toISOString(),
        created_at: new Date().toISOString()
      });

    logger.warn(`User temporarily blocked`, {
      userId,
      reason,
      blockedUntil: blockUntil.toISOString()
    });
  } catch (error: any) {
    logger.error(`Failed to block user temporarily`, {
      userId,
      reason,
      error: error.message
    });
  }
}

/**
 * Check if user is temporarily blocked
 */
export const checkUserBlocked = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return next();
    }

    const { data: block } = await supabase
      .from('user_security_blocks')
      .select('blocked_until, reason')
      .eq('user_id', userId)
      .gte('blocked_until', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (block) {
      logger.warn(`Blocked user attempted payment operation`, {
        userId,
        reason: block.reason,
        blockedUntil: block.blocked_until
      });

      return res.status(403).json({
        error: 'Account temporarily restricted',
        message: 'Your account is temporarily restricted. Please contact support.',
        blockedUntil: block.blocked_until
      });
    }

    next();
  } catch (error: any) {
    // If there's an error checking blocks, log it but continue
    logger.error(`Error checking user blocks`, {
      userId: req.user?.userId,
      error: error.message
    });
    next();
  }
};
