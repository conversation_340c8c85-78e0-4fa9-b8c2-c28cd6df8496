import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wiggyz_app/features/tournament_service.dart';

enum TournamentMatchPhase {
  scheduled,    // Tournament is scheduled but not started
  active,       // Tournament match is active (players can play)
  ended,        // Match has ended, waiting for result submission
  submission,   // Result submission phase (30-minute window)
  verification, // Results submitted, waiting for admin verification
  completed     // Tournament completed with final results
}

class TournamentMatchState {
  final String tournamentId;
  final TournamentMatchPhase phase;
  final DateTime? matchStartTime;
  final DateTime? matchEndTime;
  final DateTime? submissionDeadline;
  final Duration? remainingSubmissionTime;
  final bool canStartMatch;
  final bool canEndMatch;
  final bool canSubmitResults;
  final bool hasSubmittedResults;
  final String? verificationStatus;

  TournamentMatchState({
    required this.tournamentId,
    required this.phase,
    this.matchStartTime,
    this.matchEndTime,
    this.submissionDeadline,
    this.remainingSubmissionTime,
    this.canStartMatch = false,
    this.canEndMatch = false,
    this.canSubmitResults = false,
    this.hasSubmittedResults = false,
    this.verificationStatus,
  });

  TournamentMatchState copyWith({
    TournamentMatchPhase? phase,
    DateTime? matchStartTime,
    DateTime? matchEndTime,
    DateTime? submissionDeadline,
    Duration? remainingSubmissionTime,
    bool? canStartMatch,
    bool? canEndMatch,
    bool? canSubmitResults,
    bool? hasSubmittedResults,
    String? verificationStatus,
  }) {
    return TournamentMatchState(
      tournamentId: tournamentId,
      phase: phase ?? this.phase,
      matchStartTime: matchStartTime ?? this.matchStartTime,
      matchEndTime: matchEndTime ?? this.matchEndTime,
      submissionDeadline: submissionDeadline ?? this.submissionDeadline,
      remainingSubmissionTime: remainingSubmissionTime ?? this.remainingSubmissionTime,
      canStartMatch: canStartMatch ?? this.canStartMatch,
      canEndMatch: canEndMatch ?? this.canEndMatch,
      canSubmitResults: canSubmitResults ?? this.canSubmitResults,
      hasSubmittedResults: hasSubmittedResults ?? this.hasSubmittedResults,
      verificationStatus: verificationStatus ?? this.verificationStatus,
    );
  }
}

class TournamentMatchStateProvider with ChangeNotifier {
  final TournamentService _tournamentService = TournamentService();
  final Map<String, TournamentMatchState> _matchStates = {};
  final Map<String, Timer> _submissionTimers = {};
  
  bool _isLoading = false;
  String? _error;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;

  TournamentMatchState? getMatchState(String tournamentId) {
    return _matchStates[tournamentId];
  }

  // Initialize match state for a tournament
  Future<void> initializeMatchState(String tournamentId, {
    required bool isCreator,
    required bool isParticipant,
    required String tournamentStatus,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Check if user has already submitted results
      bool hasSubmittedResults = false;
      if (isParticipant) {
        final submissionStatus = await _tournamentService.checkTournamentSubmissionStatus(tournamentId);
        if (submissionStatus['success'] == true) {
          hasSubmittedResults = submissionStatus['data']['hasSubmitted'] ?? false;
        }
      }

      // Determine initial phase based on tournament status
      TournamentMatchPhase phase;
      switch (tournamentStatus.toLowerCase()) {
        case 'upcoming':
          phase = TournamentMatchPhase.scheduled;
          break;
        case 'active':
          phase = hasSubmittedResults ? TournamentMatchPhase.verification : TournamentMatchPhase.submission;
          break;
        case 'completed':
          phase = TournamentMatchPhase.completed;
          break;
        default:
          phase = TournamentMatchPhase.scheduled;
      }

      // Create initial state
      final state = TournamentMatchState(
        tournamentId: tournamentId,
        phase: phase,
        canStartMatch: isCreator && phase == TournamentMatchPhase.scheduled,
        canEndMatch: isCreator && (phase == TournamentMatchPhase.submission),
        canSubmitResults: isParticipant && phase == TournamentMatchPhase.submission && !hasSubmittedResults,
        hasSubmittedResults: hasSubmittedResults,
        verificationStatus: hasSubmittedResults ? 'pending' : null,
      );

      _matchStates[tournamentId] = state;

      // If in submission phase, start countdown timer
      if (phase == TournamentMatchPhase.submission) {
        _startSubmissionTimer(tournamentId);
      }

      // Always refresh submission status to ensure accuracy
      await updateSubmissionStatus(tournamentId);

    } catch (e) {
      _error = 'Failed to initialize match state: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Start tournament match
  Future<void> startTournamentMatch(String tournamentId, String roomId, String roomPassword) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Call backend to start match
      final result = await _tournamentService.startTournamentMatch(
        tournamentId,
        roomId: roomId,
        roomPassword: roomPassword,
      );

      if (result['success'] == true) {
        // Update state to submission phase (active match)
        final currentState = _matchStates[tournamentId];
        if (currentState != null) {
          _matchStates[tournamentId] = currentState.copyWith(
            phase: TournamentMatchPhase.submission,
            matchStartTime: DateTime.now(),
            canStartMatch: false,
            canEndMatch: true,
          );
        }
      } else {
        throw Exception(result['error'] ?? 'Failed to start tournament match');
      }

    } catch (e) {
      _error = 'Failed to start match: $e';
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // End tournament match
  Future<void> endTournamentMatch(String tournamentId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final currentState = _matchStates[tournamentId];
      if (currentState == null) return;

      // Update state to ended and start submission phase
      final matchEndTime = DateTime.now();
      final submissionDeadline = matchEndTime.add(const Duration(minutes: 30));

      _matchStates[tournamentId] = currentState.copyWith(
        phase: TournamentMatchPhase.submission,
        matchEndTime: matchEndTime,
        submissionDeadline: submissionDeadline,
        canEndMatch: false,
        canSubmitResults: true,
      );

      // Start 30-minute countdown timer
      _startSubmissionTimer(tournamentId);

    } catch (e) {
      _error = 'Failed to end match: $e';
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Submit tournament results
  Future<void> submitTournamentResults(String tournamentId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final currentState = _matchStates[tournamentId];
      if (currentState == null) return;

      // Update state to verification phase
      _matchStates[tournamentId] = currentState.copyWith(
        phase: TournamentMatchPhase.verification,
        hasSubmittedResults: true,
        canSubmitResults: false,
        verificationStatus: 'pending',
      );

      // Cancel submission timer
      _submissionTimers[tournamentId]?.cancel();
      _submissionTimers.remove(tournamentId);

    } catch (e) {
      _error = 'Failed to submit results: $e';
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Start 30-minute submission countdown timer
  void _startSubmissionTimer(String tournamentId) {
    _submissionTimers[tournamentId]?.cancel();
    
    _submissionTimers[tournamentId] = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        final state = _matchStates[tournamentId];
        if (state?.submissionDeadline == null) {
          timer.cancel();
          return;
        }

        final now = DateTime.now();
        final deadline = state!.submissionDeadline!;
        final remaining = deadline.difference(now);

        if (remaining.isNegative) {
          // Time expired - automatically mark as lost
          timer.cancel();
          _submissionTimers.remove(tournamentId);
          
          _matchStates[tournamentId] = state.copyWith(
            phase: TournamentMatchPhase.completed,
            canSubmitResults: false,
            verificationStatus: 'expired',
          );
          notifyListeners();
        } else {
          // Update remaining time
          _matchStates[tournamentId] = state.copyWith(
            remainingSubmissionTime: remaining,
          );
          notifyListeners();
        }
      },
    );
  }

  // Get formatted remaining time string
  String getFormattedRemainingTime(String tournamentId) {
    final state = _matchStates[tournamentId];
    final remaining = state?.remainingSubmissionTime;
    
    if (remaining == null) return '';
    
    final minutes = remaining.inMinutes;
    final seconds = remaining.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // Check if user has submitted results for a tournament
  Future<bool> hasUserSubmittedResults(String tournamentId) async {
    try {
      print('=== Checking Submission Status API ===');
      print('Tournament ID: $tournamentId');

      final submissionStatus = await _tournamentService.checkTournamentSubmissionStatus(tournamentId);

      print('API Response: $submissionStatus');

      if (submissionStatus['success'] == true) {
        final hasSubmitted = submissionStatus['data']['hasSubmitted'] ?? false;
        print('Has Submitted Results: $hasSubmitted');
        return hasSubmitted;
      } else {
        print('API call failed: ${submissionStatus['message'] ?? 'Unknown error'}');
        return false;
      }
    } catch (e) {
      print('Error checking submission status: $e');
      return false;
    }
  }

  // Update submission status for a tournament
  Future<void> updateSubmissionStatus(String tournamentId) async {
    try {
      print('=== Updating Submission Status ===');
      print('Tournament ID: $tournamentId');

      final hasSubmitted = await hasUserSubmittedResults(tournamentId);
      final currentState = _matchStates[tournamentId];

      print('Current State: $currentState');
      print('Has Submitted (API): $hasSubmitted');
      print('Has Submitted (Current State): ${currentState?.hasSubmittedResults}');

      if (currentState != null) {
        if (hasSubmitted != currentState.hasSubmittedResults) {
          print('Updating state - submission status changed');
          _matchStates[tournamentId] = currentState.copyWith(
            hasSubmittedResults: hasSubmitted,
            canSubmitResults: !hasSubmitted,
            phase: hasSubmitted ? TournamentMatchPhase.verification : currentState.phase,
            verificationStatus: hasSubmitted ? 'pending' : currentState.verificationStatus,
          );
          notifyListeners();
          print('State updated and listeners notified');
        } else {
          print('No state change needed - submission status unchanged');
        }
      } else {
        print('Warning: No current state found for tournament $tournamentId');
      }
    } catch (e) {
      print('Error updating submission status: $e');
    }
  }

  // Clean up resources
  @override
  void dispose() {
    for (final timer in _submissionTimers.values) {
      timer.cancel();
    }
    _submissionTimers.clear();
    super.dispose();
  }

  // Clear state for a specific tournament
  void clearTournamentState(String tournamentId) {
    _submissionTimers[tournamentId]?.cancel();
    _submissionTimers.remove(tournamentId);
    _matchStates.remove(tournamentId);
    notifyListeners();
  }
}
