import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../core/api/api_config.dart';
import '../models/user_activity.dart';
import '../providers/auth_provider.dart';

/// Service for managing user activities
class ActivitiesService {
  static const String _cacheKey = 'user_activities_cache';
  static const String _cacheTimestampKey = 'user_activities_cache_timestamp';
  static const Duration _cacheExpiry = Duration(minutes: 5);

  final AuthProvider _authProvider;

  ActivitiesService(this._authProvider);

  /// Get user activities with pagination
  Future<PaginatedActivities?> getUserActivities({
    int limit = 20,
    int offset = 0,
    String? activityType,
    bool forceRefresh = false,
  }) async {
    try {
      // Check cache for recent activities (first page only)
      if (!forceRefresh && offset == 0 && activityType == null) {
        final cachedActivities = await _getCachedActivities();
        if (cachedActivities != null) {
          debugPrint('Returning cached user activities');
          return cachedActivities;
        }
      }

      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return null;
      }

      // Build query parameters
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };
      if (activityType != null) {
        queryParams['type'] = activityType;
      }

      final uri = Uri.parse(
        '${ApiConfig.baseUrl}/profile/activities',
      ).replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final result = PaginatedActivities.fromJson(data['data']);

          // Cache the result if it's the first page
          if (offset == 0 && activityType == null) {
            await _cacheActivities(result);
          }

          debugPrint('Successfully fetched user activities');
          return result;
        }
      } else {
        debugPrint('Failed to fetch user activities: ${response.statusCode}');
        debugPrint('Response: ${response.body}');
      }

      return null;
    } catch (e) {
      debugPrint('Error fetching user activities: $e');
      return null;
    }
  }

  /// Get recent activities (convenience method)
  Future<List<UserActivity>> getRecentActivities({int limit = 10}) async {
    try {
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return [];
      }

      final response = await http.get(
        Uri.parse(
          '${ApiConfig.baseUrl}/profile/activities/recent?limit=$limit',
        ),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final activitiesList = data['data'] as List;
          debugPrint('Successfully fetched recent activities');
          return activitiesList
              .map((item) => UserActivity.fromJson(item))
              .toList();
        }
      } else {
        debugPrint('Failed to fetch recent activities: ${response.statusCode}');
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching recent activities: $e');
      return [];
    }
  }

  /// Get activities by type
  Future<List<UserActivity>> getActivitiesByType(
    String activityType, {
    int limit = 10,
  }) async {
    try {
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return [];
      }

      final response = await http.get(
        Uri.parse(
          '${ApiConfig.baseUrl}/profile/activities/type/$activityType?limit=$limit',
        ),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final activitiesList = data['data'] as List;
          debugPrint('Successfully fetched activities by type: $activityType');
          return activitiesList
              .map((item) => UserActivity.fromJson(item))
              .toList();
        }
      } else {
        debugPrint(
          'Failed to fetch activities by type: ${response.statusCode}',
        );
      }

      return [];
    } catch (e) {
      debugPrint('Error fetching activities by type: $e');
      return [];
    }
  }

  /// Get activity statistics
  Future<Map<String, int>?> getActivityStatistics() async {
    try {
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return null;
      }

      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/profile/activities/statistics'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          debugPrint('Successfully fetched activity statistics');
          return Map<String, int>.from(data['data']);
        }
      } else {
        debugPrint(
          'Failed to fetch activity statistics: ${response.statusCode}',
        );
      }

      return null;
    } catch (e) {
      debugPrint('Error fetching activity statistics: $e');
      return null;
    }
  }

  /// Create a new activity (for internal use)
  Future<UserActivity?> createActivity({
    required String activityType,
    required String title,
    String? description,
    double? amount,
    String? relatedEntityId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return null;
      }

      final requestBody = <String, dynamic>{
        'activityType': activityType,
        'title': title,
      };

      if (description != null) requestBody['description'] = description;
      if (amount != null) requestBody['amount'] = amount;
      if (relatedEntityId != null) {
        requestBody['relatedEntityId'] = relatedEntityId;
      }
      if (metadata != null) requestBody['metadata'] = metadata;

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/profile/activities'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          debugPrint('Successfully created activity');

          // Clear cache to force refresh
          await _clearCache();

          return UserActivity.fromJson(data['data']);
        }
      } else {
        debugPrint('Failed to create activity: ${response.statusCode}');
        debugPrint('Response: ${response.body}');
      }

      return null;
    } catch (e) {
      debugPrint('Error creating activity: $e');
      return null;
    }
  }

  /// Create match win activity
  Future<UserActivity?> createMatchWinActivity(
    String matchId, {
    double? prizeAmount,
  }) async {
    try {
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return null;
      }

      final requestBody = <String, dynamic>{'matchId': matchId};

      if (prizeAmount != null) requestBody['prizeAmount'] = prizeAmount;

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/profile/activities/match-win'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          debugPrint('Successfully created match win activity');

          // Clear cache to force refresh
          await _clearCache();

          return UserActivity.fromJson(data['data']);
        }
      } else {
        debugPrint(
          'Failed to create match win activity: ${response.statusCode}',
        );
      }

      return null;
    } catch (e) {
      debugPrint('Error creating match win activity: $e');
      return null;
    }
  }

  /// Create achievement unlock activity
  Future<UserActivity?> createAchievementUnlockActivity(
    String achievementId,
    String achievementTitle, {
    int? rewardPoints,
  }) async {
    try {
      final token = await _authProvider.authService.getToken();
      if (token == null) {
        debugPrint('No authentication token available');
        return null;
      }

      final requestBody = <String, dynamic>{
        'achievementId': achievementId,
        'achievementTitle': achievementTitle,
      };

      if (rewardPoints != null) requestBody['rewardPoints'] = rewardPoints;

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/profile/activities/achievement-unlock'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          debugPrint('Successfully created achievement unlock activity');

          // Clear cache to force refresh
          await _clearCache();

          return UserActivity.fromJson(data['data']);
        }
      } else {
        debugPrint(
          'Failed to create achievement unlock activity: ${response.statusCode}',
        );
      }

      return null;
    } catch (e) {
      debugPrint('Error creating achievement unlock activity: $e');
      return null;
    }
  }

  /// Cache activities locally
  Future<void> _cacheActivities(PaginatedActivities activities) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activitiesJson = json.encode(activities.toJson());
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setString(_cacheKey, activitiesJson);
      await prefs.setInt(_cacheTimestampKey, timestamp);

      debugPrint('Cached user activities');
    } catch (e) {
      debugPrint('Error caching activities: $e');
    }
  }

  /// Get cached activities if not expired
  Future<PaginatedActivities?> _getCachedActivities() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activitiesJson = prefs.getString(_cacheKey);
      final timestamp = prefs.getInt(_cacheTimestampKey);

      if (activitiesJson != null && timestamp != null) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
        final cacheExpired = cacheAge > _cacheExpiry.inMilliseconds;

        if (!cacheExpired) {
          final activitiesData = json.decode(activitiesJson);
          return PaginatedActivities.fromJson(activitiesData);
        } else {
          debugPrint('Activities cache expired');
          await _clearCache();
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error reading cached activities: $e');
      return null;
    }
  }

  /// Clear cached activities
  Future<void> _clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimestampKey);
      debugPrint('Cleared activities cache');
    } catch (e) {
      debugPrint('Error clearing activities cache: $e');
    }
  }

  /// Clear all cached data (for logout)
  Future<void> clearAllCache() async {
    await _clearCache();
  }
}
