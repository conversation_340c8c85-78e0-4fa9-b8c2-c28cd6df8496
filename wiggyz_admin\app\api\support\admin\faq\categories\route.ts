import { NextRequest, NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5000'

// Helper function to get admin token (you may need to adjust this based on your auth system)
function getAdminToken(request: NextRequest) {
  // This is a placeholder - implement your actual admin authentication
  return request.headers.get('authorization') || 'Bearer admin-token'
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const token = getAdminToken(request)

    const response = await fetch(`${BACKEND_URL}/api/v1/support/admin/faq/categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token,
      },
      body: JSON.stringify(body),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `Backend responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error creating FAQ category:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create FAQ category' },
      { status: 500 }
    )
  }
}
