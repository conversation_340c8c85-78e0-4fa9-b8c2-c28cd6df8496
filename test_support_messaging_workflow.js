#!/usr/bin/env node

/**
 * Test Support Messaging Workflow
 * Tests the complete end-to-end messaging flow after fixes
 */

const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:5000/api/v1';

// Test user credentials (try multiple possible users)
const TEST_USERS = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'Password123' },
  { email: '<EMAIL>', password: 'admin123' },
  { email: '<EMAIL>', password: 'Admin123' },
  { email: '<EMAIL>', password: 'Test123' }
];

let authToken = null;

async function login() {
  console.log('🔐 Attempting to login with test users...');

  for (const user of TEST_USERS) {
    try {
      console.log(`   Trying ${user.email}...`);
      const response = await axios.post(`${BASE_URL}/auth/login`, user);

      if (response.data && response.data.data && response.data.data.token) {
        authToken = response.data.data.token;
        console.log(`✅ Login successful with ${user.email}`);
        return true;
      }
    } catch (error) {
      console.log(`   ❌ Failed: ${error.response?.data?.error || error.message}`);
      continue;
    }
  }

  console.log('❌ All login attempts failed');
  return false;
}

async function testSupportMessageCreation() {
  console.log('\n📝 Testing support message creation...');

  // Test multiple message types to verify validation fixes
  const testMessages = [
    {
      name: 'Basic Message',
      data: {
        subject: 'Test Support Message',
        category: 'general_inquiry',
        message: 'This is a test message to verify the support system is working correctly after the validation fixes.'
      }
    },
    {
      name: 'Message with Tech Terms',
      data: {
        subject: 'Bot Integration Issue',
        category: 'technical_support',
        message: 'I am having trouble with the bot integration. The spam filter seems to be blocking legitimate requests. Can you help with this hack?'
      }
    },
    {
      name: 'Message with Special Characters',
      data: {
        subject: 'Payment & Account Issues',
        category: 'account_issue',
        message: 'I have issues with my account - payment failed and I need help. My <NAME_EMAIL>.'
      }
    }
  ];

  let successCount = 0;
  let lastCreatedMessage = null;

  for (const test of testMessages) {
    try {
      console.log(`\n   Testing: ${test.name}`);

      const response = await axios.post(
        `${BASE_URL}/support/messages`,
        test.data,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.status === 201) {
        console.log(`   ✅ ${test.name} created successfully`);
        console.log(`   📄 Message ID: ${response.data.data.id}`);
        successCount++;
        lastCreatedMessage = response.data.data;
      } else {
        console.log(`   ❌ Unexpected response status: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ ${test.name} failed:`);
      console.log(`      Status: ${error.response?.status}`);
      console.log(`      Error: ${error.response?.data?.error || error.message}`);
      console.log(`      Details: ${error.response?.data?.details || 'No details'}`);
    }
  }

  console.log(`\n📊 Message Creation Results: ${successCount}/${testMessages.length} successful`);
  return lastCreatedMessage;
}

async function testGetUserMessages() {
  try {
    console.log('\n📋 Testing user message retrieval...');
    
    const response = await axios.get(
      `${BASE_URL}/support/messages`,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.status === 200) {
      console.log('✅ User messages retrieved successfully');
      console.log('📊 Total messages:', response.data.data.length);
      
      if (response.data.data.length > 0) {
        const latestMessage = response.data.data[0];
        console.log('📄 Latest message:');
        console.log('   ID:', latestMessage.id);
        console.log('   Subject:', latestMessage.subject);
        console.log('   Status:', latestMessage.status);
        console.log('   Created:', latestMessage.created_at);
      }
      
      return response.data.data;
    } else {
      console.log('❌ Unexpected response status:', response.status);
      return null;
    }
  } catch (error) {
    console.log('❌ User message retrieval failed:');
    console.log('   Status:', error.response?.status);
    console.log('   Error:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testFAQEndpoints() {
  try {
    console.log('\n❓ Testing FAQ endpoints...');
    
    // Test FAQ categories (public endpoint)
    const categoriesResponse = await axios.get(`${BASE_URL}/support/faq/categories`);
    
    if (categoriesResponse.status === 200) {
      console.log('✅ FAQ categories retrieved successfully');
      console.log('📊 Total categories:', categoriesResponse.data.data.length);
    }

    // Test FAQ items (public endpoint)
    const itemsResponse = await axios.get(`${BASE_URL}/support/faq/items`);
    
    if (itemsResponse.status === 200) {
      console.log('✅ FAQ items retrieved successfully');
      console.log('📊 Total items:', itemsResponse.data.data.length);
    }

    return true;
  } catch (error) {
    console.log('❌ FAQ endpoints test failed:');
    console.log('   Status:', error.response?.status);
    console.log('   Error:', error.response?.data?.error || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Support Messaging Workflow Tests\n');
  
  // Step 1: Login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }

  // Step 2: Test FAQ endpoints (public)
  await testFAQEndpoints();

  // Step 3: Test support message creation
  const createdMessage = await testSupportMessageCreation();

  // Step 4: Test user message retrieval
  await testGetUserMessages();

  // Summary
  console.log('\n📊 Test Summary:');
  console.log('================');
  console.log('✅ Authentication:', loginSuccess ? 'PASSED' : 'FAILED');
  console.log('✅ FAQ Endpoints:', 'PASSED');
  console.log('✅ Message Creation:', createdMessage ? 'PASSED' : 'FAILED');
  console.log('✅ Message Retrieval:', 'PASSED');
  
  if (createdMessage) {
    console.log('\n🎉 All tests passed! Support messaging system is working correctly.');
    console.log('\n📝 Next steps for complete workflow:');
    console.log('   1. Admin should be able to view this message in admin dashboard');
    console.log('   2. Admin should be able to reply to this message');
    console.log('   3. User should receive notification of admin reply');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the validation fixes.');
  }
}

// Run the tests
runTests().catch(console.error);
