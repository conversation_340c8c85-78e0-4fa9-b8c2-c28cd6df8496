/**
 * Integration test script for match refund logic
 * This script creates test scenarios and verifies the refund functionality
 */

import { supabase } from '../../../config/supabase';
import { MatchDeadlineService } from '../services/matchDeadlineService';
import { walletService } from '../../wallet/services/walletService';

interface TestScenario {
  name: string;
  setup: () => Promise<{ matchId: string; userId: string; cleanup: () => Promise<void> }>;
  expectedRefund: boolean;
  description: string;
}

class RefundLogicTester {
  private matchDeadlineService: MatchDeadlineService;

  constructor() {
    this.matchDeadlineService = new MatchDeadlineService();
  }

  async runTests(): Promise<void> {
    console.log('🧪 Starting Match Refund Logic Integration Tests\n');

    const scenarios: TestScenario[] = [
      {
        name: 'Scenario 1: Eligible for Refund',
        description: 'Match with entry fee, no participants except creator, deadline expired',
        expectedRefund: true,
        setup: () => this.setupEligibleMatch()
      },
      {
        name: 'Scenario 2: Not Eligible - No Entry Fee',
        description: 'Match with no entry fee, no participants',
        expectedRefund: false,
        setup: () => this.setupNoEntryFeeMatch()
      },
      {
        name: 'Scenario 3: Not Eligible - Has Participants',
        description: 'Match with entry fee but has other participants',
        expectedRefund: false,
        setup: () => this.setupMatchWithParticipants()
      }
    ];

    for (const scenario of scenarios) {
      await this.runScenario(scenario);
    }

    console.log('\n✅ All refund logic tests completed!');
  }

  private async runScenario(scenario: TestScenario): Promise<void> {
    console.log(`\n📋 ${scenario.name}`);
    console.log(`   ${scenario.description}`);

    try {
      const { matchId, userId, cleanup } = await scenario.setup();
      
      // Get initial wallet balance
      const initialWallet = await walletService.getWalletDetails(userId);
      console.log(`   Initial wallet balance: ${initialWallet.balance}`);

      // Test database function eligibility
      const { data: isEligible, error: eligibilityError } = await supabase
        .rpc('is_match_eligible_for_creator_refund', { match_id_param: matchId });

      if (eligibilityError) {
        throw new Error(`Eligibility check failed: ${eligibilityError.message}`);
      }

      console.log(`   Eligibility check: ${isEligible ? '✅ Eligible' : '❌ Not eligible'}`);

      if (isEligible !== scenario.expectedRefund) {
        throw new Error(`Expected eligibility: ${scenario.expectedRefund}, got: ${isEligible}`);
      }

      if (isEligible) {
        // Test the refund processing
        const { data: refundResult, error: refundError } = await supabase
          .rpc('process_creator_refund', { match_id_param: matchId });

        if (refundError) {
          throw new Error(`Refund processing failed: ${refundError.message}`);
        }

        if (!refundResult?.success) {
          throw new Error(`Refund failed: ${refundResult?.error || 'Unknown error'}`);
        }

        console.log(`   Refund processed: ${refundResult.refund_amount} refunded`);

        // Verify wallet balance increased
        const finalWallet = await walletService.getWalletDetails(userId);
        const expectedBalance = initialWallet.balance + refundResult.refund_amount;
        
        if (Math.abs(finalWallet.balance - expectedBalance) > 0.01) {
          throw new Error(`Wallet balance mismatch. Expected: ${expectedBalance}, got: ${finalWallet.balance}`);
        }

        console.log(`   Final wallet balance: ${finalWallet.balance} ✅`);

        // Verify match refund fields are updated
        const { data: updatedMatch, error: matchError } = await supabase
          .from('matches')
          .select('refund_processed, refund_amount, refund_timestamp')
          .eq('id', matchId)
          .single();

        if (matchError) {
          throw new Error(`Failed to verify match update: ${matchError.message}`);
        }

        if (!updatedMatch.refund_processed) {
          throw new Error('Match refund_processed field not updated');
        }

        if (updatedMatch.refund_amount !== refundResult.refund_amount) {
          throw new Error(`Match refund_amount mismatch. Expected: ${refundResult.refund_amount}, got: ${updatedMatch.refund_amount}`);
        }

        console.log(`   Match fields updated correctly ✅`);
      }

      console.log(`   ✅ ${scenario.name} passed`);
      
      // Cleanup
      await cleanup();

    } catch (error) {
      console.error(`   ❌ ${scenario.name} failed: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  private async setupEligibleMatch(): Promise<{ matchId: string; userId: string; cleanup: () => Promise<void> }> {
    // Create a test user
    const { data: user, error: userError } = await supabase.auth.admin.createUser({
      email: `test-refund-${Date.now()}@example.com`,
      password: 'testpassword123',
      email_confirm: true
    });

    if (userError || !user.user) {
      throw new Error(`Failed to create test user: ${userError?.message}`);
    }

    const userId = user.user.id;

    // Ensure user has a wallet with sufficient balance
    await walletService.addBalance(userId, 1000, 'Test setup');

    // Create a match with entry fee
    const { data: match, error: matchError } = await supabase
      .from('matches')
      .insert({
        game_id: 1,
        created_by: userId,
        entry_fee: 50,
        max_participants: 4,
        match_type: 'standalone',
        game_format: 'solo',
        match_mode: 'ranked',
        title: 'Test Refund Match',
        status: 'completed', // Simulate expired match
        result_submission_deadline: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        refund_processed: false
      })
      .select('id')
      .single();

    if (matchError || !match) {
      throw new Error(`Failed to create test match: ${matchError?.message}`);
    }

    // Add creator as participant
    await supabase
      .from('match_participants')
      .insert({
        match_id: match.id,
        user_id: userId,
        participant_type: 'player'
      });

    return {
      matchId: match.id,
      userId,
      cleanup: async () => {
        // Clean up test data
        await supabase.from('match_participants').delete().eq('match_id', match.id);
        await supabase.from('matches').delete().eq('id', match.id);
        await supabase.auth.admin.deleteUser(userId);
      }
    };
  }

  private async setupNoEntryFeeMatch(): Promise<{ matchId: string; userId: string; cleanup: () => Promise<void> }> {
    // Similar setup but with no entry fee
    const { data: user, error: userError } = await supabase.auth.admin.createUser({
      email: `test-no-fee-${Date.now()}@example.com`,
      password: 'testpassword123',
      email_confirm: true
    });

    if (userError || !user.user) {
      throw new Error(`Failed to create test user: ${userError?.message}`);
    }

    const userId = user.user.id;

    const { data: match, error: matchError } = await supabase
      .from('matches')
      .insert({
        game_id: 1,
        created_by: userId,
        entry_fee: 0, // No entry fee
        max_participants: 4,
        match_type: 'standalone',
        game_format: 'solo',
        match_mode: 'ranked',
        title: 'Test No Fee Match',
        status: 'completed',
        result_submission_deadline: new Date(Date.now() - 3600000).toISOString(),
        refund_processed: false
      })
      .select('id')
      .single();

    if (matchError || !match) {
      throw new Error(`Failed to create test match: ${matchError?.message}`);
    }

    await supabase
      .from('match_participants')
      .insert({
        match_id: match.id,
        user_id: userId,
        participant_type: 'player'
      });

    return {
      matchId: match.id,
      userId,
      cleanup: async () => {
        await supabase.from('match_participants').delete().eq('match_id', match.id);
        await supabase.from('matches').delete().eq('id', match.id);
        await supabase.auth.admin.deleteUser(userId);
      }
    };
  }

  private async setupMatchWithParticipants(): Promise<{ matchId: string; userId: string; cleanup: () => Promise<void> }> {
    // Setup with multiple participants
    const { data: creator, error: creatorError } = await supabase.auth.admin.createUser({
      email: `test-creator-${Date.now()}@example.com`,
      password: 'testpassword123',
      email_confirm: true
    });

    const { data: participant, error: participantError } = await supabase.auth.admin.createUser({
      email: `test-participant-${Date.now()}@example.com`,
      password: 'testpassword123',
      email_confirm: true
    });

    if (creatorError || !creator.user || participantError || !participant.user) {
      throw new Error('Failed to create test users');
    }

    const creatorId = creator.user.id;
    const participantId = participant.user.id;

    const { data: match, error: matchError } = await supabase
      .from('matches')
      .insert({
        game_id: 1,
        created_by: creatorId,
        entry_fee: 50,
        max_participants: 4,
        match_type: 'standalone',
        game_format: 'solo',
        match_mode: 'ranked',
        title: 'Test Match With Participants',
        status: 'completed',
        result_submission_deadline: new Date(Date.now() - 3600000).toISOString(),
        refund_processed: false
      })
      .select('id')
      .single();

    if (matchError || !match) {
      throw new Error(`Failed to create test match: ${matchError?.message}`);
    }

    // Add both creator and participant
    await supabase.from('match_participants').insert([
      { match_id: match.id, user_id: creatorId, participant_type: 'player' },
      { match_id: match.id, user_id: participantId, participant_type: 'player' }
    ]);

    return {
      matchId: match.id,
      userId: creatorId,
      cleanup: async () => {
        await supabase.from('match_participants').delete().eq('match_id', match.id);
        await supabase.from('matches').delete().eq('id', match.id);
        await supabase.auth.admin.deleteUser(creatorId);
        await supabase.auth.admin.deleteUser(participantId);
      }
    };
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  const tester = new RefundLogicTester();
  tester.runTests().catch(console.error);
}

export { RefundLogicTester };
