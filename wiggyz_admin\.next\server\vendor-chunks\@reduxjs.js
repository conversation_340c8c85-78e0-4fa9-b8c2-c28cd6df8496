"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@reduxjs";
exports.ids = ["vendor-chunks/@reduxjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReducerType: () => (/* binding */ ReducerType),\n/* harmony export */   SHOULD_AUTOBATCH: () => (/* binding */ SHOULD_AUTOBATCH),\n/* harmony export */   TaskAbortError: () => (/* binding */ TaskAbortError),\n/* harmony export */   Tuple: () => (/* binding */ Tuple),\n/* harmony export */   __DO_NOT_USE__ActionTypes: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.__DO_NOT_USE__ActionTypes),\n/* harmony export */   addListener: () => (/* binding */ addListener),\n/* harmony export */   applyMiddleware: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.applyMiddleware),\n/* harmony export */   asyncThunkCreator: () => (/* binding */ asyncThunkCreator),\n/* harmony export */   autoBatchEnhancer: () => (/* binding */ autoBatchEnhancer),\n/* harmony export */   bindActionCreators: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.bindActionCreators),\n/* harmony export */   buildCreateSlice: () => (/* binding */ buildCreateSlice),\n/* harmony export */   clearAllListeners: () => (/* binding */ clearAllListeners),\n/* harmony export */   combineReducers: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers),\n/* harmony export */   combineSlices: () => (/* binding */ combineSlices),\n/* harmony export */   compose: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.compose),\n/* harmony export */   configureStore: () => (/* binding */ configureStore),\n/* harmony export */   createAction: () => (/* binding */ createAction),\n/* harmony export */   createActionCreatorInvariantMiddleware: () => (/* binding */ createActionCreatorInvariantMiddleware),\n/* harmony export */   createAsyncThunk: () => (/* binding */ createAsyncThunk),\n/* harmony export */   createDraftSafeSelector: () => (/* binding */ createDraftSafeSelector),\n/* harmony export */   createDraftSafeSelectorCreator: () => (/* binding */ createDraftSafeSelectorCreator),\n/* harmony export */   createDynamicMiddleware: () => (/* binding */ createDynamicMiddleware),\n/* harmony export */   createEntityAdapter: () => (/* binding */ createEntityAdapter),\n/* harmony export */   createImmutableStateInvariantMiddleware: () => (/* binding */ createImmutableStateInvariantMiddleware),\n/* harmony export */   createListenerMiddleware: () => (/* binding */ createListenerMiddleware),\n/* harmony export */   createNextState: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.produce),\n/* harmony export */   createReducer: () => (/* binding */ createReducer),\n/* harmony export */   createSelector: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.createSelector),\n/* harmony export */   createSelectorCreator: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.createSelectorCreator),\n/* harmony export */   createSerializableStateInvariantMiddleware: () => (/* binding */ createSerializableStateInvariantMiddleware),\n/* harmony export */   createSlice: () => (/* binding */ createSlice),\n/* harmony export */   createStore: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.createStore),\n/* harmony export */   current: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.current),\n/* harmony export */   findNonSerializableValue: () => (/* binding */ findNonSerializableValue),\n/* harmony export */   formatProdErrorMessage: () => (/* binding */ formatProdErrorMessage),\n/* harmony export */   freeze: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.freeze),\n/* harmony export */   isAction: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.isAction),\n/* harmony export */   isActionCreator: () => (/* binding */ isActionCreator),\n/* harmony export */   isAllOf: () => (/* binding */ isAllOf),\n/* harmony export */   isAnyOf: () => (/* binding */ isAnyOf),\n/* harmony export */   isAsyncThunkAction: () => (/* binding */ isAsyncThunkAction),\n/* harmony export */   isDraft: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.isDraft),\n/* harmony export */   isFluxStandardAction: () => (/* binding */ isFSA),\n/* harmony export */   isFulfilled: () => (/* binding */ isFulfilled),\n/* harmony export */   isImmutableDefault: () => (/* binding */ isImmutableDefault),\n/* harmony export */   isPending: () => (/* binding */ isPending),\n/* harmony export */   isPlain: () => (/* binding */ isPlain),\n/* harmony export */   isPlainObject: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject),\n/* harmony export */   isRejected: () => (/* binding */ isRejected),\n/* harmony export */   isRejectedWithValue: () => (/* binding */ isRejectedWithValue),\n/* harmony export */   legacy_createStore: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.legacy_createStore),\n/* harmony export */   lruMemoize: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.lruMemoize),\n/* harmony export */   miniSerializeError: () => (/* binding */ miniSerializeError),\n/* harmony export */   nanoid: () => (/* binding */ nanoid),\n/* harmony export */   original: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.original),\n/* harmony export */   prepareAutoBatched: () => (/* binding */ prepareAutoBatched),\n/* harmony export */   removeListener: () => (/* binding */ removeListener),\n/* harmony export */   unwrapResult: () => (/* binding */ unwrapResult),\n/* harmony export */   weakMapMemoize: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.weakMapMemoize)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/redux.mjs\");\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/immer.mjs\");\n/* harmony import */ var reselect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reselect */ \"(ssr)/./node_modules/reselect/dist/reselect.mjs\");\n/* harmony import */ var redux_thunk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux-thunk */ \"(ssr)/./node_modules/redux-thunk/dist/redux-thunk.mjs\");\n// src/index.ts\n\n\n\n\n// src/createDraftSafeSelector.ts\n\n\nvar createDraftSafeSelectorCreator = (...args) => {\n  const createSelector2 = (0,reselect__WEBPACK_IMPORTED_MODULE_1__.createSelectorCreator)(...args);\n  const createDraftSafeSelector2 = Object.assign((...args2) => {\n    const selector = createSelector2(...args2);\n    const wrappedSelector = (value, ...rest) => selector((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(value) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.current)(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector;\n  }, {\n    withTypes: () => createDraftSafeSelector2\n  });\n  return createDraftSafeSelector2;\n};\nvar createDraftSafeSelector = /* @__PURE__ */ createDraftSafeSelectorCreator(reselect__WEBPACK_IMPORTED_MODULE_1__.weakMapMemoize);\n\n// src/configureStore.ts\n\n\n// src/devtoolsExtension.ts\n\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function() {\n  if (arguments.length === 0) return void 0;\n  if (typeof arguments[0] === \"object\") return redux__WEBPACK_IMPORTED_MODULE_0__.compose;\n  return redux__WEBPACK_IMPORTED_MODULE_0__.compose.apply(null, arguments);\n};\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function() {\n  return function(noop3) {\n    return noop3;\n  };\n};\n\n// src/getDefaultMiddleware.ts\n\n\n// src/createAction.ts\n\n\n// src/tsHelpers.ts\nvar hasMatchFunction = (v) => {\n  return v && typeof v.match === \"function\";\n};\n\n// src/createAction.ts\nfunction createAction(type, prepareAction) {\n  function actionCreator(...args) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error( false ? 0 : \"prepareAction did not return an object\");\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...\"meta\" in prepared && {\n          meta: prepared.meta\n        },\n        ...\"error\" in prepared && {\n          error: prepared.error\n        }\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = (action) => (0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action) && action.type === type;\n  return actionCreator;\n}\nfunction isActionCreator(action) {\n  return typeof action === \"function\" && \"type\" in action && // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action);\n}\nfunction isFSA(action) {\n  return (0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key) {\n  return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\n}\n\n// src/actionCreatorInvariantMiddleware.ts\nfunction getMessage(type) {\n  const splitType = type ? `${type}`.split(\"/\") : [];\n  const actionName = splitType[splitType.length - 1] || \"actionCreator\";\n  return `Detected an action creator with type \"${type || \"unknown\"}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nfunction createActionCreatorInvariantMiddleware(options = {}) {\n  if (false) {}\n  const {\n    isActionCreator: isActionCreator2 = isActionCreator\n  } = options;\n  return () => (next) => (action) => {\n    if (isActionCreator2(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}\n\n// src/utils.ts\n\nfunction getTimeMeasureUtils(maxDelay, fnName) {\n  let elapsed = 0;\n  return {\n    measureTime(fn) {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nvar Tuple = class _Tuple extends Array {\n  constructor(...items) {\n    super(...items);\n    Object.setPrototypeOf(this, _Tuple.prototype);\n  }\n  static get [Symbol.species]() {\n    return _Tuple;\n  }\n  concat(...arr) {\n    return super.concat.apply(this, arr);\n  }\n  prepend(...arr) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new _Tuple(...arr[0].concat(this));\n    }\n    return new _Tuple(...arr.concat(this));\n  }\n};\nfunction freezeDraftable(val) {\n  return (0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraftable)(val) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.produce)(val, () => {\n  }) : val;\n}\nfunction getOrInsertComputed(map, key, compute) {\n  if (map.has(key)) return map.get(key);\n  return map.set(key, compute(key)).get(key);\n}\n\n// src/immutableStateInvariantMiddleware.ts\nfunction isImmutableDefault(value) {\n  return typeof value !== \"object\" || value == null || Object.isFrozen(value);\n}\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\nfunction trackProperties(isImmutable, ignorePaths = [], obj, path = \"\", checkedObjects = /* @__PURE__ */ new Set()) {\n  const tracked = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + \".\" + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked;\n}\nfunction detectMutations(isImmutable, ignoredPaths = [], trackedProperty, obj, sameParentRef = false, path = \"\") {\n  const prevObj = trackedProperty ? trackedProperty.value : void 0;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n  const keysToDetect = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\nfunction createImmutableStateInvariantMiddleware(options = {}) {\n  if (false) {} else {\n    let stringify2 = function(obj, serializer, indent, decycler) {\n      return JSON.stringify(obj, getSerialize2(serializer, decycler), indent);\n    }, getSerialize2 = function(serializer, decycler) {\n      let stack = [], keys = [];\n      if (!decycler) decycler = function(_, value) {\n        if (stack[0] === value) return \"[Circular ~]\";\n        return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\n      };\n      return function(key, value) {\n        if (stack.length > 0) {\n          var thisPos = stack.indexOf(this);\n          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n          if (~stack.indexOf(value)) value = decycler.call(this, key, value);\n        } else stack.push(value);\n        return serializer == null ? value : serializer.call(this, key, value);\n      };\n    };\n    var stringify = stringify2, getSerialize = getSerialize2;\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return (next) => (action) => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error( false ? 0 : `A state mutation was detected between dispatches, in the path '${result.path || \"\"}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error( false ? 0 : `A state mutation was detected inside a dispatch, in the path: ${result.path || \"\"}. Take a look at the reducer(s) handling the action ${stringify2(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}\n\n// src/serializableStateInvariantMiddleware.ts\n\nfunction isPlain(val) {\n  const type = typeof val;\n  return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || (0,redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(val);\n}\nfunction findNonSerializableValue(value, path = \"\", isSerializable = isPlain, getEntries, ignoredPaths = [], cache) {\n  let foundNestedSerializable;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || \"<root>\",\n      value\n    };\n  }\n  if (typeof value !== \"object\" || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === \"object\") {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nfunction isNestedFrozen(value) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== \"object\" || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\nfunction createSerializableStateInvariantMiddleware(options = {}) {\n  if (false) {} else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = [\"meta.arg\", \"meta.baseQueryMeta\"],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache = !disableCache && WeakSet ? /* @__PURE__ */ new WeakSet() : void 0;\n    return (storeAPI) => (next) => (action) => {\n      if (!(0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}\n\n// src/getDefaultMiddleware.ts\nfunction isBoolean(x) {\n  return typeof x === \"boolean\";\n}\nvar buildGetDefaultMiddleware = () => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(redux_thunk__WEBPACK_IMPORTED_MODULE_3__.thunk);\n    } else {\n      middlewareArray.push((0,redux_thunk__WEBPACK_IMPORTED_MODULE_3__.withExtraArgument)(thunk.extraArgument));\n    }\n  }\n  if (true) {\n    if (immutableCheck) {\n      let immutableOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n    }\n    if (serializableCheck) {\n      let serializableOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray;\n};\n\n// src/autoBatchEnhancer.ts\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\nvar prepareAutoBatched = () => (payload) => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nvar createQueueWithTimer = (timeout) => {\n  return (notify) => {\n    setTimeout(notify, timeout);\n  };\n};\nvar autoBatchEnhancer = (options = {\n  type: \"raf\"\n}) => (next) => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = /* @__PURE__ */ new Set();\n  const queueCallback = options.type === \"tick\" ? queueMicrotask : options.type === \"raf\" ? (\n    // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n    typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10)\n  ) : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach((l) => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener2) {\n      const wrappedListener = () => notifying && listener2();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener2);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener2);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action) {\n      try {\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        return store.dispatch(action);\n      } finally {\n        notifying = true;\n      }\n    }\n  });\n};\n\n// src/getDefaultEnhancers.ts\nvar buildGetDefaultEnhancers = (middlewareEnhancer) => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === \"object\" ? autoBatch : void 0));\n  }\n  return enhancerArray;\n};\n\n// src/configureStore.ts\nfunction configureStore(options) {\n  const getDefaultMiddleware = buildGetDefaultMiddleware();\n  const {\n    reducer = void 0,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = void 0,\n    enhancers = void 0\n  } = options || {};\n  let rootReducer;\n  if (typeof reducer === \"function\") {\n    rootReducer = reducer;\n  } else if ((0,redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(reducer)) {\n    rootReducer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)(reducer);\n  } else {\n    throw new Error( false ? 0 : \"`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers\");\n  }\n  if ( true && middleware && typeof middleware !== \"function\") {\n    throw new Error( false ? 0 : \"`middleware` field must be a callback\");\n  }\n  let finalMiddleware;\n  if (typeof middleware === \"function\") {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if ( true && !Array.isArray(finalMiddleware)) {\n      throw new Error( false ? 0 : \"when using a middleware builder function, an array of middleware must be returned\");\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if ( true && finalMiddleware.some((item) => typeof item !== \"function\")) {\n    throw new Error( false ? 0 : \"each middleware provided to configureStore must be a function\");\n  }\n  if ( true && duplicateMiddlewareCheck) {\n    let middlewareReferences = /* @__PURE__ */ new Set();\n    finalMiddleware.forEach((middleware2) => {\n      if (middlewareReferences.has(middleware2)) {\n        throw new Error( false ? 0 : \"Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.\");\n      }\n      middlewareReferences.add(middleware2);\n    });\n  }\n  let finalCompose = redux__WEBPACK_IMPORTED_MODULE_0__.compose;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: \"development\" !== \"production\",\n      ...typeof devTools === \"object\" && devTools\n    });\n  }\n  const middlewareEnhancer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.applyMiddleware)(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers(middlewareEnhancer);\n  if ( true && enhancers && typeof enhancers !== \"function\") {\n    throw new Error( false ? 0 : \"`enhancers` field must be a callback\");\n  }\n  let storeEnhancers = typeof enhancers === \"function\" ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if ( true && !Array.isArray(storeEnhancers)) {\n    throw new Error( false ? 0 : \"`enhancers` callback must return an array\");\n  }\n  if ( true && storeEnhancers.some((item) => typeof item !== \"function\")) {\n    throw new Error( false ? 0 : \"each enhancer provided to configureStore must be a function\");\n  }\n  if ( true && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error(\"middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`\");\n  }\n  const composedEnhancer = finalCompose(...storeEnhancers);\n  return (0,redux__WEBPACK_IMPORTED_MODULE_0__.createStore)(rootReducer, preloadedState, composedEnhancer);\n}\n\n// src/createReducer.ts\n\n\n// src/mapBuilders.ts\nfunction executeReducerBuilderCallback(builderCallback) {\n  const actionsMap = {};\n  const actionMatchers = [];\n  let defaultCaseReducer;\n  const builder = {\n    addCase(typeOrActionCreator, reducer) {\n      if (true) {\n        if (actionMatchers.length > 0) {\n          throw new Error( false ? 0 : \"`builder.addCase` should only be called before calling `builder.addMatcher`\");\n        }\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error( false ? 0 : \"`builder.addCase` cannot be called with an empty action type\");\n      }\n      if (type in actionsMap) {\n        throw new Error( false ? 0 : `\\`builder.addCase\\` cannot be called with two reducers for the same action type '${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher(matcher, reducer) {\n      if (true) {\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer) {\n      if (true) {\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addDefaultCase` can only be called once\");\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}\n\n// src/createReducer.ts\nfunction isStateFunction(x) {\n  return typeof x === \"function\";\n}\nfunction createReducer(initialState, mapOrBuilderCallback) {\n  if (true) {\n    if (typeof mapOrBuilderCallback === \"object\") {\n      throw new Error( false ? 0 : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n  let getInitialState;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action) {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer: reducer2\n    }) => reducer2)];\n    if (caseReducers.filter((cr) => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer) => {\n      if (caseReducer) {\n        if ((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(previousState)) {\n          const draft = previousState;\n          const result = caseReducer(draft, action);\n          if (result === void 0) {\n            return previousState;\n          }\n          return result;\n        } else if (!(0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraftable)(previousState)) {\n          const result = caseReducer(previousState, action);\n          if (result === void 0) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error(\"A case reducer on a non-draftable value must not return undefined\");\n          }\n          return result;\n        } else {\n          return (0,immer__WEBPACK_IMPORTED_MODULE_2__.produce)(previousState, (draft) => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer;\n}\n\n// src/matchers.ts\nvar matches = (matcher, action) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\nfunction isAnyOf(...matchers) {\n  return (action) => {\n    return matchers.some((matcher) => matches(matcher, action));\n  };\n}\nfunction isAllOf(...matchers) {\n  return (action) => {\n    return matchers.every((matcher) => matches(matcher, action));\n  };\n}\nfunction hasExpectedRequestMetadata(action, validStatus) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === \"string\";\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a) {\n  return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\n}\nfunction isPending(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.pending));\n}\nfunction isRejected(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.rejected));\n}\nfunction isRejectedWithValue(...asyncThunks) {\n  const hasFlag = (action) => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nfunction isFulfilled(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"fulfilled\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.fulfilled));\n}\nfunction isAsyncThunkAction(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap((asyncThunk) => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}\n\n// src/nanoid.ts\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\nvar nanoid = (size = 21) => {\n  let id = \"\";\n  let i = size;\n  while (i--) {\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};\n\n// src/createAsyncThunk.ts\nvar commonProperties = [\"name\", \"message\", \"stack\", \"code\"];\nvar RejectWithValue = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar FulfillWithMeta = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar miniSerializeError = (value) => {\n  if (typeof value === \"object\" && value !== null) {\n    const simpleError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === \"string\") {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nvar externalAbortMessage = \"External signal was aborted\";\nvar createAsyncThunk = /* @__PURE__ */ (() => {\n  function createAsyncThunk2(typePrefix, payloadCreator, options) {\n    const fulfilled = createAction(typePrefix + \"/fulfilled\", (payload, requestId, arg, meta) => ({\n      payload,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"fulfilled\"\n      }\n    }));\n    const pending = createAction(typePrefix + \"/pending\", (requestId, arg, meta) => ({\n      payload: void 0,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"pending\"\n      }\n    }));\n    const rejected = createAction(typePrefix + \"/rejected\", (error, requestId, arg, payload, meta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: \"rejected\",\n        aborted: error?.name === \"AbortError\",\n        condition: error?.name === \"ConditionError\"\n      }\n    }));\n    function actionCreator(arg, {\n      signal\n    } = {}) {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler;\n        let abortReason;\n        function abort(reason) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener(\"abort\", () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function() {\n          let finalAction;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              throw {\n                name: \"ConditionError\",\n                message: \"Aborted due to condition callback returning false.\"\n              };\n            }\n            const abortedPromise = new Promise((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: \"AbortError\",\n                  message: abortReason || \"Aborted\"\n                });\n              };\n              abortController.signal.addEventListener(\"abort\", abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })));\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: (value, meta) => {\n                return new RejectWithValue(value, meta);\n              },\n              fulfillWithValue: (value, meta) => {\n                return new FulfillWithMeta(value, meta);\n              }\n            })).then((result) => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener(\"abort\", abortHandler);\n            }\n          }\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk2.withTypes = () => createAsyncThunk2;\n  return createAsyncThunk2;\n})();\nfunction unwrapResult(action) {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\nfunction isThenable(value) {\n  return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\n}\n\n// src/createSlice.ts\nvar asyncThunkSymbol = /* @__PURE__ */ Symbol.for(\"rtk-slice-createasyncthunk\");\nvar asyncThunkCreator = {\n  [asyncThunkSymbol]: createAsyncThunk\n};\nvar ReducerType = /* @__PURE__ */ ((ReducerType2) => {\n  ReducerType2[\"reducer\"] = \"reducer\";\n  ReducerType2[\"reducerWithPrepare\"] = \"reducerWithPrepare\";\n  ReducerType2[\"asyncThunk\"] = \"asyncThunk\";\n  return ReducerType2;\n})(ReducerType || {});\nfunction getType(slice, actionKey) {\n  return `${slice}/${actionKey}`;\n}\nfunction buildCreateSlice({\n  creators\n} = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice2(options) {\n    const {\n      name,\n      reducerPath = name\n    } = options;\n    if (!name) {\n      throw new Error( false ? 0 : \"`name` is a required option for createSlice\");\n    }\n    if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n      if (options.initialState === void 0) {\n        console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\n      }\n    }\n    const reducers = (typeof options.reducers === \"function\" ? options.reducers(buildReducerCreators()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods = {\n      addCase(typeOrActionCreator, reducer2) {\n        const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error( false ? 0 : \"`context.addCase` cannot be called with an empty action type\");\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error( false ? 0 : \"`context.addCase` cannot be called with two reducers for the same action type: \" + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer2;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer2) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer: reducer2\n        });\n        return contextMethods;\n      },\n      exposeAction(name2, actionCreator) {\n        context.actionCreators[name2] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name2, reducer2) {\n        context.sliceCaseReducersByName[name2] = reducer2;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach((reducerName) => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === \"function\"\n      };\n      if (isAsyncThunkSliceReducerDefinition(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition(reducerDetails, reducerDefinition, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (true) {\n        if (typeof options.extraReducers === \"object\") {\n          throw new Error( false ? 0 : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = void 0] = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, (builder) => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key]);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = (state) => state;\n    const injectedSelectorCache = /* @__PURE__ */ new Map();\n    const injectedStateCache = /* @__PURE__ */ new WeakMap();\n    let _reducer;\n    function reducer(state, action) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps(reducerPath2, injected = false) {\n      function selectSlice(state) {\n        let sliceState = state[reducerPath2];\n        if (typeof sliceState === \"undefined\") {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (true) {\n            throw new Error( false ? 0 : \"selectSlice returned undefined for an uninjected slice reducer\");\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => /* @__PURE__ */ new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map = {};\n          for (const [name2, selector] of Object.entries(options.selectors ?? {})) {\n            map[name2] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        });\n      }\n      return {\n        reducerPath: reducerPath2,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice = {\n      name,\n      reducer,\n      actions: context.actionCreators,\n      caseReducers: context.sliceCaseReducersByName,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        };\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector(selector, selectState, getInitialState, injected) {\n  function wrapper(rootState, ...args) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === \"undefined\") {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (true) {\n        throw new Error( false ? 0 : \"selectState returned undefined for an uninjected slice reducer\");\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper;\n}\nvar createSlice = /* @__PURE__ */ buildCreateSlice();\nfunction buildReducerCreators() {\n  function asyncThunk(payloadCreator, config) {\n    return {\n      _reducerDefinitionType: \"asyncThunk\" /* asyncThunk */,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: \"reducer\" /* reducer */\n      });\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: \"reducerWithPrepare\" /* reducerWithPrepare */,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk\n  };\n}\nfunction handleNormalReducerDefinition({\n  type,\n  reducerName,\n  createNotation\n}, maybeReducerWithPrepare, context) {\n  let caseReducer;\n  let prepareCallback;\n  if (\"reducer\" in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error( false ? 0 : \"Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.\");\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"asyncThunk\" /* asyncThunk */;\n}\nfunction isCaseReducerWithPrepareDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"reducerWithPrepare\" /* reducerWithPrepare */;\n}\nfunction handleThunkCaseReducerDefinition({\n  type,\n  reducerName\n}, reducerDefinition, context, cAT) {\n  if (!cAT) {\n    throw new Error( false ? 0 : \"Cannot use `create.asyncThunk` in the built-in `createSlice`. Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.\");\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {\n}\n\n// src/entities/entity_state.ts\nfunction getInitialEntityState() {\n  return {\n    ids: [],\n    entities: {}\n  };\n}\nfunction createInitialStateFactory(stateAdapter) {\n  function getInitialState(additionalState = {}, entities) {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}\n\n// src/entities/state_selectors.ts\nfunction createSelectorsFactory() {\n  function getSelectors(selectState, options = {}) {\n    const {\n      createSelector: createSelector2 = createDraftSafeSelector\n    } = options;\n    const selectIds = (state) => state.ids;\n    const selectEntities = (state) => state.entities;\n    const selectAll = createSelector2(selectIds, selectEntities, (ids, entities) => ids.map((id) => entities[id]));\n    const selectId = (_, id) => id;\n    const selectById = (entities, id) => entities[id];\n    const selectTotal = createSelector2(selectIds, (ids) => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector2(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector2(selectState, selectEntities);\n    return {\n      selectIds: createSelector2(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector2(selectState, selectAll),\n      selectTotal: createSelector2(selectState, selectTotal),\n      selectById: createSelector2(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}\n\n// src/entities/state_adapter.ts\n\nvar isDraftTyped = immer__WEBPACK_IMPORTED_MODULE_2__.isDraft;\nfunction createSingleArgumentStateOperator(mutator) {\n  const operator = createStateOperator((_, state) => mutator(state));\n  return function operation(state) {\n    return operator(state, void 0);\n  };\n}\nfunction createStateOperator(mutator) {\n  return function operation(state, arg) {\n    function isPayloadActionArgument(arg2) {\n      return isFSA(arg2);\n    }\n    const runMutator = (draft) => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped(state)) {\n      runMutator(state);\n      return state;\n    }\n    return (0,immer__WEBPACK_IMPORTED_MODULE_2__.produce)(state, runMutator);\n  };\n}\n\n// src/entities/utils.ts\n\nfunction selectIdValue(entity, selectId) {\n  const key = selectId(entity);\n  if ( true && key === void 0) {\n    console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\n  }\n  return key;\n}\nfunction ensureEntitiesArray(entities) {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nfunction getCurrent(value) {\n  return (0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(value) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.current)(value) : value;\n}\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set(existingIdsArray);\n  const added = [];\n  const addedIds = /* @__PURE__ */ new Set([]);\n  const updated = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}\n\n// src/entities/unsorted_state_adapter.ts\nfunction createUnsortedStateAdapter(selectId) {\n  function addOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key);\n    state.entities[key] = entity;\n  }\n  function addManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key);\n    }\n    ;\n    state.entities[key] = entity;\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {};\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key, state) {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys, state) {\n    let didMutate = false;\n    keys.forEach((key) => {\n      if (key in state.entities) {\n        delete state.entities[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = state.ids.filter((id) => id in state.entities);\n    }\n  }\n  function removeAllMutably(state) {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys, update, state) {\n    const original3 = state.entities[update.id];\n    if (original3 === void 0) {\n      return false;\n    }\n    const updated = Object.assign({}, original3, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete state.entities[update.id];\n    }\n    ;\n    state.entities[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    const newKeys = {};\n    const updatesPerEntity = {};\n    updates.forEach((update) => {\n      if (update.id in state.entities) {\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter((update) => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map((e) => selectIdValue(e, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}\n\n// src/entities/sorted_state_adapter.ts\nfunction findInsertIndex(sortedItems, item, comparisonFunction) {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nfunction insert(sortedItems, item, comparisonFunction) {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nfunction createSortedStateAdapter(selectId, comparer) {\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity, state) {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities, state, existingIds) {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter((model) => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity, state) {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete state.entities[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {};\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity = state.entities[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        replacedIds = true;\n        delete state.entities[update.id];\n        const oldIndex = state.ids.indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        state.entities[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a, b) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  const mergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities;\n    let ids = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}\n\n// src/entities/create_adapter.ts\nfunction createEntityAdapter(options = {}) {\n  const {\n    selectId,\n    sortComparer\n  } = {\n    sortComparer: false,\n    selectId: (instance) => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}\n\n// src/listenerMiddleware/index.ts\n\n\n// src/listenerMiddleware/exceptions.ts\nvar task = \"task\";\nvar listener = \"listener\";\nvar completed = \"completed\";\nvar cancelled = \"cancelled\";\nvar taskCancelled = `task-${cancelled}`;\nvar taskCompleted = `task-${completed}`;\nvar listenerCancelled = `${listener}-${cancelled}`;\nvar listenerCompleted = `${listener}-${completed}`;\nvar TaskAbortError = class {\n  constructor(code) {\n    this.code = code;\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n  name = \"TaskAbortError\";\n  message;\n};\n\n// src/listenerMiddleware/utils.ts\nvar assertFunction = (func, expected) => {\n  if (typeof func !== \"function\") {\n    throw new TypeError( false ? 0 : `${expected} is not a function`);\n  }\n};\nvar noop2 = () => {\n};\nvar catchRejection = (promise, onError = noop2) => {\n  promise.catch(onError);\n  return promise;\n};\nvar addAbortSignalListener = (abortSignal, callback) => {\n  abortSignal.addEventListener(\"abort\", callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener(\"abort\", callback);\n};\nvar abortControllerWithReason = (abortController, reason) => {\n  const signal = abortController.signal;\n  if (signal.aborted) {\n    return;\n  }\n  if (!(\"reason\" in signal)) {\n    Object.defineProperty(signal, \"reason\", {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  abortController.abort(reason);\n};\n\n// src/listenerMiddleware/task.ts\nvar validateActive = (signal) => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal;\n    throw new TaskAbortError(reason);\n  }\n};\nfunction raceWithSignal(signal, promise) {\n  let cleanup = noop2;\n  return new Promise((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    cleanup = noop2;\n  });\n}\nvar runTask = async (task2, cleanUp) => {\n  try {\n    await Promise.resolve();\n    const value = await task2();\n    return {\n      status: \"ok\",\n      value\n    };\n  } catch (error) {\n    return {\n      status: error instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\nvar createPause = (signal) => {\n  return (promise) => {\n    return catchRejection(raceWithSignal(signal, promise).then((output) => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\nvar createDelay = (signal) => {\n  const pause = createPause(signal);\n  return (timeoutMs) => {\n    return pause(new Promise((resolve) => setTimeout(resolve, timeoutMs)));\n  };\n};\n\n// src/listenerMiddleware/index.ts\nvar {\n  assign\n} = Object;\nvar INTERNAL_NIL_TOKEN = {};\nvar alm = \"listenerMiddleware\";\nvar createFork = (parentAbortSignal, parentBlockingPromises) => {\n  const linkControllers = (controller) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return (taskExecutor, opts) => {\n    assertFunction(taskExecutor, \"taskExecutor\");\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask(async () => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result2 = await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      });\n      validateActive(childAbortController.signal);\n      return result2;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop2));\n    }\n    return {\n      result: createPause(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nvar createTakePattern = (startListening, signal) => {\n  const take = async (predicate, timeout) => {\n    validateActive(signal);\n    let unsubscribe = () => {\n    };\n    const tuplePromise = new Promise((resolve, reject) => {\n      let stopListening = startListening({\n        predicate,\n        effect: (action, listenerApi) => {\n          listenerApi.unsubscribe();\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise((resolve) => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      unsubscribe();\n    }\n  };\n  return (predicate, timeout) => catchRejection(take(predicate, timeout));\n};\nvar getListenerEntryPropsFrom = (options) => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {\n  } else {\n    throw new Error( false ? 0 : \"Creating or removing a listener requires one of the known fields for matching an action\");\n  }\n  assertFunction(effect, \"options.listener\");\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\nvar createListenerEntry = /* @__PURE__ */ assign((options) => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: /* @__PURE__ */ new Set(),\n    unsubscribe: () => {\n      throw new Error( false ? 0 : \"Unsubscribe not initialized\");\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n});\nvar findListenerEntry = (listenerMap, options) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find((entry) => {\n    const matchPredicateOrType = typeof type === \"string\" ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nvar cancelActiveListeners = (entry) => {\n  entry.pending.forEach((controller) => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nvar createClearListenerMiddleware = (listenerMap) => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\nvar safelyNotifyError = (errorHandler, errorToNotify, errorInfo) => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\nvar addListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/add`), {\n  withTypes: () => addListener\n});\nvar clearAllListeners = /* @__PURE__ */ createAction(`${alm}/removeAll`);\nvar removeListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n});\nvar defaultErrorHandler = (...args) => {\n  console.error(`${alm}/error`, ...args);\n};\nvar createListenerMiddleware = (middlewareOptions = {}) => {\n  const listenerMap = /* @__PURE__ */ new Map();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, \"onError\");\n  const insertEntry = (entry) => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return (cancelOptions) => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options);\n    return insertEntry(entry);\n  };\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry, action, api, getOriginalState) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening, internalTaskController.signal);\n    const autoJoinPromises = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(\n        action,\n        // Use assign() rather than ... to avoid extra helper functions added to bundle\n        assign({}, api, {\n          getOriginalState,\n          condition: (predicate, timeout) => take(predicate, timeout).then(Boolean),\n          take,\n          delay: createDelay(internalTaskController.signal),\n          pause: createPause(internalTaskController.signal),\n          extra,\n          signal: internalTaskController.signal,\n          fork: createFork(internalTaskController.signal, autoJoinPromises),\n          unsubscribe: entry.unsubscribe,\n          subscribe: () => {\n            listenerMap.set(entry.id, entry);\n          },\n          cancelActiveListeners: () => {\n            entry.pending.forEach((controller, _, set) => {\n              if (controller !== internalTaskController) {\n                abortControllerWithReason(controller, listenerCancelled);\n                set.delete(controller);\n              }\n            });\n          },\n          cancel: () => {\n            abortControllerWithReason(internalTaskController, listenerCancelled);\n            entry.pending.delete(internalTaskController);\n          },\n          throwIfCancelled: () => {\n            validateActive(internalTaskController.signal);\n          }\n        })\n      ));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: \"effect\"\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted);\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware = (api) => (next) => (action) => {\n    if (!(0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action)) {\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n    let originalState = api.getState();\n    const getOriginalState = () => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error( false ? 0 : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState;\n    };\n    let result;\n    try {\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: \"predicate\"\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  };\n};\n\n// src/dynamicMiddleware/index.ts\n\nvar createMiddlewareEntry = (middleware) => ({\n  middleware,\n  applied: /* @__PURE__ */ new Map()\n});\nvar matchInstance = (instanceId) => (action) => action?.meta?.instanceId === instanceId;\nvar createDynamicMiddleware = () => {\n  const instanceId = nanoid();\n  const middlewareMap = /* @__PURE__ */ new Map();\n  const withMiddleware = Object.assign(createAction(\"dynamicMiddleware/add\", (...middlewares) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  });\n  const addMiddleware = Object.assign(function addMiddleware2(...middlewares) {\n    middlewares.forEach((middleware2) => {\n      getOrInsertComputed(middlewareMap, middleware2, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  });\n  const getFinalMiddleware = (api) => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map((entry) => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return (0,redux__WEBPACK_IMPORTED_MODULE_0__.compose)(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware = (api) => (next) => (action) => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};\n\n// src/combineSlices.ts\n\nvar isSliceLike = (maybeSliceLike) => \"reducerPath\" in maybeSliceLike && typeof maybeSliceLike.reducerPath === \"string\";\nvar getReducers = (slices) => slices.flatMap((sliceOrMap) => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer]] : Object.entries(sliceOrMap));\nvar ORIGINAL_STATE = Symbol.for(\"rtk-state-proxy-original\");\nvar isStateProxy = (value) => !!value && !!value[ORIGINAL_STATE];\nvar stateProxyMap = /* @__PURE__ */ new WeakMap();\nvar createStateProxy = (state, reducerMap, initialStateCache) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === \"undefined\") {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== \"undefined\") return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        const reducerResult = reducer(void 0, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === \"undefined\") {\n          throw new Error( false ? 0 : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n}));\nvar original = (state) => {\n  if (!isStateProxy(state)) {\n    throw new Error( false ? 0 : \"original must be used on state Proxy\");\n  }\n  return state[ORIGINAL_STATE];\n};\nvar emptyObject = {};\nvar noopReducer = (state = emptyObject) => state;\nfunction combineSlices(...slices) {\n  const reducerMap = Object.fromEntries(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state, action) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache = {};\n  const inject = (slice, config = {}) => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector(selectorFn, selectState) {\n    return function selector2(state, ...args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  });\n}\n\n// src/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\n\n//# sourceMappingURL=redux-toolkit.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\n");

/***/ })

};
;