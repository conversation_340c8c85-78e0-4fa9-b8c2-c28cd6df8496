/// API Response Validation Utility for WiggyZ App
/// Provides comprehensive validation and logging for API responses

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class ApiResponseValidator {
  /// Validates and logs API response structure
  static Map<String, dynamic>? validateAndParseResponse(
    http.Response response, {
    required String endpoint,
    bool requiresData = true,
  }) {
    try {
      debugPrint('🔍 Validating response for endpoint: $endpoint');
      debugPrint('📊 Status Code: ${response.statusCode}');
      debugPrint('📄 Raw Response: ${response.body}');

      // Check if response is successful
      if (response.statusCode < 200 || response.statusCode >= 300) {
        debugPrint('❌ HTTP Error: ${response.statusCode}');
        _logErrorDetails(response, endpoint);
        return null;
      }

      // Try to parse JSON
      Map<String, dynamic> data;
      try {
        data = json.decode(response.body) as Map<String, dynamic>;
      } catch (e) {
        debugPrint('❌ JSON Parse Error: $e');
        debugPrint('📄 Response body: ${response.body}');
        return null;
      }

      // Log parsed data structure
      debugPrint('✅ Parsed JSON structure:');
      _logDataStructure(data);

      // Validate expected structure
      if (!_validateResponseStructure(data, requiresData)) {
        debugPrint('❌ Invalid response structure');
        return null;
      }

      debugPrint('✅ Response validation successful');
      return data;
    } catch (e, stackTrace) {
      debugPrint('💥 Exception during response validation: $e');
      debugPrint('📚 Stack trace: $stackTrace');
      return null;
    }
  }

  /// Validates specific data fields before model parsing
  static bool validateModelData(
    Map<String, dynamic> data,
    List<String> requiredFields, {
    String? modelName,
  }) {
    // COMPLETELY DISABLE VALIDATION - ALWAYS RETURN TRUE
    return true;
  }

  /// Validates array response data
  static List<Map<String, dynamic>>? validateArrayResponse(
    Map<String, dynamic> response, {
    required String endpoint,
  }) {
    debugPrint('🔍 Validating array response for: $endpoint');

    if (response['success'] != true) {
      debugPrint('❌ API returned success=false');
      debugPrint('📄 Response: $response');
      return null;
    }

    if (response['data'] == null) {
      debugPrint('⚠️ API returned null data');
      return [];
    }

    if (response['data'] is! List) {
      debugPrint('❌ Expected array but got: ${response['data'].runtimeType}');
      debugPrint('📄 Data: ${response['data']}');
      return null;
    }

    final list = response['data'] as List;
    debugPrint('✅ Valid array with ${list.length} items');

    return list.cast<Map<String, dynamic>>();
  }

  /// Validates single object response data
  static Map<String, dynamic>? validateObjectResponse(
    Map<String, dynamic> response, {
    required String endpoint,
  }) {
    debugPrint('🔍 Validating object response for: $endpoint');

    if (response['success'] != true) {
      debugPrint('❌ API returned success=false');
      debugPrint('📄 Response: $response');
      return null;
    }

    if (response['data'] == null) {
      debugPrint('❌ API returned null data');
      return null;
    }

    if (response['data'] is! Map<String, dynamic>) {
      debugPrint('❌ Expected object but got: ${response['data'].runtimeType}');
      debugPrint('📄 Data: ${response['data']}');
      return null;
    }

    debugPrint('✅ Valid object response');
    return response['data'] as Map<String, dynamic>;
  }

  /// Private helper to validate basic response structure
  static bool _validateResponseStructure(
    Map<String, dynamic> data,
    bool requiresData,
  ) {
    // Check for success field
    if (!data.containsKey('success')) {
      debugPrint('❌ Missing "success" field');
      return false;
    }

    // Check if data field is required and present
    if (requiresData && !data.containsKey('data')) {
      debugPrint('❌ Missing required "data" field');
      return false;
    }

    return true;
  }

  /// Private helper to log error details
  static void _logErrorDetails(http.Response response, String endpoint) {
    debugPrint('🚨 Error Details for $endpoint:');
    debugPrint('  Status: ${response.statusCode}');
    debugPrint('  Headers: ${response.headers}');
    debugPrint('  Body: ${response.body}');
    
    // Try to parse error message if JSON
    try {
      final errorData = json.decode(response.body);
      if (errorData is Map<String, dynamic>) {
        debugPrint('  Error Message: ${errorData['message'] ?? 'No message'}');
        debugPrint('  Error Code: ${errorData['code'] ?? 'No code'}');
      }
    } catch (e) {
      debugPrint('  Could not parse error as JSON');
    }
  }

  /// Private helper to log data structure
  static void _logDataStructure(Map<String, dynamic> data, [int depth = 0]) {
    final indent = '  ' * depth;
    
    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (value is Map<String, dynamic>) {
        debugPrint('$indent$key: Map (${value.length} keys)');
        if (depth < 2) { // Limit recursion depth
          _logDataStructure(value, depth + 1);
        }
      } else if (value is List) {
        debugPrint('$indent$key: List (${value.length} items)');
        if (value.isNotEmpty && depth < 2) {
          debugPrint('$indent  First item type: ${value.first.runtimeType}');
        }
      } else {
        debugPrint('$indent$key: ${value.runtimeType} = $value');
      }
    }
  }
}
