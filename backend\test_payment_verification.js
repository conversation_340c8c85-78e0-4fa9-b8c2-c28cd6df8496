const fetch = require('node-fetch');

async function testPaymentVerification() {
  const paymentData = {
    razorpay_order_id: "order_test_123",
    razorpay_payment_id: "pay_test_123", 
    razorpay_signature: "test_signature_123",
    transaction_id: "2c02329f-aa78-4f2b-bba1-ffc0f19e3a74"
  };

  try {
    console.log('Testing payment verification with data:', paymentData);
    
    const response = await fetch('http://127.0.0.1:5000/api/v1/wallet/verify-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IndpZ2d5eiJ9.***************************************************************************************************************************************************************************.cIW11_LX9Q'
      },
      body: JSON.stringify(paymentData)
    });

    console.log('Response status:', response.status);
    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (response.ok) {
      console.log('✅ Payment verification successful!');
    } else {
      console.log('❌ Payment verification failed');
    }

  } catch (error) {
    console.error('Error testing payment verification:', error);
  }
}

testPaymentVerification();
