#!/usr/bin/env node

const axios = require('axios');

const API_BASE_URL = 'http://127.0.0.1:5000/api/v1';

async function testUserLoyaltyEndpoints() {
  console.log('🔐 Testing user loyalty admin endpoints...');
  
  try {
    // Login as admin first
    console.log('\n🔑 Logging in as admin...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (!loginResponse.data.access_token) {
      throw new Error('Failed to get admin token');
    }

    const token = loginResponse.data.access_token;
    console.log('✅ Admin login successful!');

    // Test user loyalty endpoint
    console.log('\n📡 Testing User Loyalty endpoint (GET /rewards/admin/user-loyalty)...');
    
    try {
      const response = await axios({
        method: 'GET',
        url: `${API_BASE_URL}/rewards/admin/user-loyalty`,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      console.log(`✅ User Loyalty: Status ${response.status}`);
      console.log(`📊 Data: ${JSON.stringify(response.data, null, 2).substring(0, 500)}...`);
      
      // If we have users, test the points adjustment endpoint
      if (response.data.data && response.data.data.length > 0) {
        const testUser = response.data.data[0];
        console.log(`\n📡 Testing Points Adjustment for user: ${testUser.users.name}...`);
        
        try {
          const adjustResponse = await axios({
            method: 'POST',
            url: `${API_BASE_URL}/rewards/admin/user-points`,
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            data: {
              user_id: testUser.user_id,
              points: 100,
              reason: 'Test adjustment from admin dashboard'
            },
            timeout: 10000
          });

          console.log(`✅ Points Adjustment: Status ${adjustResponse.status}`);
          console.log(`📊 Result: ${JSON.stringify(adjustResponse.data, null, 2)}`);
          
        } catch (error) {
          console.log(`❌ Points Adjustment: ${error.response?.status || 'Network Error'} - ${error.response?.data?.error || error.message}`);
        }
      } else {
        console.log('ℹ️ No users found to test points adjustment');
      }
      
    } catch (error) {
      console.log(`❌ User Loyalty: ${error.response?.status || 'Network Error'} - ${error.response?.data?.error || error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
  }
}

// Run the test
if (require.main === module) {
  testUserLoyaltyEndpoints().then(() => {
    console.log('\n🎉 User loyalty endpoints testing completed!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { testUserLoyaltyEndpoints };
