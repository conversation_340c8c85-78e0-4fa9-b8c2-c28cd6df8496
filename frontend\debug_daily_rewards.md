# Daily Rewards Debug Guide

## Current Issues
1. Daily Rewards screen shows "Oops! Something went wrong" error
2. "Claim Now" button appears unresponsive
3. Need to identify the root cause of the error

## Enhanced Debugging Added

### 1. Daily Rewards Screen (`daily_rewards_screen.dart`)
- Added detailed error state with debug information
- Enhanced `_claimReward` method with comprehensive logging
- Added validation checks before claiming

### 2. Rewards Screen (`rewards_screen.dart`)
- Enhanced "Claim Now" button with detailed logging
- Added error handling and user feedback
- Added validation checks

### 3. Reward Provider (`reward_provider.dart`)
- Added comprehensive logging to `fetchDailyRewardStatus`
- Added detailed logging to `claimDailyReward`
- Added calendar creation error handling

## Debug Steps

### Step 1: Check Initial Load
1. Open the app and navigate to Daily Rewards screen
2. Check debug console for these logs:
   ```
   🎁 Starting fetchDailyRewardStatus...
   📡 Calling reward service getDailyRewardStatus...
   📊 Received status: [true/false]
   ```

### Step 2: Identify Error Source
Look for these error patterns:
- **Network Error**: Connection issues, timeout
- **Authentication Error**: Token expired or invalid
- **Data Parsing Error**: JSON structure issues
- **Calendar Creation Error**: Issues with reward data structure

### Step 3: Test Claim Functionality
1. If calendar loads successfully, try claiming
2. Check for these logs:
   ```
   🎁 Starting claimDailyReward...
   🔍 Can claim check: [true/false]
   📡 Calling reward service claimDailyReward...
   ```

## Common Issues and Solutions

### Issue 1: Authentication Error
**Symptoms**: 401 errors, "Authentication required"
**Solution**: Check token validity, re-login if needed

### Issue 2: Network Connectivity
**Symptoms**: SocketException, TimeoutException
**Solution**: Check internet connection, retry

### Issue 3: Data Structure Issues
**Symptoms**: JSON parsing errors, calendar creation failures
**Solution**: Check backend response format, validate data structure

### Issue 4: Backend Configuration
**Symptoms**: "No daily rewards configured"
**Solution**: Check backend database for daily reward configuration

## Debug Commands

### Run with Debug Logs
```bash
cd frontend
flutter run --debug
```

### Check Specific Logs
Look for these prefixes in the console:
- 🎁 Daily reward operations
- 📡 Network requests
- 📊 Data processing
- ✅ Success operations
- ❌ Error conditions
- 💥 Exceptions

### Test Backend Directly
```bash
# Test daily reward status endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
     https://your-backend-url/api/v1/rewards/daily-status

# Test daily reward claim endpoint
curl -X POST \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "X-Idempotency-Key: test-key" \
     https://your-backend-url/api/v1/rewards/claim-daily
```

## Next Steps

1. **Run the app** with enhanced debugging
2. **Capture the exact error** from debug logs
3. **Identify the failure point** (network, auth, parsing, etc.)
4. **Apply targeted fix** based on the specific issue
5. **Test the complete flow** from UI to backend

## Expected Debug Output

### Successful Flow
```
🎁 Starting fetchDailyRewardStatus...
📡 Calling reward service getDailyRewardStatus...
📊 Received status: true
🔍 Status data - Streak: 1, Rewards: 7, Claimed: false
✅ Status validated: true
📅 Calendar created: true
📊 Calendar days: 7
💾 Data cached successfully
✅ fetchDailyRewardStatus completed successfully
🏁 fetchDailyRewardStatus finished - Error: null
```

### Error Flow
```
🎁 Starting fetchDailyRewardStatus...
📡 Calling reward service getDailyRewardStatus...
💥 Exception in fetchDailyRewardStatus: [ERROR_DETAILS]
🏁 fetchDailyRewardStatus finished - Error: [USER_FRIENDLY_MESSAGE]
```

This enhanced debugging will help us identify exactly where the issue is occurring and provide a targeted fix.
