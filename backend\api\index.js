// Simple Vercel serverless function for testing
const express = require('express');
const cors = require('cors');

const app = express();

// Enable CORS for all origins
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Idempotency-Key'],
  credentials: false
}));

app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    message: 'WiggyZ API is running'
  });
});

// Test auth endpoint
app.post('/api/v1/auth/login', (req, res) => {
  res.json({ 
    message: 'Login endpoint is accessible',
    timestamp: new Date().toISOString(),
    body: req.body
  });
});

// Catch all
app.all('*', (req, res) => {
  res.json({ 
    message: 'WiggyZ API endpoint',
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

module.exports = app;
