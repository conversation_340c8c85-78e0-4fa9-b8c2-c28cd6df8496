import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'

const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8080/api/v1'

// Helper function to get admin token from Supabase session
async function getAdminToken(request: NextRequest) {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set() {
          // Not needed for server-side operations
        },
        remove() {
          // Not needed for server-side operations
        },
      },
    }
  )

  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error || !session?.access_token) {
    throw new Error('No valid authentication session')
  }

  return `Bearer ${session.access_token}`
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ messageId: string }> }
) {
  try {
    const token = await getAdminToken(request)
    const { messageId } = await params

    const response = await fetch(`${BACKEND_URL}/support/messages/${messageId}/replies`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token,
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `Backend responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching support message replies:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch support message replies' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ messageId: string }> }
) {
  try {
    const token = await getAdminToken(request)
    const { messageId } = await params
    const body = await request.json()

    const response = await fetch(`${BACKEND_URL}/support/admin/messages/${messageId}/replies`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token,
      },
      body: JSON.stringify(body),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `Backend responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error creating support message reply:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create support message reply' },
      { status: 500 }
    )
  }
}
