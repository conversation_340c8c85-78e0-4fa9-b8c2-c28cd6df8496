import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'

const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8080/api/v1'

// Helper function to get admin token from Supabase session
async function getAdminToken(request: NextRequest) {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set() {
          // Not needed for server-side operations
        },
        remove() {
          // Not needed for server-side operations
        },
      },
    }
  )

  try {
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Supabase auth error:', error)
      throw new Error(`Authentication error: ${error.message}`)
    }

    if (!session?.access_token) {
      console.log('No session found. Available cookies:', request.cookies.getAll().map(c => c.name))
      throw new Error('No valid authentication session')
    }

    console.log('Session found for user:', session.user.email)
    return `Bearer ${session.access_token}`
  } catch (err) {
    console.error('Error getting session:', err)
    throw new Error('Failed to get authentication session')
  }
}

export async function GET(request: NextRequest) {
  try {
    const token = await getAdminToken(request)
    const { searchParams } = new URL(request.url)
    
    // Forward query parameters to backend
    const queryString = searchParams.toString()
    const url = `${BACKEND_URL}/support/admin/messages${queryString ? `?${queryString}` : ''}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token,
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `Backend responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching support messages:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch support messages' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = await getAdminToken(request)
    const body = await request.json()
    const { searchParams } = new URL(request.url)
    const messageId = searchParams.get('messageId')

    if (!messageId) {
      return NextResponse.json(
        { error: 'Message ID is required' },
        { status: 400 }
      )
    }

    const response = await fetch(`${BACKEND_URL}/support/admin/messages/${messageId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token,
      },
      body: JSON.stringify(body),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `Backend responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error updating support message:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update support message' },
      { status: 500 }
    )
  }
}
