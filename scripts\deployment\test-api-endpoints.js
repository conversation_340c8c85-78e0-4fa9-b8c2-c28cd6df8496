#!/usr/bin/env node

/**
 * API Endpoints Testing Script for WiggyZ Backend
 * Tests critical API endpoints to verify deployment functionality
 */

const https = require('https');
const http = require('http');

class APITester {
  constructor(baseUrl) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.testResults = [];
  }

  log(message, type = 'info', endpoint = 'general') {
    const result = {
      timestamp: new Date().toISOString(),
      message,
      type,
      endpoint
    };
    this.testResults.push(result);
    
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} [${endpoint.toUpperCase()}] ${message}`);
  }

  async makeRequest(path, options = {}) {
    return new Promise((resolve) => {
      const url = `${this.baseUrl}${path}`;
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'WiggyZ-API-Tester/1.0',
          ...options.headers
        },
        timeout: 15000
      };
      
      const req = client.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const jsonData = data ? JSON.parse(data) : null;
            resolve({
              success: res.statusCode >= 200 && res.statusCode < 400,
              statusCode: res.statusCode,
              data: jsonData,
              rawData: data
            });
          } catch (error) {
            resolve({
              success: res.statusCode >= 200 && res.statusCode < 400,
              statusCode: res.statusCode,
              data: null,
              rawData: data,
              parseError: error.message
            });
          }
        });
      });
      
      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message,
          statusCode: 0
        });
      });
      
      req.on('timeout', () => {
        req.destroy();
        resolve({
          success: false,
          error: 'Request timeout',
          statusCode: 0
        });
      });
      
      if (options.body) {
        req.write(JSON.stringify(options.body));
      }
      
      req.end();
    });
  }

  async testHealthEndpoint() {
    this.log('Testing health endpoint...', 'info', 'health');
    
    const result = await this.makeRequest('/health');
    
    if (result.success && result.data && result.data.status === 'ok') {
      this.log('Health endpoint is working correctly', 'success', 'health');
      this.log(`Response: ${JSON.stringify(result.data)}`, 'info', 'health');
      return true;
    } else {
      this.log(`Health endpoint failed: ${result.error || `Status ${result.statusCode}`}`, 'error', 'health');
      if (result.rawData) {
        this.log(`Raw response: ${result.rawData.substring(0, 200)}...`, 'warn', 'health');
      }
      return false;
    }
  }

  async testAuthEndpoints() {
    this.log('Testing authentication endpoints...', 'info', 'auth');
    
    // Test verify-token endpoint (should return 401 without token)
    const verifyResult = await this.makeRequest('/api/v1/auth/verify-token');
    
    if (verifyResult.statusCode === 401 || verifyResult.statusCode === 400) {
      this.log('Auth verify-token endpoint responding correctly (401/400 expected)', 'success', 'auth');
    } else {
      this.log(`Auth verify-token unexpected response: ${verifyResult.statusCode}`, 'warn', 'auth');
    }

    // Test register endpoint structure (POST without data should return validation error)
    const registerResult = await this.makeRequest('/api/v1/auth/register', {
      method: 'POST',
      body: {}
    });
    
    if (registerResult.statusCode === 400 || registerResult.statusCode === 422) {
      this.log('Auth register endpoint responding correctly (validation error expected)', 'success', 'auth');
    } else {
      this.log(`Auth register unexpected response: ${registerResult.statusCode}`, 'warn', 'auth');
    }

    return true;
  }

  async testGameEndpoints() {
    this.log('Testing game endpoints...', 'info', 'games');
    
    const result = await this.makeRequest('/api/v1/games');
    
    if (result.success) {
      this.log('Games endpoint is accessible', 'success', 'games');
      if (result.data && Array.isArray(result.data)) {
        this.log(`Found ${result.data.length} games`, 'info', 'games');
      }
    } else {
      this.log(`Games endpoint failed: ${result.error || `Status ${result.statusCode}`}`, 'error', 'games');
    }

    return result.success;
  }

  async testTournamentEndpoints() {
    this.log('Testing tournament endpoints...', 'info', 'tournaments');
    
    // Test tournaments list (might require auth, but should respond)
    const result = await this.makeRequest('/api/v1/tournaments');
    
    if (result.success || result.statusCode === 401) {
      this.log('Tournaments endpoint is accessible', 'success', 'tournaments');
    } else {
      this.log(`Tournaments endpoint failed: ${result.error || `Status ${result.statusCode}`}`, 'error', 'tournaments');
    }

    return result.success || result.statusCode === 401;
  }

  async testMatchEndpoints() {
    this.log('Testing match endpoints...', 'info', 'matches');
    
    // Test matches list (might require auth, but should respond)
    const result = await this.makeRequest('/api/v1/matches');
    
    if (result.success || result.statusCode === 401) {
      this.log('Matches endpoint is accessible', 'success', 'matches');
    } else {
      this.log(`Matches endpoint failed: ${result.error || `Status ${result.statusCode}`}`, 'error', 'matches');
    }

    return result.success || result.statusCode === 401;
  }

  async testCORSHeaders() {
    this.log('Testing CORS configuration...', 'info', 'cors');
    
    const result = await this.makeRequest('/health', {
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://app.wiggyz.com',
        'Access-Control-Request-Method': 'GET'
      }
    });
    
    if (result.statusCode === 200 || result.statusCode === 204) {
      this.log('CORS preflight request handled correctly', 'success', 'cors');
    } else {
      this.log(`CORS preflight failed: Status ${result.statusCode}`, 'warn', 'cors');
    }

    return true;
  }

  async runAllTests() {
    this.log(`🚀 Starting API tests for: ${this.baseUrl}`, 'info', 'test');
    
    const tests = [
      { name: 'Health Check', test: () => this.testHealthEndpoint() },
      { name: 'Authentication Endpoints', test: () => this.testAuthEndpoints() },
      { name: 'Game Endpoints', test: () => this.testGameEndpoints() },
      { name: 'Tournament Endpoints', test: () => this.testTournamentEndpoints() },
      { name: 'Match Endpoints', test: () => this.testMatchEndpoints() },
      { name: 'CORS Configuration', test: () => this.testCORSHeaders() }
    ];

    const results = {};
    let passedTests = 0;

    for (const { name, test } of tests) {
      try {
        this.log(`\n--- Testing ${name} ---`, 'info', 'test');
        const result = await test();
        results[name] = result;
        if (result) passedTests++;
      } catch (error) {
        this.log(`Test ${name} crashed: ${error.message}`, 'error', 'test');
        results[name] = false;
      }
    }

    this.log(`\n📊 Test Summary: ${passedTests}/${tests.length} tests passed`, 'info', 'summary');
    
    if (passedTests === tests.length) {
      this.log('🎉 All API tests passed! Backend is fully functional.', 'success', 'summary');
    } else if (passedTests >= tests.length * 0.8) {
      this.log('⚠️ Most tests passed. Backend is mostly functional with minor issues.', 'warn', 'summary');
    } else {
      this.log('❌ Multiple test failures. Backend may have significant issues.', 'error', 'summary');
    }

    return {
      success: passedTests >= tests.length * 0.8,
      passed: passedTests,
      total: tests.length,
      results: results
    };
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const baseUrl = args[0] || 'https://wiggyz-backend-dt566rys4-tausifraja977-gmailcoms-projects.vercel.app';
  
  console.log('🧪 WiggyZ API Endpoint Tester');
  console.log(`🎯 Target URL: ${baseUrl}\n`);
  
  const tester = new APITester(baseUrl);
  
  tester.runAllTests()
    .then(result => {
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test runner crashed:', error.message);
      process.exit(1);
    });
}

module.exports = APITester;
