/**
 * Match Deadline Controller
 * 
 * Handles deadline-related API endpoints for match result submission deadlines
 */

import { Request, Response } from 'express';
import { matchDeadlineService } from '../services/matchDeadlineService';
import { matchAuditService } from '../services/matchAuditService';
import { supabase } from '../../../config/supabase';
import { logger } from '../../../utils/logger';

export interface MatchDeadlineStatus {
  matchId: string;
  deadline: string;
  timeRemaining: number; // milliseconds
  hasExpired: boolean;
  userHasSubmitted: boolean;
  canSubmitResults: boolean;
  participantsSubmitted: number;
  totalParticipants: number;
  matchStatus: string;
  deadlineWarningLevel: 'normal' | 'warning' | 'critical' | 'expired';
}

export class MatchDeadlineController {
  
  /**
   * Get deadline status for a specific match
   * GET /api/v1/matches/:id/deadline-status
   */
  async getDeadlineStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id: matchId } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      // Get match details with deadline information
      const { data: match, error: matchError } = await supabase
        .from('matches')
        .select(`
          id,
          status,
          result_submission_deadline,
          auto_finalized,
          match_participants (
            id,
            user_id,
            participant_type,
            result_submitted_at
          )
        `)
        .eq('id', matchId)
        .single();

      if (matchError || !match) {
        res.status(404).json({ error: 'Match not found' });
        return;
      }

      // Check if user is a participant
      const userParticipant = match.match_participants?.find(
        (p: any) => p.user_id === userId && p.participant_type === 'player'
      );

      if (!userParticipant) {
        res.status(403).json({ error: 'User is not a participant in this match' });
        return;
      }

      // If no deadline is set, return basic info
      if (!match.result_submission_deadline) {
        res.json({
          matchId,
          deadline: null,
          timeRemaining: 0,
          hasExpired: false,
          userHasSubmitted: !!userParticipant.result_submitted_at,
          canSubmitResults: !userParticipant.result_submitted_at && match.status === 'live',
          participantsSubmitted: 0,
          totalParticipants: 0,
          matchStatus: match.status,
          deadlineWarningLevel: 'normal'
        });
        return;
      }

      const deadline = new Date(match.result_submission_deadline);
      const now = new Date();
      const timeRemaining = deadline.getTime() - now.getTime();
      const hasExpired = timeRemaining <= 0;

      // Count participants and submissions
      const players = match.match_participants?.filter((p: any) => p.participant_type === 'player') || [];
      const submittedCount = players.filter((p: any) => p.result_submitted_at).length;

      // Determine warning level
      let deadlineWarningLevel: 'normal' | 'warning' | 'critical' | 'expired' = 'normal';
      if (hasExpired) {
        deadlineWarningLevel = 'expired';
      } else if (timeRemaining <= 2 * 60 * 1000) { // 2 minutes
        deadlineWarningLevel = 'critical';
      } else if (timeRemaining <= 10 * 60 * 1000) { // 10 minutes
        deadlineWarningLevel = 'warning';
      }

      const deadlineStatus: MatchDeadlineStatus = {
        matchId,
        deadline: match.result_submission_deadline,
        timeRemaining: Math.max(0, timeRemaining),
        hasExpired,
        userHasSubmitted: !!userParticipant.result_submitted_at,
        canSubmitResults: !userParticipant.result_submitted_at && !hasExpired && match.status === 'live',
        participantsSubmitted: submittedCount,
        totalParticipants: players.length,
        matchStatus: match.status,
        deadlineWarningLevel
      };

      res.json(deadlineStatus);

    } catch (error) {
      logger.error('Error getting deadline status: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to get deadline status',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get matches approaching deadline (for notifications)
   * GET /api/v1/matches/approaching-deadline
   */
  async getMatchesApproachingDeadline(req: Request, res: Response): Promise<void> {
    try {
      const { warning_minutes = 10 } = req.query;
      const warningMinutes = parseInt(warning_minutes as string, 10);

      // Check if user has admin permissions
      const userId = req.user?.userId;
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (!user || !['admin', 'manager'].includes(user.role)) {
        res.status(403).json({ error: 'Admin access required' });
        return;
      }

      const { data: matches, error } = await supabase
        .rpc('get_matches_approaching_deadline', { warning_minutes: warningMinutes });

      if (error) {
        throw new Error(`Failed to get approaching deadline matches: ${error.message}`);
      }

      res.json({
        matches: matches || [],
        warning_minutes: warningMinutes,
        count: matches?.length || 0
      });

    } catch (error) {
      logger.error('Error getting matches approaching deadline: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to get matches approaching deadline',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Manually process expired matches (admin only)
   * POST /api/v1/matches/process-expired
   */
  async processExpiredMatches(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;

      // Check if user has admin permissions
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (!user || !['admin', 'manager'].includes(user.role)) {
        res.status(403).json({ error: 'Admin access required' });
        return;
      }

      logger.info(`Admin ${userId} manually triggered expired match processing`);

      const result = await matchDeadlineService.manualProcessExpiredMatches();

      res.json({
        message: 'Expired match processing completed',
        processed: result.processed,
        errors: result.errors,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error processing expired matches: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to process expired matches',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get audit log for a specific match (admin only)
   * GET /api/v1/matches/:id/audit-log
   */
  async getMatchAuditLog(req: Request, res: Response): Promise<void> {
    try {
      const { id: matchId } = req.params;
      const userId = req.user?.userId;

      // Check if user has admin permissions
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (!user || !['admin', 'manager'].includes(user.role)) {
        res.status(403).json({ error: 'Admin access required' });
        return;
      }

      const { data: auditLogs, error } = await supabase
        .from('match_audit_log')
        .select('*')
        .eq('match_id', matchId)
        .order('action_timestamp', { ascending: false });

      if (error) {
        throw new Error(`Failed to get audit log: ${error.message}`);
      }

      res.json({
        matchId,
        auditLogs: auditLogs || [],
        count: auditLogs?.length || 0
      });

    } catch (error) {
      logger.error('Error getting match audit log: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to get match audit log',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get deadline statistics (admin only)
   * GET /api/v1/matches/deadline-stats
   */
  async getDeadlineStatistics(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;

      // Check if user has admin permissions
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (!user || !['admin', 'manager'].includes(user.role)) {
        res.status(403).json({ error: 'Admin access required' });
        return;
      }

      // Get various deadline-related statistics
      const [
        { data: totalMatches },
        { data: autoFinalizedMatches },
        { data: expiredMatches },
        { data: approachingDeadline }
      ] = await Promise.all([
        supabase
          .from('matches')
          .select('id', { count: 'exact' })
          .not('result_submission_deadline', 'is', null),
        
        supabase
          .from('matches')
          .select('id', { count: 'exact' })
          .eq('auto_finalized', true),
        
        supabase
          .from('match_deadline_status')
          .select('id', { count: 'exact' })
          .eq('deadline_expired', true),
        
        supabase
          .rpc('get_matches_approaching_deadline', { warning_minutes: 30 })
      ]);

      res.json({
        totalMatchesWithDeadlines: totalMatches?.length || 0,
        autoFinalizedMatches: autoFinalizedMatches?.length || 0,
        expiredMatches: expiredMatches?.length || 0,
        matchesApproachingDeadline: approachingDeadline?.length || 0,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error getting deadline statistics: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to get deadline statistics',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Update deadline for a match (admin only)
   * PATCH /api/v1/matches/:id/deadline
   */
  async updateMatchDeadline(req: Request, res: Response): Promise<void> {
    try {
      const { id: matchId } = req.params;
      const { deadline } = req.body;
      const userId = req.user?.userId;

      // Check if user has admin permissions
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (!user || !['admin', 'manager'].includes(user.role)) {
        res.status(403).json({ error: 'Admin access required' });
        return;
      }

      // Validate deadline format
      const deadlineDate = new Date(deadline);
      if (isNaN(deadlineDate.getTime())) {
        res.status(400).json({ error: 'Invalid deadline format' });
        return;
      }

      // Update match deadline
      const { data: updatedMatch, error: updateError } = await supabase
        .from('matches')
        .update({
          result_submission_deadline: deadlineDate.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', matchId)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Failed to update deadline: ${updateError.message}`);
      }

      // Update deadline status table
      await supabase
        .from('match_deadline_status')
        .upsert({
          match_id: matchId,
          result_submission_deadline: deadlineDate.toISOString(),
          deadline_expired: deadlineDate.getTime() <= Date.now(),
          updated_at: new Date().toISOString()
        });

      // Log the admin action
      await matchAuditService.logAction({
        match_id: matchId,
        action_type: 'manual_admin_override',
        triggered_by: 'admin',
        admin_user_id: userId,
        affected_participants: [],
        action_details: {
          action: 'deadline_updated',
          old_deadline: updatedMatch.result_submission_deadline,
          new_deadline: deadline
        },
        reason: 'Admin manually updated result submission deadline',
        deadline_time: deadline,
        participants_submitted: 0,
        participants_defaulted: 0,
        total_participants: 0
      });

      res.json({
        message: 'Deadline updated successfully',
        match: updatedMatch,
        newDeadline: deadline
      });

    } catch (error) {
      logger.error('Error updating match deadline: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to update match deadline',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get recent audit logs (admin only)
   * GET /api/v1/matches/audit-log/recent
   */
  async getRecentAuditLogs(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      const { limit = 50, offset = 0 } = req.query;

      // Check if user has admin permissions
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (!user || !['admin', 'manager'].includes(user.role)) {
        res.status(403).json({ error: 'Admin access required' });
        return;
      }

      const auditLogs = await matchAuditService.getRecentAuditLogs(
        parseInt(limit as string, 10),
        parseInt(offset as string, 10)
      );

      res.json({
        auditLogs,
        count: auditLogs.length,
        limit: parseInt(limit as string, 10),
        offset: parseInt(offset as string, 10)
      });

    } catch (error) {
      logger.error('Error getting recent audit logs: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to get recent audit logs',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get audit metrics (admin only)
   * GET /api/v1/matches/audit-metrics
   */
  async getAuditMetrics(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      const { timeWindow = 24 } = req.query;

      // Check if user has admin permissions
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (!user || !['admin', 'manager'].includes(user.role)) {
        res.status(403).json({ error: 'Admin access required' });
        return;
      }

      const metrics = await matchAuditService.getAuditMetrics(
        parseInt(timeWindow as string, 10)
      );

      res.json(metrics);

    } catch (error) {
      logger.error('Error getting audit metrics: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to get audit metrics',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get system health status (admin only)
   * GET /api/v1/matches/system-health
   */
  async getSystemHealth(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;

      // Check if user has admin permissions
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (!user || !['admin', 'manager'].includes(user.role)) {
        res.status(403).json({ error: 'Admin access required' });
        return;
      }

      const health = await matchAuditService.getSystemHealth();

      res.json(health);

    } catch (error) {
      logger.error('Error getting system health: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to get system health',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Export audit logs (admin only)
   * GET /api/v1/matches/audit-log/export
   */
  async exportAuditLogs(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      const { startDate, endDate } = req.query;

      // Check if user has admin permissions
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (!user || !['admin', 'manager'].includes(user.role)) {
        res.status(403).json({ error: 'Admin access required' });
        return;
      }

      if (!startDate || !endDate) {
        res.status(400).json({ error: 'Start date and end date are required' });
        return;
      }

      const auditLogs = await matchAuditService.exportAuditLogs(
        startDate as string,
        endDate as string
      );

      // Set headers for file download
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${startDate}-${endDate}.json"`);

      res.json({
        exportDate: new Date().toISOString(),
        dateRange: { startDate, endDate },
        totalRecords: auditLogs.length,
        auditLogs
      });

    } catch (error) {
      logger.error('Error exporting audit logs: ' + (error instanceof Error ? error.message : String(error)));
      res.status(500).json({
        error: 'Failed to export audit logs',
        details: error instanceof Error ? error.message : String(error)
      });
    }
  }
}

export const matchDeadlineController = new MatchDeadlineController();
