/**
 * Test file to verify real-time payment summary calculations
 * This simulates the fee calculation logic implemented in both screens
 */

void main() {
  print('🧪 Testing Real-time Payment Summary Updates');
  print('=' * 50);
  
  testAddMoneyRealTimeCalculations();
  testWithdrawalRealTimeCalculations();
  testEdgeCasesAndValidation();
  testDecimalPrecision();
  testUserTypingScenarios();
  
  print('\n✅ All real-time payment summary tests passed!');
  print('The implementation is working correctly.');
}

void testAddMoneyRealTimeCalculations() {
  print('\n💰 Testing Add Money Real-time Calculations');
  print('-' * 45);
  
  final testCases = [
    {'input': '', 'expected': AddMoneyResult.empty()},
    {'input': '0', 'expected': AddMoneyResult.empty()},
    {'input': '25', 'expected': AddM<PERSON>Result(25.0, 0.5, 0.09, 1.41, 24.41, 25.0)},
    {'input': '100', 'expected': Add<PERSON><PERSON><PERSON><PERSON><PERSON>(100.0, 2.0, 0.36, 2.36, 97.64, 100.0)},
    {'input': '500', 'expected': AddMoneyR<PERSON>ult(500.0, 10.0, 1.8, 11.8, 488.2, 500.0)},
    {'input': '1000', 'expected': AddMoneyResult(1000.0, 20.0, 3.6, 23.6, 976.4, 1000.0)},
    {'input': '99.99', 'expected': AddMoneyResult(99.99, 2.0, 0.36, 2.36, 97.63, 99.99)},
  ];
  
  for (final testCase in testCases) {
    final input = testCase['input'] as String;
    final expected = testCase['expected'] as AddMoneyResult;
    final result = calculateAddMoneyFees(input);
    
    print('Input: "$input"');
    print('  Base Amount: ${result.formatCurrency(result.baseAmount)}');
    print('  Transaction Fee: ${result.formatCurrency(result.transactionFee)}');
    print('  GST on Fee: ${result.formatCurrency(result.gstOnFee)}');
    print('  Total Deductions: ${result.formatCurrency(result.totalDeductions)}');
    print('  Amount to Wallet: ${result.formatCurrency(result.amountToWallet)}');
    print('  Amount Charged: ${result.formatCurrency(result.amountCharged)}');
    
    // Verify calculations
    assert(result.equals(expected), 'Add money calculation failed for input: $input');
    print('  ✅ PASS\n');
  }
}

void testWithdrawalRealTimeCalculations() {
  print('\n💸 Testing Withdrawal Real-time Calculations (Updated Fee Structure)');
  print('-' * 65);

  final testCases = [
    {'input': '', 'expected': WithdrawalResult.empty()},
    {'input': '0', 'expected': WithdrawalResult.empty()},
    {'input': '100', 'expected': WithdrawalResult(100.0, 5.0, 0.9, 5.9, 94.1)}, // Min ₹5 fee
    {'input': '200', 'expected': WithdrawalResult(200.0, 5.0, 0.9, 5.9, 194.1)}, // Min ₹5 fee
    {'input': '250', 'expected': WithdrawalResult(250.0, 5.0, 0.9, 5.9, 244.1)}, // Min ₹5 fee
    {'input': '300', 'expected': WithdrawalResult(300.0, 6.0, 1.08, 7.08, 292.92)}, // 2% fee (₹6 > ₹5)
    {'input': '500', 'expected': WithdrawalResult(500.0, 10.0, 1.8, 11.8, 488.2)}, // 2% fee
    {'input': '1000', 'expected': WithdrawalResult(1000.0, 20.0, 3.6, 23.6, 976.4)}, // 2% fee
  ];
  
  for (final testCase in testCases) {
    final input = testCase['input'] as String;
    final expected = testCase['expected'] as WithdrawalResult;
    final result = calculateWithdrawalFees(input);
    
    print('Input: "$input"');
    print('  Withdrawal Amount: ${result.formatCurrency(result.withdrawalAmount)}');
    print('  Transaction Fee: ${result.formatCurrency(result.transactionFee)}');
    print('  GST on Fee: ${result.formatCurrency(result.gstOnFee)}');
    print('  Total Deductions: ${result.formatCurrency(result.totalDeductions)}');
    print('  Amount to Bank: ${result.formatCurrency(result.finalAmount)}');
    
    // Verify calculations
    assert(result.equals(expected), 'Withdrawal calculation failed for input: $input');
    print('  ✅ PASS\n');
  }
}

void testEdgeCasesAndValidation() {
  print('\n🔍 Testing Edge Cases and Validation');
  print('-' * 40);
  
  // Test invalid inputs
  final invalidInputs = ['abc', '12.345', '-100', '********'];
  
  print('Invalid inputs:');
  for (final input in invalidInputs) {
    final addResult = calculateAddMoneyFees(input);
    final withdrawResult = calculateWithdrawalFees(input);
    
    print('  Input: "$input"');
    print('    Add Money: ${addResult.formatCurrency(addResult.baseAmount)}');
    print('    Withdrawal: ${withdrawResult.formatCurrency(withdrawResult.withdrawalAmount)}');
  }
  
  // Test minimum amount validation
  print('\nMinimum amount validation:');
  final addMoney24 = calculateAddMoneyFees('24');
  final addMoney25 = calculateAddMoneyFees('25');
  print('  Add Money ₹24: Valid = ${addMoney24.baseAmount >= 25}');
  print('  Add Money ₹25: Valid = ${addMoney25.baseAmount >= 25}');
  
  final withdrawal99 = calculateWithdrawalFees('99');
  final withdrawal100 = calculateWithdrawalFees('100');
  print('  Withdrawal ₹99: Valid = ${withdrawal99.withdrawalAmount >= 100}');
  print('  Withdrawal ₹100: Valid = ${withdrawal100.withdrawalAmount >= 100}');
  
  print('  ✅ Edge cases handled correctly');
}

void testDecimalPrecision() {
  print('\n🎯 Testing Decimal Precision');
  print('-' * 30);
  
  final testCases = [
    '33.33',
    '66.67',
    '99.99',
    '123.45',
    '999.99'
  ];
  
  for (final input in testCases) {
    final addResult = calculateAddMoneyFees(input);
    final withdrawResult = calculateWithdrawalFees(input);
    
    print('Input: ₹$input');
    print('  Add Money Net: ${addResult.formatCurrency(addResult.amountToWallet)}');
    print('  Withdrawal Net: ${withdrawResult.formatCurrency(withdrawResult.finalAmount)}');
    
    // Verify precision (should have at most 2 decimal places)
    final addPrecision = addResult.amountToWallet.toStringAsFixed(2);
    final withdrawPrecision = withdrawResult.finalAmount.toStringAsFixed(2);
    
    assert(addPrecision.split('.')[1].length <= 2, 'Add money precision error');
    assert(withdrawPrecision.split('.')[1].length <= 2, 'Withdrawal precision error');
  }
  
  print('  ✅ Decimal precision is correct');
}

void testUserTypingScenarios() {
  print('\n⌨️  Testing User Typing Scenarios');
  print('-' * 35);
  
  // Simulate user typing "100" character by character
  final typingSequence = ['1', '10', '100'];
  
  print('User typing "100":');
  for (final input in typingSequence) {
    final result = calculateAddMoneyFees(input);
    print('  "$input" → Net: ${result.formatCurrency(result.amountToWallet)}');
  }
  
  // Simulate user typing "99.99"
  final decimalSequence = ['9', '99', '99.', '99.9', '99.99'];
  
  print('\nUser typing "99.99":');
  for (final input in decimalSequence) {
    final result = calculateAddMoneyFees(input);
    print('  "$input" → Net: ${result.formatCurrency(result.amountToWallet)}');
  }
  
  print('  ✅ Real-time typing scenarios work correctly');
}

// Helper classes for test results
class AddMoneyResult {
  final double baseAmount;
  final double transactionFee;
  final double gstOnFee;
  final double totalDeductions;
  final double amountToWallet;
  final double amountCharged;
  
  AddMoneyResult(this.baseAmount, this.transactionFee, this.gstOnFee, 
                this.totalDeductions, this.amountToWallet, this.amountCharged);
  
  AddMoneyResult.empty() : this(0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
  
  String formatCurrency(double amount) {
    if (amount == 0.0) return '₹0';
    return '₹${amount.toStringAsFixed(2)}';
  }
  
  bool equals(AddMoneyResult other) {
    return (baseAmount - other.baseAmount).abs() < 0.01 &&
           (transactionFee - other.transactionFee).abs() < 0.01 &&
           (gstOnFee - other.gstOnFee).abs() < 0.01 &&
           (totalDeductions - other.totalDeductions).abs() < 0.01 &&
           (amountToWallet - other.amountToWallet).abs() < 0.01 &&
           (amountCharged - other.amountCharged).abs() < 0.01;
  }
}

class WithdrawalResult {
  final double withdrawalAmount;
  final double transactionFee;
  final double gstOnFee;
  final double totalDeductions;
  final double finalAmount;
  
  WithdrawalResult(this.withdrawalAmount, this.transactionFee, this.gstOnFee,
                  this.totalDeductions, this.finalAmount);
  
  WithdrawalResult.empty() : this(0.0, 0.0, 0.0, 0.0, 0.0);
  
  String formatCurrency(double amount) {
    if (amount == 0.0) return '₹0';
    return '₹${amount.toStringAsFixed(2)}';
  }
  
  bool equals(WithdrawalResult other) {
    return (withdrawalAmount - other.withdrawalAmount).abs() < 0.01 &&
           (transactionFee - other.transactionFee).abs() < 0.01 &&
           (gstOnFee - other.gstOnFee).abs() < 0.01 &&
           (totalDeductions - other.totalDeductions).abs() < 0.01 &&
           (finalAmount - other.finalAmount).abs() < 0.01;
  }
}

// Fee calculation functions (matching the app logic)
AddMoneyResult calculateAddMoneyFees(String input) {
  if (input.isEmpty) return AddMoneyResult.empty();
  
  final baseAmount = double.tryParse(input) ?? 0.0;
  if (baseAmount <= 0) return AddMoneyResult.empty();
  
  final transactionFee = baseAmount * 0.02; // 2% transaction fee
  final gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
  final totalDeductions = transactionFee + gstOnFee;
  final amountToWallet = baseAmount - totalDeductions; // Net amount credited to wallet
  final amountCharged = baseAmount; // User pays the amount they entered
  
  return AddMoneyResult(baseAmount, transactionFee, gstOnFee, 
                       totalDeductions, amountToWallet, amountCharged);
}

WithdrawalResult calculateWithdrawalFees(String input) {
  if (input.isEmpty) return WithdrawalResult.empty();

  final withdrawalAmount = double.tryParse(input) ?? 0.0;
  if (withdrawalAmount <= 0) return WithdrawalResult.empty();

  // Minimum ₹5 transaction fee OR 2% of withdrawal amount (whichever is higher)
  final calculatedFee = withdrawalAmount * 0.02; // 2% transaction fee
  final transactionFee = calculatedFee > 5.0 ? calculatedFee : 5.0; // Minimum ₹5
  final gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
  final totalDeductions = transactionFee + gstOnFee;
  final finalAmount = withdrawalAmount - totalDeductions; // Amount user receives after fees

  return WithdrawalResult(withdrawalAmount, transactionFee, gstOnFee,
                         totalDeductions, finalAmount);
}
