import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/shared/widgets/custom_app_bar.dart';
import 'package:wiggyz_app/screens/notification_screen.dart';
import 'package:wiggyz_app/features/notifications/providers/notification_provider.dart';

void main() {
  group('Notification Functionality Tests', () {
    testWidgets('CustomAppBar shows notification icon when enabled', (WidgetTester tester) async {
      // Create a mock notification provider
      final notificationProvider = NotificationProvider();
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<NotificationProvider>(
            create: (_) => notificationProvider,
            child: Scaffold(
              appBar: CustomAppBar.text(
                title: 'Test Screen',
                showNotificationIcon: true,
                showBackButton: false,
              ),
              body: const Center(child: Text('Test Body')),
            ),
          ),
        ),
      );

      // Verify that notification icon is present
      expect(find.byIcon(Icons.notifications_none_rounded), findsOneWidget);
    });

    testWidgets('CustomAppBar hides notification icon when disabled', (WidgetTester tester) async {
      // Create a mock notification provider
      final notificationProvider = NotificationProvider();
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<NotificationProvider>(
            create: (_) => notificationProvider,
            child: Scaffold(
              appBar: CustomAppBar.text(
                title: 'Test Screen',
                showNotificationIcon: false,
                showBackButton: false,
              ),
              body: const Center(child: Text('Test Body')),
            ),
          ),
        ),
      );

      // Verify that notification icon is not present
      expect(find.byIcon(Icons.notifications_none_rounded), findsNothing);
    });

    testWidgets('Notification icon navigates to NotificationScreen when tapped', (WidgetTester tester) async {
      // Create a mock notification provider
      final notificationProvider = NotificationProvider();
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<NotificationProvider>(
            create: (_) => notificationProvider,
            child: Scaffold(
              appBar: CustomAppBar.text(
                title: 'Test Screen',
                showNotificationIcon: true,
                showBackButton: false,
              ),
              body: const Center(child: Text('Test Body')),
            ),
          ),
        ),
      );

      // Find and tap the notification icon
      final notificationIcon = find.byIcon(Icons.notifications_none_rounded);
      expect(notificationIcon, findsOneWidget);
      
      await tester.tap(notificationIcon);
      await tester.pumpAndSettle();

      // Verify that NotificationScreen is displayed
      expect(find.byType(NotificationScreen), findsOneWidget);
    });

    testWidgets('Profile screen uses CustomAppBar with notification icon enabled', (WidgetTester tester) async {
      // This test verifies that the profile screen configuration is correct
      // We'll test the CustomAppBar configuration directly
      
      const appBar = CustomAppBar(
        title: Text('Profile'),
        showNotificationIcon: true,
        showBackButton: false,
      );

      // Verify the configuration
      expect(appBar.showNotificationIcon, isTrue);
      expect(appBar.showBackButton, isFalse);
      expect(appBar.title, isA<Text>());
    });
  });
}
