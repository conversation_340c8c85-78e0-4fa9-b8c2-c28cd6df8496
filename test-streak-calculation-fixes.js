#!/usr/bin/env node

/**
 * Test script to verify streak calculation fixes
 * Tests the specific issues that were identified and fixed
 */

const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:5000/api/v1';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Advanture101$'
};

let authToken = null;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, ...args) {
  console.log(color + args.join(' ') + colors.reset);
}

function section(title) {
  console.log('\n' + '='.repeat(50));
  console.log(title);
  console.log('='.repeat(50));
}

async function login() {
  try {
    log(colors.blue, '🔐 Authenticating test user...');
    const response = await axios.post(`${BASE_URL}/auth/login`, TEST_USER);

    if (response.data.access_token) {
      authToken = response.data.access_token;
      log(colors.green, '✅ Authentication successful');
      log(colors.green, `   User: ${response.data.user.name} (${response.data.user.role})`);
      return true;
    } else {
      log(colors.red, '❌ Login failed:', response.data.message);
      return false;
    }
  } catch (error) {
    log(colors.red, '❌ Login failed:', error.response?.data?.message || error.message);
    return false;
  }
}

async function testStreakConsistency() {
  section('Testing Streak Calculation Consistency');
  
  try {
    log(colors.blue, '📊 Testing streak consistency across multiple requests...');
    
    // Make multiple requests to check consistency
    const requests = [];
    for (let i = 0; i < 5; i++) {
      requests.push(
        axios.get(`${BASE_URL}/rewards/daily-status`, {
          headers: { 'Authorization': `Bearer ${authToken}` }
        })
      );
    }
    
    const responses = await Promise.all(requests);
    
    // Check all responses are successful
    const allSuccessful = responses.every(r => r.data.success);
    if (!allSuccessful) {
      log(colors.red, '❌ Some requests failed');
      return false;
    }
    
    // Check streak consistency
    const streaks = responses.map(r => r.data.data.streak);
    const firstStreak = responses[0].data.data.streak;
    const allConsistent = responses.every(r =>
      r.data.data.streak.current_streak === firstStreak.current_streak &&
      r.data.data.streak.longest_streak === firstStreak.longest_streak
    );
    
    if (allConsistent) {
      log(colors.green, '✅ Streak data is consistent across requests');
      log(colors.green, `   Current streak: ${firstStreak.current_streak}`);
      log(colors.green, `   Longest streak: ${firstStreak.longest_streak}`);
      return true;
    } else {
      log(colors.red, '❌ Streak data is inconsistent');
      return false;
    }
  } catch (error) {
    log(colors.red, '❌ Streak consistency test failed:', error.message);
    return false;
  }
}

async function testIdempotency() {
  section('Testing Idempotency Fixes');
  
  try {
    log(colors.blue, '🔄 Testing idempotency with same key...');
    
    const idempotencyKey = `test-${Date.now()}`;
    
    // Make two identical requests with same idempotency key
    const request1 = axios.post(`${BASE_URL}/rewards/claim-daily`, {}, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'X-Idempotency-Key': idempotencyKey
      }
    });
    
    const request2 = axios.post(`${BASE_URL}/rewards/claim-daily`, {}, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'X-Idempotency-Key': idempotencyKey
      }
    });
    
    const [response1, response2] = await Promise.allSettled([request1, request2]);
    
    // Both should have same outcome (either both succeed or both fail with same error)
    const status1 = response1.status === 'fulfilled' ? response1.value.status : response1.reason.response?.status;
    const status2 = response2.status === 'fulfilled' ? response2.value.status : response2.reason.response?.status;
    
    if (status1 === status2) {
      log(colors.green, '✅ Idempotency working correctly');
      log(colors.green, `   Both requests returned status: ${status1}`);
      
      if (status1 === 200) {
        log(colors.green, '   Daily reward claimed successfully');
      } else if (status1 === 400) {
        log(colors.green, '   Both requests properly rejected (already claimed)');
      }
      return true;
    } else {
      log(colors.red, '❌ Idempotency failed');
      log(colors.red, `   Request 1 status: ${status1}`);
      log(colors.red, `   Request 2 status: ${status2}`);
      return false;
    }
  } catch (error) {
    log(colors.red, '❌ Idempotency test failed:', error.message);
    return false;
  }
}

async function testLoyaltyIntegration() {
  section('Testing Loyalty Integration Fixes');
  
  try {
    log(colors.blue, '🏆 Testing loyalty points integration...');
    
    // Get initial loyalty status
    const initialResponse = await axios.get(`${BASE_URL}/rewards/loyalty`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    if (!initialResponse.data.success) {
      log(colors.red, '❌ Failed to get initial loyalty status');
      return false;
    }
    
    const initialPoints = initialResponse.data.data.points;
    log(colors.blue, `   Initial loyalty points: ${initialPoints}`);
    
    // Try to claim daily reward
    const claimResponse = await axios.post(`${BASE_URL}/rewards/claim-daily`, {}, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'X-Idempotency-Key': `loyalty-test-${Date.now()}`
      }
    }).catch(err => err.response);
    
    if (claimResponse.status === 200) {
      // Reward claimed successfully, check if loyalty points increased
      const finalResponse = await axios.get(`${BASE_URL}/rewards/loyalty`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      });
      
      const finalPoints = finalResponse.data.data.points;
      log(colors.blue, `   Final loyalty points: ${finalPoints}`);
      
      if (finalPoints > initialPoints) {
        log(colors.green, '✅ Loyalty points increased after daily reward claim');
        log(colors.green, `   Points gained: ${finalPoints - initialPoints}`);
        return true;
      } else {
        log(colors.yellow, '⚠️ Loyalty points did not increase (might be expected if no points reward)');
        return true;
      }
    } else if (claimResponse.status === 400) {
      log(colors.yellow, '⚠️ Daily reward already claimed (expected behavior)');
      log(colors.green, '✅ Loyalty integration test passed (reward already claimed)');
      return true;
    } else {
      log(colors.red, '❌ Unexpected response from claim endpoint:', claimResponse.status);
      return false;
    }
  } catch (error) {
    log(colors.red, '❌ Loyalty integration test failed:', error.message);
    return false;
  }
}

async function testErrorHandling() {
  section('Testing Error Handling Improvements');
  
  try {
    log(colors.blue, '🛡️ Testing error handling...');
    
    // Test unauthorized access
    const unauthorizedResponse = await axios.get(`${BASE_URL}/rewards/daily-status`)
      .catch(err => err.response);
    
    if (unauthorizedResponse.status === 401) {
      log(colors.green, '✅ Unauthorized access properly rejected');
    } else {
      log(colors.red, '❌ Unauthorized access not properly handled');
      return false;
    }
    
    // Test invalid token
    const invalidTokenResponse = await axios.get(`${BASE_URL}/rewards/daily-status`, {
      headers: { 'Authorization': 'Bearer invalid-token' }
    }).catch(err => err.response);
    
    if (invalidTokenResponse.status === 401) {
      log(colors.green, '✅ Invalid token properly rejected');
    } else {
      log(colors.red, '❌ Invalid token not properly handled');
      return false;
    }
    
    return true;
  } catch (error) {
    log(colors.red, '❌ Error handling test failed:', error.message);
    return false;
  }
}

async function testTimezoneHandling() {
  section('Testing Timezone Handling Fixes');
  
  try {
    log(colors.blue, '🌍 Testing timezone consistency...');
    
    // Make requests with different timezone headers (if supported)
    const response1 = await axios.get(`${BASE_URL}/rewards/daily-status`, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'X-Timezone': 'UTC'
      }
    });
    
    const response2 = await axios.get(`${BASE_URL}/rewards/daily-status`, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'X-Timezone': 'America/New_York'
      }
    });
    
    // Both should return same hasClaimedToday status (using UTC internally)
    const claimed1 = response1.data.data.hasClaimedToday;
    const claimed2 = response2.data.data.hasClaimedToday;
    
    if (claimed1 === claimed2) {
      log(colors.green, '✅ Timezone handling is consistent');
      log(colors.green, `   Has claimed today: ${claimed1}`);
      return true;
    } else {
      log(colors.red, '❌ Timezone handling is inconsistent');
      return false;
    }
  } catch (error) {
    log(colors.red, '❌ Timezone handling test failed:', error.message);
    return false;
  }
}

async function runTests() {
  section('WiggyZ Daily Rewards Fixes Verification');
  
  const results = {
    authentication: false,
    streakConsistency: false,
    idempotency: false,
    loyaltyIntegration: false,
    errorHandling: false,
    timezoneHandling: false
  };
  
  // Test authentication
  results.authentication = await login();
  if (!results.authentication) {
    log(colors.red, '❌ Cannot proceed without authentication');
    return results;
  }
  
  // Run all tests
  results.streakConsistency = await testStreakConsistency();
  results.idempotency = await testIdempotency();
  results.loyaltyIntegration = await testLoyaltyIntegration();
  results.errorHandling = await testErrorHandling();
  results.timezoneHandling = await testTimezoneHandling();
  
  // Summary
  section('Test Results Summary');
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, passed]) => {
    const icon = passed ? '✅' : '❌';
    const color = passed ? colors.green : colors.red;
    log(color, `${icon} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  console.log('\n' + '='.repeat(50));
  if (passedTests === totalTests) {
    log(colors.green + colors.bold, `🎉 All ${totalTests} tests passed! Daily rewards fixes are working correctly.`);
  } else {
    log(colors.yellow + colors.bold, `⚠️ ${passedTests}/${totalTests} tests passed. Some issues may need attention.`);
  }
  console.log('='.repeat(50));
  
  return results;
}

// Run the tests
runTests().catch(console.error);
