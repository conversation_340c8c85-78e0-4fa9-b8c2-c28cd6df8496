{"timestamp": "2025-07-31T07:28:45.930Z", "results": [{"timestamp": "2025-07-31T07:28:44.715Z", "message": "Loaded environment variables from backend/.env", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.741Z", "message": "🔍 Running pre-deployment validation...", "type": "info", "category": "validation"}, {"timestamp": "2025-07-31T07:28:44.741Z", "message": "Validating environment variables...", "type": "info", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.742Z", "message": "Found required variable: SUPABASE_URL", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.742Z", "message": "Found required variable: SUPABASE_KEY", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.743Z", "message": "Found required variable: JWT_REFRESH_SECRET", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.744Z", "message": "Found required variable: RAZORPAY_KEY_ID", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.746Z", "message": "Found required variable: RAZORPAY_KEY_SECRET", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.754Z", "message": "Found optional variable: JWT_ACCESS_EXPIRY", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.755Z", "message": "Found optional variable: JWT_REFRESH_EXPIRY", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.757Z", "message": "Missing optional environment variable: PAYMENT_TIMEOUT_MINUTES (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.757Z", "message": "Missing optional environment variable: DISABLE_FRAUD_DETECTION (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.757Z", "message": "✅ All required environment variables are present", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:28:44.758Z", "message": "Validating Supabase connection...", "type": "info", "category": "database"}, {"timestamp": "2025-07-31T07:28:45.861Z", "message": "Supabase connection successful", "type": "success", "category": "database"}, {"timestamp": "2025-07-31T07:28:45.867Z", "message": "Validating Razorpay credentials...", "type": "info", "category": "payment"}, {"timestamp": "2025-07-31T07:28:45.870Z", "message": "Razorpay credentials format validation passed", "type": "success", "category": "payment"}, {"timestamp": "2025-07-31T07:28:45.881Z", "message": "Validating build output...", "type": "info", "category": "build"}, {"timestamp": "2025-07-31T07:28:45.897Z", "message": "Build output is older than 1 hour, consider rebuilding", "type": "warn", "category": "build"}, {"timestamp": "2025-07-31T07:28:45.912Z", "message": "Build output validation passed", "type": "success", "category": "build"}, {"timestamp": "2025-07-31T07:28:45.915Z", "message": "✅ All pre-deployment validations passed", "type": "success", "category": "validation"}]}