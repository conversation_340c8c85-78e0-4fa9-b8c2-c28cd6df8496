/**
 * FAQ Service
 * Business logic for FAQ operations
 */

import { supabase } from '../../../config/supabase';
import { logger } from '../../../utils/logger';
import { ApiError } from '../../../utils/errorHandler';

export interface CreateFaqCategoryData {
  name: string;
  description?: string;
  displayOrder?: number;
}

export interface CreateFaqItemData {
  categoryId: string;
  question: string;
  answer: string;
  displayOrder?: number;
}

export interface SubmitFaqFeedbackData {
  faqItemId: string;
  userId: string;
  isHelpful: boolean;
  feedbackText?: string;
}

export const faqService = {
  /**
   * Get all FAQ categories
   */
  async getFaqCategories() {
    try {
      const { data, error } = await supabase
        .from('faq_categories')
        .select(`
          *,
          items:faq_items(count)
        `)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        logger.error(`Error getting FAQ categories: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve FAQ categories');
      }

      return data || [];
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get all FAQ items
   */
  async getFaqItems() {
    try {
      const { data, error } = await supabase
        .from('faq_items')
        .select(`
          *,
          category:faq_categories(id, name)
        `)
        .eq('is_active', true)
        .not('question', 'is', null)
        .not('answer', 'is', null)
        .order('display_order', { ascending: true });

      if (error) {
        logger.error(`Error getting FAQ items: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve FAQ items');
      }

      return data || [];
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get FAQ items by category
   */
  async getFaqItemsByCategory(categoryId: string) {
    try {
      const { data, error } = await supabase
        .from('faq_items')
        .select(`
          *,
          category:faq_categories(id, name)
        `)
        .eq('category_id', categoryId)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        logger.error(`Error getting FAQ items by category: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve FAQ items');
      }

      return data || [];
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get FAQ item by ID
   */
  async getFaqItemById(itemId: string) {
    try {
      const { data, error } = await supabase
        .from('faq_items')
        .select(`
          *,
          category:faq_categories(id, name)
        `)
        .eq('id', itemId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Item not found
        }
        logger.error(`Error getting FAQ item by ID: ${error.message}`);
        throw new ApiError(500, 'Failed to retrieve FAQ item');
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Increment FAQ view count
   */
  async incrementFaqViewCount(itemId: string) {
    try {
      const { error } = await supabase
        .from('faq_items')
        .update({
          view_count: supabase.raw('view_count + 1')
        })
        .eq('id', itemId);

      if (error) {
        logger.error(`Error incrementing FAQ view count: ${error.message}`);
        // Don't throw error for view count increment failure
      }
    } catch (error) {
      logger.error(`FAQ service error: ${error}`);
      // Don't throw error for view count increment failure
    }
  },

  /**
   * Submit FAQ feedback
   */
  async submitFaqFeedback(data: SubmitFaqFeedbackData) {
    try {
      // Check if user already provided feedback for this item
      const { data: existingFeedback, error: checkError } = await supabase
        .from('faq_feedback')
        .select('id')
        .eq('faq_item_id', data.faqItemId)
        .eq('user_id', data.userId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        logger.error(`Error checking existing FAQ feedback: ${checkError.message}`);
        throw new ApiError(500, 'Failed to check existing feedback');
      }

      if (existingFeedback) {
        // Update existing feedback
        const { data: feedback, error } = await supabase
          .from('faq_feedback')
          .update({
            is_helpful: data.isHelpful,
            feedback_text: data.feedbackText,
            created_at: new Date().toISOString()
          })
          .eq('id', existingFeedback.id)
          .select()
          .single();

        if (error) {
          logger.error(`Error updating FAQ feedback: ${error.message}`);
          throw new ApiError(500, 'Failed to update feedback');
        }

        return feedback;
      } else {
        // Create new feedback
        const { data: feedback, error } = await supabase
          .from('faq_feedback')
          .insert({
            faq_item_id: data.faqItemId,
            user_id: data.userId,
            is_helpful: data.isHelpful,
            feedback_text: data.feedbackText
          })
          .select()
          .single();

        if (error) {
          logger.error(`Error creating FAQ feedback: ${error.message}`);
          throw new ApiError(500, 'Failed to submit feedback');
        }

        // Update helpful count if feedback is positive
        if (data.isHelpful) {
          await supabase
            .from('faq_items')
            .update({
              helpful_count: supabase.raw('helpful_count + 1')
            })
            .eq('id', data.faqItemId);
        }

        return feedback;
      }
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Create FAQ category
   */
  async createFaqCategory(data: CreateFaqCategoryData) {
    try {
      const { data: category, error } = await supabase
        .from('faq_categories')
        .insert({
          name: data.name,
          description: data.description,
          display_order: data.displayOrder || 0
        })
        .select()
        .single();

      if (error) {
        logger.error(`Error creating FAQ category: ${error.message}`);
        throw new ApiError(500, 'Failed to create FAQ category');
      }

      return category;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Update FAQ category
   */
  async updateFaqCategory(categoryId: string, updates: any) {
    try {
      const { data, error } = await supabase
        .from('faq_categories')
        .update(updates)
        .eq('id', categoryId)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Category not found
        }
        logger.error(`Error updating FAQ category: ${error.message}`);
        throw new ApiError(500, 'Failed to update FAQ category');
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Delete FAQ category
   */
  async deleteFaqCategory(categoryId: string) {
    try {
      // Check if category has items
      const { count, error: countError } = await supabase
        .from('faq_items')
        .select('*', { count: 'exact', head: true })
        .eq('category_id', categoryId);

      if (countError) {
        logger.error(`Error checking FAQ items count: ${countError.message}`);
        throw new ApiError(500, 'Failed to check category items');
      }

      if (count && count > 0) {
        throw new ApiError(400, 'Cannot delete category with existing items');
      }

      const { error } = await supabase
        .from('faq_categories')
        .delete()
        .eq('id', categoryId);

      if (error) {
        logger.error(`Error deleting FAQ category: ${error.message}`);
        throw new ApiError(500, 'Failed to delete FAQ category');
      }

      return true;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Create FAQ item
   */
  async createFaqItem(data: CreateFaqItemData) {
    try {
      const { data: item, error } = await supabase
        .from('faq_items')
        .insert({
          category_id: data.categoryId,
          question: data.question,
          answer: data.answer,
          display_order: data.displayOrder || 0
        })
        .select(`
          *,
          category:faq_categories(id, name)
        `)
        .single();

      if (error) {
        logger.error(`Error creating FAQ item: ${error.message}`);
        throw new ApiError(500, 'Failed to create FAQ item');
      }

      return item;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Update FAQ item
   */
  async updateFaqItem(itemId: string, updates: any) {
    try {
      const { data, error } = await supabase
        .from('faq_items')
        .update(updates)
        .eq('id', itemId)
        .select(`
          *,
          category:faq_categories(id, name)
        `)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Item not found
        }
        logger.error(`Error updating FAQ item: ${error.message}`);
        throw new ApiError(500, 'Failed to update FAQ item');
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Delete FAQ item
   */
  async deleteFaqItem(itemId: string) {
    try {
      const { error } = await supabase
        .from('faq_items')
        .delete()
        .eq('id', itemId);

      if (error) {
        logger.error(`Error deleting FAQ item: ${error.message}`);
        throw new ApiError(500, 'Failed to delete FAQ item');
      }

      return true;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  },

  /**
   * Get FAQ analytics
   */
  async getFaqAnalytics() {
    try {
      // Get total counts
      const { count: totalCategories } = await supabase
        .from('faq_categories')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      const { count: totalItems } = await supabase
        .from('faq_items')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      const { count: totalFeedback } = await supabase
        .from('faq_feedback')
        .select('*', { count: 'exact', head: true });

      // Get most viewed items
      const { data: mostViewed, error: viewedError } = await supabase
        .from('faq_items')
        .select(`
          id,
          question,
          view_count,
          category:faq_categories(name)
        `)
        .eq('is_active', true)
        .order('view_count', { ascending: false })
        .limit(10);

      if (viewedError) {
        logger.error(`Error getting most viewed FAQ items: ${viewedError.message}`);
      }

      // Get most helpful items
      const { data: mostHelpful, error: helpfulError } = await supabase
        .from('faq_items')
        .select(`
          id,
          question,
          helpful_count,
          category:faq_categories(name)
        `)
        .eq('is_active', true)
        .order('helpful_count', { ascending: false })
        .limit(10);

      if (helpfulError) {
        logger.error(`Error getting most helpful FAQ items: ${helpfulError.message}`);
      }

      return {
        totalCategories: totalCategories || 0,
        totalItems: totalItems || 0,
        totalFeedback: totalFeedback || 0,
        mostViewed: mostViewed || [],
        mostHelpful: mostHelpful || []
      };
    } catch (error) {
      logger.error(`FAQ service error: ${error}`);
      throw new ApiError(500, 'Internal server error');
    }
  }
};
