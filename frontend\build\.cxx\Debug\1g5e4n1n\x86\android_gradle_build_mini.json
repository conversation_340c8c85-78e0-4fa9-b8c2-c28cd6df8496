{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Node\\wiggyz_backup\\frontend\\build\\.cxx\\Debug\\1g5e4n1n\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Node\\wiggyz_backup\\frontend\\build\\.cxx\\Debug\\1g5e4n1n\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}