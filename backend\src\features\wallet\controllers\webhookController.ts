/**
 * Webhook Controller for handling payment gateway webhooks
 * Specifically designed for secure Razorpay webhook processing
 */
import { Request, Response } from 'express';
import { razorpayService, WebhookEvent } from '../../../services/razorpayService';
import { walletService } from '../services/walletService';
import { successResponse, errorResponse } from '../../../utils/responseFormatter';
import { logger } from '../../../utils/logger';
import { supabase } from '../../../config/supabase';

/**
 * Process Razorpay webhook events
 * @route POST /api/v1/wallet/webhook/razorpay
 */
export const processRazorpayWebhook = async (req: Request, res: Response) => {
  try {
    // Get signature from headers
    const signature = req.get('X-Razorpay-Signature');
    if (!signature) {
      logger.warn('Missing Razorpay signature in webhook request', {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      return res.status(400).json(errorResponse('Missing webhook signature'));
    }

    // Get raw body for signature verification
    const body = req.body;
    const bodyString = typeof body === 'string' ? body : JSON.stringify(body);

    // Verify webhook signature
    const isValidSignature = razorpayService.verifyWebhookSignature(bodyString, signature);
    if (!isValidSignature) {
      logger.warn('Invalid Razorpay webhook signature', {
        ip: req.ip,
        signature: signature.substring(0, 20) + '...',
      });
      return res.status(401).json(errorResponse('Invalid webhook signature'));
    }

    // Parse webhook event
    const webhookEvent: WebhookEvent = typeof body === 'string' ? JSON.parse(body) : body;
    
    logger.info('Received valid Razorpay webhook', {
      event: webhookEvent.event,
      entity: webhookEvent.entity,
      accountId: webhookEvent.account_id,
      timestamp: new Date(webhookEvent.created_at * 1000).toISOString()
    });

    // Process the webhook event
    await processWebhookEvent(webhookEvent);

    // Respond with success
    return res.status(200).json(successResponse(null, 'Webhook processed successfully'));
  } catch (error: any) {
    logger.error('Error processing Razorpay webhook', {
      error: error.message,
      ip: req.ip,
      body: req.body
    });
    
    // Always return 200 to prevent webhook retries for processing errors
    return res.status(200).json(errorResponse('Webhook processing failed'));
  }
};

/**
 * Process different types of webhook events
 */
async function processWebhookEvent(event: WebhookEvent): Promise<void> {
  try {
    switch (event.event) {
      case 'payment.authorized':
        await handlePaymentAuthorized(event);
        break;
      case 'payment.captured':
        await handlePaymentCaptured(event);
        break;
      case 'payment.failed':
        await handlePaymentFailed(event);
        break;
      case 'order.paid':
        await handleOrderPaid(event);
        break;
      default:
        logger.info(`Unhandled webhook event: ${event.event}`);
    }
  } catch (error: any) {
    logger.error(`Failed to process webhook event: ${event.event}`, {
      error: error.message,
      event: event.event,
      entity: event.entity
    });
    throw error;
  }
}

/**
 * Handle payment.authorized event
 */
async function handlePaymentAuthorized(event: WebhookEvent): Promise<void> {
  const payment = event.payload.payment?.entity;
  if (!payment) {
    logger.warn('No payment entity in payment.authorized webhook');
    return;
  }

  logger.info('Processing payment.authorized webhook', {
    paymentId: payment.id,
    orderId: payment.order_id,
    amount: payment.amount,
    method: payment.method
  });

  // Update transaction status to processing
  const transactionId = payment.notes?.transaction_id || payment.order_id;
  if (transactionId) {
    await updateTransactionStatus(transactionId, 'processing', {
      razorpay_payment_id: payment.id,
      status: payment.status,
      method: payment.method,
      authorized_at: new Date().toISOString()
    });
  }
}

/**
 * Handle payment.captured event
 */
async function handlePaymentCaptured(event: WebhookEvent): Promise<void> {
  const payment = event.payload.payment?.entity;
  if (!payment) {
    logger.warn('No payment entity in payment.captured webhook');
    return;
  }

  logger.info('Processing payment.captured webhook', {
    paymentId: payment.id,
    orderId: payment.order_id,
    amount: payment.amount,
    method: payment.method
  });

  // Find the transaction and user
  const transactionId = payment.notes?.transaction_id || payment.order_id;
  if (!transactionId) {
    logger.error('No transaction ID found in payment.captured webhook', {
      paymentId: payment.id,
      orderId: payment.order_id
    });
    return;
  }

  // Get transaction details
  const { data: transaction, error: txError } = await supabase
    .from('wallet_transactions')
    .select('user_id, amount, status')
    .eq('id', transactionId)
    .single();

  if (txError || !transaction) {
    logger.error('Transaction not found for payment.captured webhook', {
      transactionId,
      paymentId: payment.id,
      error: txError?.message
    });
    return;
  }

  // Only process if transaction is still pending or processing
  if (transaction.status === 'completed') {
    logger.info('Transaction already completed, skipping webhook processing', {
      transactionId,
      paymentId: payment.id
    });
    return;
  }

  try {
    // Process the successful payment
    await walletService.processSuccessfulPayment(
      transaction.user_id,
      transactionId,
      {
        razorpay_order_id: payment.order_id,
        razorpay_payment_id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        method: payment.method,
        status: payment.status
      }
    );

    logger.info('Payment processed successfully via webhook', {
      transactionId,
      paymentId: payment.id,
      userId: transaction.user_id,
      amount: payment.amount
    });
  } catch (error: any) {
    logger.error('Failed to process payment via webhook', {
      transactionId,
      paymentId: payment.id,
      userId: transaction.user_id,
      error: error.message
    });
  }
}

/**
 * Handle payment.failed event
 */
async function handlePaymentFailed(event: WebhookEvent): Promise<void> {
  const payment = event.payload.payment?.entity;
  if (!payment) {
    logger.warn('No payment entity in payment.failed webhook');
    return;
  }

  logger.info('Processing payment.failed webhook', {
    paymentId: payment.id,
    orderId: payment.order_id,
    amount: payment.amount,
    errorCode: payment.error_code,
    errorDescription: payment.error_description
  });

  // Update transaction status to failed
  const transactionId = payment.notes?.transaction_id || payment.order_id;
  if (transactionId) {
    await updateTransactionStatus(transactionId, 'failed', {
      razorpay_payment_id: payment.id,
      status: payment.status,
      error_code: payment.error_code,
      error_description: payment.error_description,
      failed_at: new Date().toISOString()
    });
  }
}

/**
 * Handle order.paid event
 */
async function handleOrderPaid(event: WebhookEvent): Promise<void> {
  const order = event.payload.order?.entity;
  if (!order) {
    logger.warn('No order entity in order.paid webhook');
    return;
  }

  logger.info('Processing order.paid webhook', {
    orderId: order.id,
    amount: order.amount,
    status: order.status
  });

  // This event is usually fired after payment.captured
  // We can use it for additional verification or logging
}

/**
 * Update transaction status in database
 */
async function updateTransactionStatus(
  transactionId: string, 
  status: string, 
  metadata: Record<string, any>
): Promise<void> {
  try {
    const { error } = await supabase
      .from('wallet_transactions')
      .update({
        status,
        payment_gateway_response: metadata,
        updated_at: new Date().toISOString()
      })
      .eq('id', transactionId);

    if (error) {
      logger.error('Failed to update transaction status', {
        transactionId,
        status,
        error: error.message
      });
    } else {
      logger.info('Transaction status updated', {
        transactionId,
        status
      });
    }
  } catch (error: any) {
    logger.error('Error updating transaction status', {
      transactionId,
      status,
      error: error.message
    });
  }
}
