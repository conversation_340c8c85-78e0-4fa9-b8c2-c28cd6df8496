import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:wiggyz_app/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Support System Integration Tests', () {
    testWidgets('Complete support flow - contact form submission', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Login first (assuming login flow exists)
      await _loginTestUser(tester);

      // Navigate to profile
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();

      // Tap Help & Support button
      await tester.tap(find.text('Help & Support'));
      await tester.pumpAndSettle();

      // Verify we're on the help screen
      expect(find.text('Help & Support'), findsOneWidget);
      expect(find.text('FAQ'), findsOneWidget);
      expect(find.text('Contact Us'), findsOneWidget);
      expect(find.text('Chat'), findsOneWidget);

      // Switch to Contact Us tab
      await tester.tap(find.text('Contact Us'));
      await tester.pumpAndSettle();

      // Fill out the contact form
      await tester.tap(find.byType(DropdownButtonFormField<String>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('General Inquiry'));
      await tester.pumpAndSettle();

      await tester.enterText(
        find.widgetWithText(TextFormField, 'Brief description of your issue'),
        'Integration Test Support Message'
      );
      await tester.pumpAndSettle();

      await tester.enterText(
        find.widgetWithText(TextFormField, 'Please provide detailed information about your issue...'),
        'This is an automated integration test message. Please ignore this message as it is part of the testing process.'
      );
      await tester.pumpAndSettle();

      // Submit the form
      await tester.tap(find.text('Submit Message'));
      await tester.pumpAndSettle();

      // Verify success message
      expect(find.text('Your message has been submitted successfully!'), findsOneWidget);

      // Verify form is cleared
      expect(find.widgetWithText(TextFormField, 'Integration Test Support Message'), findsNothing);
    });

    testWidgets('FAQ interaction flow', (WidgetTester tester) async {
      // Start the app and login
      app.main();
      await tester.pumpAndSettle();
      await _loginTestUser(tester);

      // Navigate to help & support
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Help & Support'));
      await tester.pumpAndSettle();

      // FAQ tab should be selected by default
      expect(find.text('FAQ'), findsOneWidget);

      // Wait for FAQ items to load
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Find and tap on the first FAQ item
      final faqItems = find.byType(ExpansionTile);
      if (faqItems.evaluate().isNotEmpty) {
        await tester.tap(faqItems.first);
        await tester.pumpAndSettle();

        // Verify the FAQ expanded and shows feedback options
        expect(find.text('Was this helpful?'), findsOneWidget);
        expect(find.byIcon(Icons.thumb_up_outlined), findsOneWidget);
        expect(find.byIcon(Icons.thumb_down_outlined), findsOneWidget);

        // Tap helpful button
        await tester.tap(find.byIcon(Icons.thumb_up_outlined));
        await tester.pumpAndSettle();

        // Verify feedback success message
        expect(find.text('Thank you for your feedback!'), findsOneWidget);
      }
    });

    testWidgets('Chat system flow', (WidgetTester tester) async {
      // Start the app and login
      app.main();
      await tester.pumpAndSettle();
      await _loginTestUser(tester);

      // Navigate to help & support
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Help & Support'));
      await tester.pumpAndSettle();

      // Switch to Chat tab
      await tester.tap(find.text('Chat'));
      await tester.pumpAndSettle();

      // Start chat
      await tester.tap(find.text('Start Chat'));
      await tester.pumpAndSettle();

      // Verify we're on the chat screen
      expect(find.text('Support Chat'), findsOneWidget);

      // Wait for chat to initialize
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Send a test message
      const testMessage = 'Hello, this is an integration test message';
      await tester.enterText(find.byType(TextField), testMessage);
      await tester.pumpAndSettle();

      await tester.tap(find.byIcon(Icons.send));
      await tester.pumpAndSettle();

      // Verify message appears in chat
      expect(find.text(testMessage), findsOneWidget);

      // Verify input is cleared
      expect(find.widgetWithText(TextField, testMessage), findsNothing);
    });

    testWidgets('Form validation tests', (WidgetTester tester) async {
      // Start the app and login
      app.main();
      await tester.pumpAndSettle();
      await _loginTestUser(tester);

      // Navigate to contact form
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Help & Support'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Contact Us'));
      await tester.pumpAndSettle();

      // Try to submit empty form
      await tester.tap(find.text('Submit Message'));
      await tester.pumpAndSettle();

      // Verify validation message
      expect(find.text('Please fill in all required fields'), findsOneWidget);

      // Fill only subject
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Brief description of your issue'),
        'Test Subject'
      );
      await tester.pumpAndSettle();

      // Try to submit with missing message
      await tester.tap(find.text('Submit Message'));
      await tester.pumpAndSettle();

      // Should still show validation error
      expect(find.text('Please fill in all required fields'), findsOneWidget);
    });

    testWidgets('Rate limiting test', (WidgetTester tester) async {
      // Start the app and login
      app.main();
      await tester.pumpAndSettle();
      await _loginTestUser(tester);

      // Navigate to contact form
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Help & Support'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Contact Us'));
      await tester.pumpAndSettle();

      // Submit multiple messages quickly to test rate limiting
      for (int i = 0; i < 4; i++) {
        // Select category
        await tester.tap(find.byType(DropdownButtonFormField<String>));
        await tester.pumpAndSettle();
        await tester.tap(find.text('General Inquiry'));
        await tester.pumpAndSettle();

        // Fill form
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Brief description of your issue'),
          'Rate Limit Test Message ${i + 1}'
        );
        await tester.pumpAndSettle();

        await tester.enterText(
          find.widgetWithText(TextFormField, 'Please provide detailed information about your issue...'),
          'This is rate limit test message number ${i + 1}'
        );
        await tester.pumpAndSettle();

        // Submit
        await tester.tap(find.text('Submit Message'));
        await tester.pumpAndSettle();

        if (i < 3) {
          // First 3 should succeed
          expect(find.text('Your message has been submitted successfully!'), findsOneWidget);
        } else {
          // 4th should be rate limited
          expect(find.textContaining('Too many'), findsOneWidget);
        }

        // Wait a bit between submissions
        await tester.pump(const Duration(seconds: 1));
      }
    });

    testWidgets('Navigation and UI consistency test', (WidgetTester tester) async {
      // Start the app and login
      app.main();
      await tester.pumpAndSettle();
      await _loginTestUser(tester);

      // Navigate to help & support
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Help & Support'));
      await tester.pumpAndSettle();

      // Test tab navigation
      await tester.tap(find.text('Contact Us'));
      await tester.pumpAndSettle();
      expect(find.text('Contact Support'), findsOneWidget);

      await tester.tap(find.text('Chat'));
      await tester.pumpAndSettle();
      expect(find.text('Live Chat'), findsOneWidget);

      await tester.tap(find.text('FAQ'));
      await tester.pumpAndSettle();
      // FAQ content should be visible

      // Test back navigation
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();
      expect(find.text('Profile'), findsOneWidget);
    });
  });
}

// Helper function to login test user
Future<void> _loginTestUser(WidgetTester tester) async {
  // This is a simplified login flow - adjust based on your actual login implementation
  
  // Check if already logged in
  if (find.text('Profile').evaluate().isNotEmpty) {
    return; // Already logged in
  }

  // Look for login button or email field
  final emailField = find.byType(TextFormField).first;
  if (emailField.evaluate().isNotEmpty) {
    await tester.enterText(emailField, '<EMAIL>');
    await tester.pumpAndSettle();

    final passwordField = find.byType(TextFormField).last;
    await tester.enterText(passwordField, 'testpassword123');
    await tester.pumpAndSettle();

    await tester.tap(find.text('Login'));
    await tester.pumpAndSettle();
  }

  // Wait for login to complete
  await tester.pumpAndSettle(const Duration(seconds: 3));
}
