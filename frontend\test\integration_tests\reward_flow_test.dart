/// Integration tests for complete reward system flows
/// Tests end-to-end scenarios including UI interactions and state changes

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/providers/reward_provider.dart';
import 'package:wiggyz_app/screens/daily_rewards_screen.dart';
import 'package:wiggyz_app/screens/rewards_screen.dart';
import 'package:wiggyz_app/screens/achievements_screen.dart';
import 'package:wiggyz_app/screens/loyalty_screen.dart';
import 'package:wiggyz_app/services/auth_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Reward System Integration Tests', () {
    late AuthService authService;
    late RewardProvider rewardProvider;

    setUp(() {
      authService = AuthService();
      rewardProvider = RewardProvider(authService);
    });

    testWidgets('Complete daily reward claiming flow', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: DailyRewardsScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Verify daily rewards screen loads
      expect(find.text('Daily Rewards'), findsOneWidget);

      // Look for claimable reward
      final claimButton = find.text('Claim Reward');
      if (claimButton.evaluate().isNotEmpty) {
        // Tap claim button
        await tester.tap(claimButton);
        await tester.pumpAndSettle();

        // Verify success message or state change
        expect(find.textContaining('claimed'), findsOneWidget);
      }
    });

    testWidgets('Achievement progress and claiming flow', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: AchievementsScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Verify achievements screen loads
      expect(find.text('Achievements'), findsOneWidget);

      // Check for achievement cards
      expect(find.byType(Card), findsWidgets);

      // Look for ready to claim achievements
      final readyToClaim = find.text('Ready');
      if (readyToClaim.evaluate().isNotEmpty) {
        // Tap on achievement card
        await tester.tap(readyToClaim.first);
        await tester.pumpAndSettle();

        // Should open achievement details modal
        expect(find.text('Claim Reward'), findsOneWidget);

        // Tap claim button
        await tester.tap(find.text('Claim Reward'));
        await tester.pumpAndSettle();
      }
    });

    testWidgets('Loyalty system navigation and display', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: LoyaltyScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Verify loyalty screen loads
      expect(find.text('Loyalty Program'), findsOneWidget);

      // Check for tab navigation
      expect(find.text('My Status'), findsOneWidget);
      expect(find.text('All Tiers'), findsOneWidget);

      // Tap on All Tiers tab
      await tester.tap(find.text('All Tiers'));
      await tester.pumpAndSettle();

      // Verify tier cards are displayed
      expect(find.byType(Card), findsWidgets);
    });

    testWidgets('Main rewards screen navigation', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: RewardsScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Verify main rewards screen loads
      expect(find.text('Rewards'), findsOneWidget);

      // Check for different sections
      expect(find.text('Daily Rewards'), findsOneWidget);
      expect(find.text('Loyalty Status'), findsOneWidget);
      expect(find.text('Achievements'), findsOneWidget);

      // Test navigation to daily rewards
      final viewAllButton = find.text('View All').first;
      await tester.tap(viewAllButton);
      await tester.pumpAndSettle();

      // Should navigate to daily rewards screen
      expect(find.text('Daily Rewards'), findsOneWidget);
    });

    testWidgets('Error handling and retry flow', (WidgetTester tester) async {
      // Mock network error scenario
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: DailyRewardsScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Look for error state
      final errorText = find.textContaining('error');
      if (errorText.evaluate().isNotEmpty) {
        // Look for retry button
        final retryButton = find.text('Try Again');
        if (retryButton.evaluate().isNotEmpty) {
          await tester.tap(retryButton);
          await tester.pumpAndSettle();
        }
      }
    });

    testWidgets('Offline mode handling', (WidgetTester tester) async {
      // Test offline scenario
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: DailyRewardsScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // In offline mode, should show cached data
      // This test would require mocking network connectivity
    });

    testWidgets('Notification integration', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: RewardsScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Check for notification indicators
      final notificationDot = find.byType(Container);

      // This would test notification badge visibility
      expect(notificationDot, findsWidgets);
    });

    testWidgets('Performance with large data sets', (WidgetTester tester) async {
      // Test with large number of achievements/rewards
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: AchievementsScreen(),
          ),
        ),
      );

      // Measure performance
      final stopwatch = Stopwatch()..start();
      
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      
      // Verify reasonable load time (less than 2 seconds)
      expect(stopwatch.elapsedMilliseconds, lessThan(2000));
    });

    testWidgets('State persistence across navigation', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: RewardsScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Navigate to daily rewards
      await tester.tap(find.text('View All').first);
      await tester.pumpAndSettle();

      // Navigate back
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify state is maintained
      expect(find.text('Rewards'), findsOneWidget);
    });

    testWidgets('Concurrent operations handling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>.value(value: rewardProvider),
          ],
          child: MaterialApp(
            home: DailyRewardsScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Simulate rapid tapping (race condition test)
      final claimButton = find.text('Claim Reward');
      if (claimButton.evaluate().isNotEmpty) {
        // Tap multiple times rapidly
        await tester.tap(claimButton);
        await tester.tap(claimButton);
        await tester.tap(claimButton);
        
        await tester.pumpAndSettle();

        // Should only process one claim
        // This would require checking the actual state
      }
    });
  });

  group('Accessibility Tests', () {
    testWidgets('Screen reader support', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>(
              create: (_) => RewardProvider(AuthService()),
            ),
          ],
          child: MaterialApp(
            home: DailyRewardsScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify semantic labels are present
      expect(find.bySemanticsLabel('Daily Rewards'), findsOneWidget);
    });

    testWidgets('Keyboard navigation support', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<RewardProvider>(
              create: (_) => RewardProvider(AuthService()),
            ),
          ],
          child: MaterialApp(
            home: RewardsScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Test tab navigation
      await tester.sendKeyEvent(LogicalKeyboardKey.tab);
      await tester.pump();

      // Verify focus management
    });
  });
}
