{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-a2d1d669e071e4cd4312.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-e9acdb7234bda8f30b1e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-2cbe68b48d18392177e0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-e9acdb7234bda8f30b1e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-2cbe68b48d18392177e0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-a2d1d669e071e4cd4312.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}