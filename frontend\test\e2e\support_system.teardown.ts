/**
 * Global Teardown for Support System Tests
 * Cleans up test environment and removes test data
 */

import { FullConfig } from '@playwright/test';
import { createClient } from '@supabase/supabase-js';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Tearing down Support System tests...');

  // Initialize Supabase client
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseServiceKey) {
    console.warn('⚠️  SUPABASE_SERVICE_ROLE_KEY not found. Some test cleanup may fail.');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Clean up test data
    await cleanupTestData(supabase);
    
    // Optionally remove test users (commented out to preserve for multiple test runs)
    // await removeTestUsers(supabase);
    
    console.log('✅ Support System test teardown completed successfully');
  } catch (error) {
    console.error('❌ Support System test teardown failed:', error);
  }
}

async function cleanupTestData(supabase: any) {
  console.log('🗑️  Cleaning up test data...');

  try {
    // Get test user IDs
    const { data: testUsers } = await supabase
      .from('users')
      .select('id')
      .in('email', ['<EMAIL>', '<EMAIL>']);

    if (!testUsers || testUsers.length === 0) {
      console.log('ℹ️  No test users found for cleanup');
      return;
    }

    const testUserIds = testUsers.map(user => user.id);

    // Clean up support messages
    const { error: messagesError } = await supabase
      .from('support_messages')
      .delete()
      .in('user_id', testUserIds);

    if (messagesError) {
      console.warn('⚠️  Could not clean up support messages:', messagesError.message);
    } else {
      console.log('✅ Cleaned up support messages');
    }

    // Clean up chat sessions and messages
    const { data: chatSessions } = await supabase
      .from('chat_sessions')
      .select('id')
      .in('user_id', testUserIds);

    if (chatSessions && chatSessions.length > 0) {
      const sessionIds = chatSessions.map(session => session.id);
      
      // Delete chat messages first (due to foreign key constraint)
      const { error: chatMessagesError } = await supabase
        .from('chat_messages')
        .delete()
        .in('chat_session_id', sessionIds);

      if (chatMessagesError) {
        console.warn('⚠️  Could not clean up chat messages:', chatMessagesError.message);
      }

      // Delete chat sessions
      const { error: chatSessionsError } = await supabase
        .from('chat_sessions')
        .delete()
        .in('id', sessionIds);

      if (chatSessionsError) {
        console.warn('⚠️  Could not clean up chat sessions:', chatSessionsError.message);
      } else {
        console.log('✅ Cleaned up chat sessions and messages');
      }
    }

    // Clean up FAQ feedback
    const { error: faqFeedbackError } = await supabase
      .from('faq_feedback')
      .delete()
      .in('user_id', testUserIds);

    if (faqFeedbackError) {
      console.warn('⚠️  Could not clean up FAQ feedback:', faqFeedbackError.message);
    } else {
      console.log('✅ Cleaned up FAQ feedback');
    }

    // Clean up rate limiting data
    const { error: rateLimitError } = await supabase
      .from('rate_limiting')
      .delete()
      .in('user_id', testUserIds);

    if (rateLimitError) {
      console.warn('⚠️  Could not clean up rate limiting data:', rateLimitError.message);
    } else {
      console.log('✅ Cleaned up rate limiting data');
    }

    // Clean up request logs
    const { error: requestLogsError } = await supabase
      .from('request_logs')
      .delete()
      .in('user_id', testUserIds);

    if (requestLogsError) {
      console.warn('⚠️  Could not clean up request logs:', requestLogsError.message);
    } else {
      console.log('✅ Cleaned up request logs');
    }

    // Clean up test FAQ items
    const { error: testFaqError } = await supabase
      .from('faq_items')
      .delete()
      .eq('display_order', 999); // Test items have display_order 999

    if (testFaqError) {
      console.warn('⚠️  Could not clean up test FAQ items:', testFaqError.message);
    } else {
      console.log('✅ Cleaned up test FAQ items');
    }

  } catch (error) {
    console.warn('⚠️  Error during test data cleanup:', error);
  }
}

async function removeTestUsers(supabase: any) {
  console.log('👥 Removing test users...');

  const testEmails = ['<EMAIL>', '<EMAIL>'];

  for (const email of testEmails) {
    try {
      // Get user data
      const { data: user } = await supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .single();

      if (user) {
        // Delete from users table (this should cascade to auth.users)
        const { error: userError } = await supabase
          .from('users')
          .delete()
          .eq('id', user.id);

        if (userError) {
          console.warn(`⚠️  Could not delete user ${email}:`, userError.message);
        }

        // Delete from auth.users
        const { error: authError } = await supabase.auth.admin.deleteUser(user.id);

        if (authError) {
          console.warn(`⚠️  Could not delete auth user ${email}:`, authError.message);
        } else {
          console.log(`✅ Removed test user: ${email}`);
        }
      }
    } catch (error) {
      console.warn(`⚠️  Error removing user ${email}:`, error);
    }
  }
}

export default globalTeardown;
