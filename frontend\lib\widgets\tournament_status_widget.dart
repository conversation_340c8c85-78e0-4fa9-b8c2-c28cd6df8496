import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:wiggyz_app/services/tournament_status_service.dart';

/// Widget that displays real-time tournament status updates
class TournamentStatusWidget extends StatefulWidget {
  final String tournamentId;
  final bool showDetailedStatus;
  final VoidCallback? onStatusChange;

  const TournamentStatusWidget({
    super.key,
    required this.tournamentId,
    this.showDetailedStatus = true,
    this.onStatusChange,
  });

  @override
  State<TournamentStatusWidget> createState() => _TournamentStatusWidgetState();
}

class _TournamentStatusWidgetState extends State<TournamentStatusWidget>
    with SingleTickerProviderStateMixin {
  final TournamentStatusService _statusService = TournamentStatusService();
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  Map<String, dynamic>? _currentStatus;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startStatusPolling();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.repeat(reverse: true);
  }

  void _startStatusPolling() {
    // Start polling for this tournament
    _statusService.startPolling(widget.tournamentId);

    // Listen to status updates
    _statusService.addListener(_onStatusUpdate);

    // Get initial cached status
    _currentStatus = _statusService.getCachedStatus(widget.tournamentId);
  }

  void _onStatusUpdate() {
    final newStatus = _statusService.getCachedStatus(widget.tournamentId);
    if (mounted && newStatus != _currentStatus) {
      setState(() {
        _currentStatus = newStatus;
      });
      widget.onStatusChange?.call();
    }
  }

  @override
  void dispose() {
    _statusService.removeListener(_onStatusUpdate);
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_currentStatus == null) {
      return _buildLoadingStatus();
    }

    return _buildStatusCard();
  }

  Widget _buildLoadingStatus() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF333333)),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'Loading tournament status...',
            style: GoogleFonts.inter(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    final verificationStatus = _currentStatus!['verification_status'];
    final tournamentStatus = _currentStatus!['tournament']?['status'];
    final isWinner = _currentStatus!['is_winner'];
    final statusMessage = _statusService.getStatusMessage(widget.tournamentId);

    final statusColor = _getStatusColor(verificationStatus, tournamentStatus, isWinner);
    final statusIcon = _getStatusIcon(verificationStatus, tournamentStatus, isWinner);
    final isPending = verificationStatus == 'pending';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: statusColor.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: isPending ? _pulseAnimation.value : 1.0,
                    child: Icon(
                      statusIcon,
                      color: statusColor,
                      size: 24,
                    ),
                  );
                },
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  statusMessage,
                  style: GoogleFonts.inter(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          if (widget.showDetailedStatus) ...[
            const SizedBox(height: 12),
            _buildDetailedStatus(),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailedStatus() {
    final verificationStatus = _currentStatus!['verification_status'];
    final tournamentStatus = _currentStatus!['tournament']?['status'];
    final submittedAt = _currentStatus!['submitted_at'];
    final verifiedAt = _currentStatus!['verified_at'];
    final adminNotes = _currentStatus!['admin_notes'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStatusRow('Tournament Status', _getFormattedTournamentStatus(tournamentStatus, verificationStatus)),
        if (verificationStatus != null)
          _buildStatusRow('Verification Status', verificationStatus.toString().toUpperCase()),
        if (submittedAt != null)
          _buildStatusRow('Submitted', _formatDateTime(submittedAt)),
        if (verifiedAt != null)
          _buildStatusRow('Verified', _formatDateTime(verifiedAt)),
        if (adminNotes != null && adminNotes.toString().isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Admin Notes:',
            style: GoogleFonts.inter(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            adminNotes.toString(),
            style: GoogleFonts.inter(
              color: Colors.white.withOpacity(0.9),
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.inter(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.inter(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String? verificationStatus, String? tournamentStatus, bool? isWinner) {
    if (tournamentStatus == 'completed') {
      return isWinner == true ? Colors.green : Colors.blue;
    }

    switch (verificationStatus) {
      case 'pending':
        return Colors.orange;
      case 'verified':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  IconData _getStatusIcon(String? verificationStatus, String? tournamentStatus, bool? isWinner) {
    if (tournamentStatus == 'completed') {
      return isWinner == true ? Icons.emoji_events : Icons.check_circle;
    }

    switch (verificationStatus) {
      case 'pending':
        return Icons.hourglass_empty;
      case 'verified':
        return Icons.check_circle;
      case 'rejected':
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  String _getFormattedTournamentStatus(String? tournamentStatus, String? verificationStatus) {
    // If we have verification status, use that to determine the display
    if (verificationStatus != null) {
      switch (verificationStatus.toLowerCase()) {
        case 'pending':
          return 'WAITING FOR VERIFICATION';
        case 'verified':
          return 'VERIFICATION COMPLETE';
        case 'rejected':
          return 'VERIFICATION FAILED';
        default:
          return 'UNDER REVIEW';
      }
    }

    // Otherwise, use tournament status
    if (tournamentStatus != null) {
      switch (tournamentStatus.toLowerCase()) {
        case 'active':
          return 'TOURNAMENT ACTIVE';
        case 'completed':
          return 'TOURNAMENT COMPLETED';
        case 'upcoming':
          return 'TOURNAMENT UPCOMING';
        case 'registration':
          return 'REGISTRATION OPEN';
        case 'cancelled':
          return 'TOURNAMENT CANCELLED';
        default:
          return tournamentStatus.toUpperCase();
      }
    }

    return 'STATUS LOADING...';
  }

  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inHours < 1) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inDays < 1) {
        return '${difference.inHours}h ago';
      } else {
        return '${difference.inDays}d ago';
      }
    } catch (e) {
      return 'Unknown';
    }
  }
}
