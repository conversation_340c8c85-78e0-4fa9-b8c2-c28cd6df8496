/**
 * Admin controller for managing rewards system
 * Provides CRUD operations for daily rewards, loyalty tiers, and analytics
 */
import { Request, Response } from 'express';
import { supabase } from '../../../config/supabase';
import { formatResponse } from '../../../utils/responseFormatter';
import { logger } from '../../../utils/logger';

// ==================== DAILY REWARDS MANAGEMENT ====================

/**
 * Get all daily rewards for admin management
 */
export const getAllDailyRewards = async (req: Request, res: Response) => {
  try {
    const { data: rewards, error } = await supabase
      .from('rewards')
      .select(`
        id,
        title,
        description,
        points,
        diamond_value,
        requirements,
        created_at,
        updated_at,
        reward_types:type_id (
          name,
          icon
        )
      `)
      .eq('type_id', '3a5f9e7d-cb09-4cae-8f3b-a7f43e2c8bb6') // daily_login type
      .order('requirements->day', { ascending: true });

    if (error) throw error;

    return formatResponse(res, 200, 'Daily rewards retrieved successfully', rewards || []);
  } catch (error) {
    logger.error(`Error in getAllDailyRewards: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to retrieve daily rewards', null, error);
  }
};

/**
 * Create a new daily reward
 */
export const createDailyReward = async (req: Request, res: Response) => {
  try {
    const { title, description, points, diamond_value, day } = req.body;

    // Check if reward for this day already exists
    const { data: existingReward, error: checkError } = await supabase
      .from('rewards')
      .select('id')
      .eq('type_id', '3a5f9e7d-cb09-4cae-8f3b-a7f43e2c8bb6')
      .eq('requirements->day', day)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingReward) {
      return formatResponse(res, 400, `Daily reward for day ${day} already exists`, null);
    }

    const { data: newReward, error } = await supabase
      .from('rewards')
      .insert({
        title,
        description,
        points,
        diamond_value,
        type_id: '3a5f9e7d-cb09-4cae-8f3b-a7f43e2c8bb6', // daily_login type
        requirements: { day: parseInt(day) },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;

    return formatResponse(res, 201, 'Daily reward created successfully', newReward);
  } catch (error) {
    logger.error(`Error in createDailyReward: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to create daily reward', null, error);
  }
};

/**
 * Update an existing daily reward
 */
export const updateDailyReward = async (req: Request, res: Response) => {
  try {
    const { rewardId } = req.params;
    const { title, description, points, diamond_value, day } = req.body;

    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (points !== undefined) updateData.points = points;
    if (diamond_value !== undefined) updateData.diamond_value = diamond_value;
    if (day !== undefined) updateData.requirements = { day: parseInt(day) };

    const { data: updatedReward, error } = await supabase
      .from('rewards')
      .update(updateData)
      .eq('id', rewardId)
      .eq('type_id', '3a5f9e7d-cb09-4cae-8f3b-a7f43e2c8bb6') // Ensure it's a daily reward
      .select()
      .single();

    if (error) throw error;

    if (!updatedReward) {
      return formatResponse(res, 404, 'Daily reward not found', null);
    }

    return formatResponse(res, 200, 'Daily reward updated successfully', updatedReward);
  } catch (error) {
    logger.error(`Error in updateDailyReward: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to update daily reward', null, error);
  }
};

/**
 * Delete a daily reward
 */
export const deleteDailyReward = async (req: Request, res: Response) => {
  try {
    const { rewardId } = req.params;

    // Check if reward has been claimed by users
    const { data: userRewards, error: checkError } = await supabase
      .from('user_rewards')
      .select('id')
      .eq('reward_id', rewardId)
      .limit(1);

    if (checkError) throw checkError;

    if (userRewards && userRewards.length > 0) {
      return formatResponse(res, 400, 'Cannot delete reward that has been claimed by users', null);
    }

    const { error } = await supabase
      .from('rewards')
      .delete()
      .eq('id', rewardId)
      .eq('type_id', '3a5f9e7d-cb09-4cae-8f3b-a7f43e2c8bb6'); // Ensure it's a daily reward

    if (error) throw error;

    return formatResponse(res, 200, 'Daily reward deleted successfully', null);
  } catch (error) {
    logger.error(`Error in deleteDailyReward: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to delete daily reward', null, error);
  }
};

// ==================== LOYALTY TIERS MANAGEMENT ====================

/**
 * Get all loyalty tiers for admin management
 */
export const getAllLoyaltyTiers = async (req: Request, res: Response) => {
  try {
    const { data: tiers, error } = await supabase
      .from('loyalty_tiers')
      .select('*')
      .order('min_points', { ascending: true });

    if (error) throw error;

    return formatResponse(res, 200, 'Loyalty tiers retrieved successfully', tiers || []);
  } catch (error) {
    logger.error(`Error in getAllLoyaltyTiers: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to retrieve loyalty tiers', null, error);
  }
};

/**
 * Create a new loyalty tier
 */
export const createLoyaltyTier = async (req: Request, res: Response) => {
  try {
    const { name, description, min_points, benefits, icon } = req.body;

    const { data: newTier, error } = await supabase
      .from('loyalty_tiers')
      .insert({
        name,
        description,
        min_points,
        benefits,
        icon,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;

    return formatResponse(res, 201, 'Loyalty tier created successfully', newTier);
  } catch (error) {
    logger.error(`Error in createLoyaltyTier: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to create loyalty tier', null, error);
  }
};

/**
 * Update an existing loyalty tier
 */
export const updateLoyaltyTier = async (req: Request, res: Response) => {
  try {
    const { tierId } = req.params;
    const { name, description, min_points, benefits, icon } = req.body;

    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (min_points !== undefined) updateData.min_points = min_points;
    if (benefits !== undefined) updateData.benefits = benefits;
    if (icon !== undefined) updateData.icon = icon;

    const { data: updatedTier, error } = await supabase
      .from('loyalty_tiers')
      .update(updateData)
      .eq('id', tierId)
      .select()
      .single();

    if (error) throw error;

    if (!updatedTier) {
      return formatResponse(res, 404, 'Loyalty tier not found', null);
    }

    return formatResponse(res, 200, 'Loyalty tier updated successfully', updatedTier);
  } catch (error) {
    logger.error(`Error in updateLoyaltyTier: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to update loyalty tier', null, error);
  }
};

/**
 * Delete a loyalty tier
 */
export const deleteLoyaltyTier = async (req: Request, res: Response) => {
  try {
    const { tierId } = req.params;

    // Check if users are currently in this tier
    const { data: usersInTier, error: checkError } = await supabase
      .from('user_loyalty')
      .select('id')
      .eq('tier_id', tierId)
      .limit(1);

    if (checkError) throw checkError;

    if (usersInTier && usersInTier.length > 0) {
      return formatResponse(res, 400, 'Cannot delete tier that has active users', null);
    }

    const { error } = await supabase
      .from('loyalty_tiers')
      .delete()
      .eq('id', tierId);

    if (error) throw error;

    return formatResponse(res, 200, 'Loyalty tier deleted successfully', null);
  } catch (error) {
    logger.error(`Error in deleteLoyaltyTier: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to delete loyalty tier', null, error);
  }
};

// ==================== ANALYTICS ====================

/**
 * Get rewards analytics for admin dashboard
 */
export const getRewardsAnalytics = async (req: Request, res: Response) => {
  try {
    // Get basic stats
    const [
      totalRewardsResult,
      totalDiamondsResult,
      dailyClaimsResult,
      loyaltyStatsResult
    ] = await Promise.all([
      supabase.from('user_rewards').select('id', { count: 'exact' }),
      supabase.from('reward_transactions').select('amount.sum()'),
      supabase.from('user_rewards')
        .select('claimed_at')
        .gte('claimed_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()),
      supabase.from('user_loyalty').select('points.sum()')
    ]);

    const analytics = {
      totalRewardsClaimed: totalRewardsResult.count || 0,
      totalDiamondsRewarded: totalDiamondsResult.data?.[0]?.sum || 0,
      weeklyClaimsCount: dailyClaimsResult.data?.length || 0,
      totalLoyaltyPoints: loyaltyStatsResult.data?.[0]?.sum || 0,
      lastUpdated: new Date().toISOString()
    };

    return formatResponse(res, 200, 'Rewards analytics retrieved successfully', analytics);
  } catch (error) {
    logger.error(`Error in getRewardsAnalytics: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to retrieve rewards analytics', null, error);
  }
};

// ==================== USER LOYALTY MANAGEMENT ====================

/**
 * Get all user loyalty data for admin management
 */
export const getAllUserLoyalty = async (req: Request, res: Response) => {
  try {
    // Use raw SQL query since Supabase joins can be tricky
    const { data: userLoyalty, error } = await supabase.rpc('get_user_loyalty_admin', {});

    if (error) {
      // Fallback to manual query if RPC doesn't exist
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('user_loyalty')
        .select('user_id, points, tier_id, updated_at');

      if (fallbackError) throw fallbackError;

      // Get user and tier data separately
      const userIds = fallbackData?.map((ul: any) => ul.user_id) || [];
      const tierIds = fallbackData?.map((ul: any) => ul.tier_id) || [];

      const [usersData, tiersData] = await Promise.all([
        supabase.from('users').select('id, name, email').in('id', userIds),
        supabase.from('loyalty_tiers').select('id, name, min_points').in('id', tierIds)
      ]);

      // Combine the data
      const combinedData = fallbackData?.map((ul: any) => ({
        user_id: ul.user_id,
        points: ul.points,
        tier_id: ul.tier_id,
        updated_at: ul.updated_at,
        users: usersData.data?.find((u: any) => u.id === ul.user_id) || { name: 'Unknown', email: '<EMAIL>' },
        loyalty_tiers: tiersData.data?.find((t: any) => t.id === ul.tier_id) || { name: 'Unknown', min_points: 0 }
      })) || [];

      // Sort by points descending
      combinedData.sort((a: any, b: any) => b.points - a.points);

      return formatResponse(res, 200, 'User loyalty data retrieved successfully', combinedData);
    }

    return formatResponse(res, 200, 'User loyalty data retrieved successfully', userLoyalty || []);
  } catch (error) {
    logger.error(`Error in getAllUserLoyalty: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to retrieve user loyalty data', null, error);
  }
};

/**
 * Adjust user loyalty points (admin only)
 */
export const adjustUserPoints = async (req: Request, res: Response) => {
  try {
    const { user_id, points, reason } = req.body;
    const adminId = req.user?.userId;

    if (!user_id || points === undefined || !reason) {
      return formatResponse(res, 400, 'User ID, points, and reason are required', null);
    }

    // Get current user loyalty data
    const { data: currentLoyalty, error: loyaltyError } = await supabase
      .from('user_loyalty')
      .select('points, tier_id')
      .eq('user_id', user_id)
      .single();

    if (loyaltyError) {
      return formatResponse(res, 404, 'User loyalty record not found', null);
    }

    // Calculate new points total
    const newPoints = Math.max(0, currentLoyalty.points + points); // Ensure points don't go negative

    // Check if user should be upgraded/downgraded to a new tier
    const { data: eligibleTier, error: tierError } = await supabase
      .from('loyalty_tiers')
      .select('id')
      .lte('min_points', newPoints)
      .order('min_points', { ascending: false })
      .limit(1)
      .single();

    if (tierError) {
      logger.warn(`Could not find eligible tier for ${newPoints} points, keeping current tier`);
    }

    // Update user loyalty points and tier if necessary
    const updateData: any = {
      points: newPoints,
      updated_at: new Date().toISOString()
    };

    if (eligibleTier && eligibleTier.id !== currentLoyalty.tier_id) {
      updateData.tier_id = eligibleTier.id;
    }

    const { data: updatedLoyalty, error: updateError } = await supabase
      .from('user_loyalty')
      .update(updateData)
      .eq('user_id', user_id)
      .select()
      .single();

    if (updateError) throw updateError;

    // Log the admin action
    await supabase
      .from('reward_transactions')
      .insert({
        user_id,
        amount: points,
        transaction_type: points > 0 ? 'credit' : 'debit',
        description: `Admin adjustment: ${reason}`,
        metadata: {
          admin_id: adminId,
          reason,
          previous_points: currentLoyalty.points,
          new_points: newPoints
        },
        created_at: new Date().toISOString()
      });

    return formatResponse(res, 200, 'User points adjusted successfully', {
      user_id,
      previous_points: currentLoyalty.points,
      adjustment: points,
      new_points: newPoints,
      reason
    });
  } catch (error) {
    logger.error(`Error in adjustUserPoints: ${error instanceof Error ? error.message : String(error)}`);
    return formatResponse(res, 500, 'Failed to adjust user points', null, error);
  }
};
