// Direct test of the deadline service
const { matchDeadlineService } = require('./dist/features/matches/services/matchDeadlineService.js');

async function testDeadlineProcessing() {
  try {
    console.log('🧪 Testing deadline processing directly...');
    
    // First, let's check if there are any expired matches
    console.log('📋 Checking for expired matches...');
    const expiredMatches = await matchDeadlineService.getExpiredMatches();
    console.log(`Found ${expiredMatches.length} expired matches:`, expiredMatches);
    
    if (expiredMatches.length > 0) {
      console.log('⚡ Processing expired matches...');
      const result = await matchDeadlineService.manualProcessExpiredMatches();
      console.log('✅ Processing completed:', result);
    } else {
      console.log('ℹ️ No expired matches found to process');
    }
    
  } catch (error) {
    console.error('❌ Error testing deadline processing:', error);
  }
}

testDeadlineProcessing();
