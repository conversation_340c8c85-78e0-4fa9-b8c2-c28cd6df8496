class FaqCategory {
  final String id;
  final String name;
  final String? description;
  final int displayOrder;
  final bool isActive;
  final DateTime createdAt;
  final int? itemCount;

  FaqCategory({
    required this.id,
    required this.name,
    this.description,
    required this.displayOrder,
    required this.isActive,
    required this.createdAt,
    this.itemCount,
  });

  factory FaqCategory.fromJson(Map<String, dynamic> json) {
    return FaqCategory(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? 'Unknown Category',
      description: json['description'] as String?,
      displayOrder: json['display_order'] as int? ?? 0,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      itemCount: _parseItemCount(json),
    );
  }

  static int? _parseItemCount(Map<String, dynamic> json) {
    // Handle different response formats from backend
    if (json['items'] != null) {
      final items = json['items'];
      if (items is List) {
        return items.length;
      } else if (items is Map && items['count'] != null) {
        return items['count'] as int?;
      }
    }
    return json['item_count'] as int?;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'display_order': displayOrder,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      if (itemCount != null) 'item_count': itemCount,
    };
  }
}

class FaqItem {
  final String id;
  final String categoryId;
  final String question;
  final String answer;
  final int displayOrder;
  final bool isActive;
  final int viewCount;
  final int helpfulCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final FaqCategory? category;

  FaqItem({
    required this.id,
    required this.categoryId,
    required this.question,
    required this.answer,
    required this.displayOrder,
    required this.isActive,
    required this.viewCount,
    required this.helpfulCount,
    required this.createdAt,
    required this.updatedAt,
    this.category,
  });

  factory FaqItem.fromJson(Map<String, dynamic> json) {
    return FaqItem(
      id: json['id'] as String? ?? '',
      categoryId: json['category_id'] as String? ?? '',
      question: json['question'] as String? ?? 'No question available',
      answer: json['answer'] as String? ?? 'No answer available',
      displayOrder: json['display_order'] as int? ?? 0,
      isActive: json['is_active'] as bool? ?? true,
      viewCount: json['view_count'] as int? ?? 0,
      helpfulCount: json['helpful_count'] as int? ?? 0,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
      category: json['category'] != null
          ? FaqCategory.fromJson(json['category'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category_id': categoryId,
      'question': question,
      'answer': answer,
      'display_order': displayOrder,
      'is_active': isActive,
      'view_count': viewCount,
      'helpful_count': helpfulCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      if (category != null) 'category': category!.toJson(),
    };
  }
}

class FaqFeedback {
  final String id;
  final String faqItemId;
  final String userId;
  final bool isHelpful;
  final String? feedbackText;
  final DateTime createdAt;

  FaqFeedback({
    required this.id,
    required this.faqItemId,
    required this.userId,
    required this.isHelpful,
    this.feedbackText,
    required this.createdAt,
  });

  factory FaqFeedback.fromJson(Map<String, dynamic> json) {
    return FaqFeedback(
      id: json['id'] as String,
      faqItemId: json['faq_item_id'] as String,
      userId: json['user_id'] as String,
      isHelpful: json['is_helpful'] as bool,
      feedbackText: json['feedback_text'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'faq_item_id': faqItemId,
      'user_id': userId,
      'is_helpful': isHelpful,
      'feedback_text': feedbackText,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
