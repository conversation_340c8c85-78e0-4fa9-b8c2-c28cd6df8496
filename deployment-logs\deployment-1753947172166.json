{"timestamp": "2025-07-31T07:32:52.167Z", "logs": [{"timestamp": "2025-07-31T07:32:16.329Z", "message": "Starting deployment to staging...", "type": "info"}, {"timestamp": "2025-07-31T07:32:16.331Z", "message": "Validating deployment prerequisites...", "type": "info"}, {"timestamp": "2025-07-31T07:32:17.675Z", "message": "Vercel CLI is installed", "type": "success"}, {"timestamp": "2025-07-31T07:32:17.676Z", "message": "All prerequisites validated", "type": "success"}, {"timestamp": "2025-07-31T07:32:17.676Z", "message": "Building TypeScript backend...", "type": "info"}, {"timestamp": "2025-07-31T07:32:49.492Z", "message": "Backend build completed successfully", "type": "success"}, {"timestamp": "2025-07-31T07:32:49.493Z", "message": "Deploying to Vercel (staging)...", "type": "info"}, {"timestamp": "2025-07-31T07:32:49.495Z", "message": "Running: ve<PERSON>el", "type": "info"}, {"timestamp": "2025-07-31T07:32:52.165Z", "message": "Vercel deployment failed: Command failed: vercel\nVercel CLI 44.6.5\nError: Command `vercel deploy` requires confirmation. Use option \"--yes\" to confirm.\n", "type": "error"}, {"timestamp": "2025-07-31T07:32:52.165Z", "message": "Deployment failed: Command failed: vercel\nVercel CLI 44.6.5\nError: Command `vercel deploy` requires confirmation. Use option \"--yes\" to confirm.\n", "type": "error"}]}