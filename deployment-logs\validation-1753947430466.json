{"timestamp": "2025-07-31T07:37:10.467Z", "results": [{"timestamp": "2025-07-31T07:37:09.398Z", "message": "Loaded environment variables from backend/.env", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T07:37:09.488Z", "message": "🔍 Running post-deployment validation...", "type": "info", "category": "validation"}, {"timestamp": "2025-07-31T07:37:09.496Z", "message": "Validating deployed endpoint: https://wiggyz-backend-fxui5976h-tausifraja977-gmailcoms-projects.vercel.app", "type": "info", "category": "deployment"}, {"timestamp": "2025-07-31T07:37:10.465Z", "message": "Health endpoint failed: undefined", "type": "error", "category": "deployment"}, {"timestamp": "2025-07-31T07:37:10.466Z", "message": "❌ Post-deployment validation failed", "type": "error", "category": "validation"}]}