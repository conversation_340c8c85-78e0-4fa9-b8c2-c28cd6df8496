/**
 * Razorpay Configuration Test Script
 * Run this to verify your Razorpay credentials are working
 */

require('dotenv').config();
const Razorpay = require('razorpay');

async function testRazorpayConfig() {
  console.log('🧪 Testing Razorpay Configuration...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log(`RAZORPAY_KEY_ID: ${process.env.RAZORPAY_KEY_ID ? '✅ Set' : '❌ Missing'}`);
  console.log(`RAZORPAY_KEY_SECRET: ${process.env.RAZORPAY_KEY_SECRET ? '✅ Set' : '❌ Missing'}`);
  console.log(`RAZORPAY_WEBHOOK_SECRET: ${process.env.RAZORPAY_WEBHOOK_SECRET ? '✅ Set' : '❌ Missing'}\n`);

  // Check if credentials are still placeholders
  if (process.env.RAZORPAY_KEY_ID?.includes('REPLACE_WITH_YOUR_ACTUAL')) {
    console.log('❌ ERROR: Razorpay credentials are still placeholder values!');
    console.log('Please update your .env file with actual Razorpay credentials.\n');
    return;
  }

  // Validate credential formats
  console.log('🔍 Validating credential formats...');
  const keyId = process.env.RAZORPAY_KEY_ID;
  const keySecret = process.env.RAZORPAY_KEY_SECRET;

  if (keyId) {
    console.log(`Key ID: ${keyId}`);
    if (!keyId.startsWith('rzp_test_') && !keyId.startsWith('rzp_live_')) {
      console.log('⚠️  WARNING: Key ID should start with "rzp_test_" or "rzp_live_"');
    }
    if (keyId.length < 20) {
      console.log('⚠️  WARNING: Key ID seems too short');
    }
  }

  if (keySecret) {
    console.log(`Key Secret: ${keySecret.substring(0, 8)}...${keySecret.substring(keySecret.length - 4)} (${keySecret.length} chars)`);
    if (keySecret.length < 20) {
      console.log('⚠️  WARNING: Key Secret seems too short');
    }
  }

  if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
    console.log('❌ ERROR: Missing Razorpay credentials!');
    console.log('Please set RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET in your .env file.\n');
    return;
  }

  try {
    // Initialize Razorpay
    console.log('🔧 Initializing Razorpay instance...');
    const razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_KEY_SECRET,
    });

    console.log('✅ Razorpay instance created successfully');

    // Test basic connectivity first
    console.log('\n🌐 Testing Razorpay API connectivity...');

    // Test creating an order with minimal data
    console.log('📦 Testing order creation...');
    const orderOptions = {
      amount: 10000, // ₹100 in paise
      currency: 'INR',
      receipt: 'test_receipt_' + Date.now(),
      notes: {
        test: 'true',
        purpose: 'configuration_test'
      }
    };

    console.log('Order options:', JSON.stringify(orderOptions, null, 2));

    const order = await razorpay.orders.create(orderOptions);
    console.log('✅ Order created successfully!');
    console.log(`   Order ID: ${order.id}`);
    console.log(`   Amount: ₹${order.amount / 100}`);
    console.log(`   Status: ${order.status}`);
    console.log(`   Currency: ${order.currency}`);
    console.log(`   Receipt: ${order.receipt}\n`);

    // Test fetching the order
    console.log('🔍 Testing order fetch...');
    const fetchedOrder = await razorpay.orders.fetch(order.id);
    console.log('✅ Order fetched successfully!');
    console.log(`   Fetched Order ID: ${fetchedOrder.id}`);
    console.log(`   Status: ${fetchedOrder.status}`);
    console.log(`   Created At: ${new Date(fetchedOrder.created_at * 1000).toISOString()}\n`);

    console.log('🎉 All tests passed! Your Razorpay configuration is working correctly.');
    console.log('\n📝 Next steps:');
    console.log('1. Test the payment flow in your Flutter app');
    console.log('2. Use test card numbers for testing:');
    console.log('   - Success: 4111 1111 1111 1111');
    console.log('   - Failure: 4000 0000 0000 0002');
    console.log('3. Monitor backend logs during payment testing');
    console.log('4. Set up webhook endpoint in Razorpay dashboard');

  } catch (error) {
    console.log('❌ ERROR: Razorpay test failed!');

    // Enhanced error handling
    console.log('\n🔍 Error Details:');
    console.log('Error Object:', error);
    console.log('Error Type:', typeof error);
    console.log('Error Constructor:', error?.constructor?.name);

    // Safely extract error message
    const errorMessage = error?.message || error?.error?.description || error?.toString() || 'Unknown error';
    console.log(`Error Message: ${errorMessage}`);

    // Check for specific error types
    if (error?.error) {
      console.log('Razorpay API Error:', error.error);
      if (error.error.code) {
        console.log(`Error Code: ${error.error.code}`);
      }
      if (error.error.description) {
        console.log(`Description: ${error.error.description}`);
      }
    }

    // Check for common error patterns
    const errorStr = errorMessage.toLowerCase();
    if (errorStr.includes('authentication') || errorStr.includes('unauthorized') || errorStr.includes('invalid key')) {
      console.log('\n💡 This looks like an authentication error.');
      console.log('Please check:');
      console.log('1. Your Razorpay Key ID and Secret are correct');
      console.log('2. You\'re using the right environment (test vs live)');
      console.log('3. Your Razorpay account is active');
      console.log('4. Key ID format: should start with "rzp_test_" for test mode');
    }

    if (errorStr.includes('network') || errorStr.includes('timeout') || errorStr.includes('enotfound')) {
      console.log('\n💡 This looks like a network error.');
      console.log('Please check your internet connection and try again.');
    }

    if (errorStr.includes('bad request') || errorStr.includes('400')) {
      console.log('\n💡 This looks like a bad request error.');
      console.log('Please check your request parameters and API key format.');
    }
  }
}

// Run the test
testRazorpayConfig().catch(console.error);
