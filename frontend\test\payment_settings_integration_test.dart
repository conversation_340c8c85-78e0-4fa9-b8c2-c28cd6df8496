import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wiggyz_app/providers/payment_provider.dart';
import 'package:wiggyz_app/screens/payment_settings_screen.dart';
import 'package:wiggyz_app/screens/settings_screen.dart';
import 'package:wiggyz_app/screens/profile_screen.dart';
import 'package:wiggyz_app/theme_provider.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:wiggyz_app/providers/user_provider.dart';

void main() {
  group('Payment Settings Integration Tests', () {
    late PaymentProvider paymentProvider;
    late ThemeProvider themeProvider;
    late AuthProvider authProvider;
    late UserProvider userProvider;

    setUp(() {
      SharedPreferences.setMockInitialValues({});
      paymentProvider = PaymentProvider();
      themeProvider = ThemeProvider();
      authProvider = AuthProvider();
      userProvider = UserProvider();
    });

    tearDown(() {
      paymentProvider.dispose();
      themeProvider.dispose();
      authProvider.dispose();
      userProvider.dispose();
    });

    Widget createTestApp({required Widget home}) {
      return MultiProvider(
        providers: [
          ChangeNotifierProvider<PaymentProvider>.value(value: paymentProvider),
          ChangeNotifierProvider<ThemeProvider>.value(value: themeProvider),
          ChangeNotifierProvider<AuthProvider>.value(value: authProvider),
          ChangeNotifierProvider<UserProvider>.value(value: userProvider),
        ],
        child: MaterialApp(
          home: home,
          theme: ThemeData.light(),
          darkTheme: ThemeData.dark(),
          routes: {
            '/settings': (context) => const SettingsScreen(),
            '/settings/payment': (context) => const PaymentSettingsScreen(),
          },
        ),
      );
    }

    group('Navigation Flow Tests', () {
      testWidgets('should navigate from Profile to Settings to Payment Settings', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp(home: const ProfileScreen()));
        await tester.pumpAndSettle();

        // Find and tap Settings button in profile
        final settingsButton = find.text('Settings');
        expect(settingsButton, findsOneWidget);

        await tester.tap(settingsButton);
        await tester.pumpAndSettle();

        // Should navigate to Settings screen
        expect(find.text('App Settings'), findsOneWidget);
        expect(find.text('Payment Settings'), findsOneWidget);

        // Tap Payment Settings
        await tester.tap(find.text('Payment Settings'));
        await tester.pumpAndSettle();

        // Should navigate to Payment Settings screen
        expect(find.text('Payment Information'), findsOneWidget);
        expect(find.text('Add Payment Method'), findsOneWidget);
      });

      testWidgets('should maintain state across navigation', (WidgetTester tester) async {
        // Initialize with some payment methods
        await paymentProvider.init();
        await paymentProvider.addPaymentMethod(PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - test@bank',
          isDefault: true,
          lastUsed: 'Last used today',
        ));

        await tester.pumpWidget(createTestApp(home: const SettingsScreen()));
        await tester.pumpAndSettle();

        // Navigate to Payment Settings
        await tester.tap(find.text('Payment Settings'));
        await tester.pumpAndSettle();

        // Verify payment method is displayed
        expect(find.text('UPI - test@bank'), findsOneWidget);
        expect(find.text('Default'), findsOneWidget);

        // Navigate back
        await tester.pageBack();
        await tester.pumpAndSettle();

        // Should be back on Settings screen
        expect(find.text('App Settings'), findsOneWidget);

        // Navigate to Payment Settings again
        await tester.tap(find.text('Payment Settings'));
        await tester.pumpAndSettle();

        // State should be preserved
        expect(find.text('UPI - test@bank'), findsOneWidget);
        expect(find.text('Default'), findsOneWidget);
      });
    });

    group('End-to-End Payment Method Management', () {
      testWidgets('should complete full payment method lifecycle', (WidgetTester tester) async {
        await paymentProvider.init();
        await tester.pumpWidget(createTestApp(home: const PaymentSettingsScreen()));
        await tester.pumpAndSettle();

        // Initially should show empty state
        expect(find.text('No Payment Methods'), findsOneWidget);

        // Add a Bitcoin payment method
        await tester.tap(find.text('Bitcoin'));
        await tester.pumpAndSettle();

        // Should open add payment method dialog
        expect(find.text('Add Bitcoin'), findsOneWidget);
        expect(find.text('Bitcoin Wallet Address'), findsOneWidget);

        // Fill in the form
        final addressField = find.byType(TextFormField).first;
        await tester.enterText(addressField, '******************************************');

        final labelField = find.byType(TextFormField).last;
        await tester.enterText(labelField, 'My Bitcoin Wallet');

        // Save the payment method
        await tester.tap(find.text('Add Payment Method'));
        await tester.pumpAndSettle();

        // Should show success message and display the payment method
        expect(find.text('Bitcoin added successfully'), findsOneWidget);
        expect(find.text('My Bitcoin Wallet - bc1qxy...0wlh'), findsOneWidget);
        expect(find.text('Default'), findsOneWidget);

        // Add another payment method (UPI)
        await tester.tap(find.text('UPI'));
        await tester.pumpAndSettle();

        // Fill UPI form
        final upiIdField = find.byType(TextFormField).first;
        await tester.enterText(upiIdField, 'test@okaxis');

        final nameField = find.byType(TextFormField).last;
        await tester.enterText(nameField, 'Test User');

        await tester.tap(find.text('Add Payment Method'));
        await tester.pumpAndSettle();

        // Should now have two payment methods
        expect(find.text('UPI - test@okaxis'), findsOneWidget);
        expect(find.text('My Bitcoin Wallet - bc1qxy...0wlh'), findsOneWidget);

        // Bitcoin should still be default (first added)
        final bitcoinCard = find.ancestor(
          of: find.text('My Bitcoin Wallet - bc1qxy...0wlh'),
          matching: find.byType(Container),
        );
        expect(find.descendant(of: bitcoinCard, matching: find.text('Default')), findsOneWidget);

        // Set UPI as default
        final upiMenuButton = find.descendant(
          of: find.ancestor(
            of: find.text('UPI - test@okaxis'),
            matching: find.byType(Container),
          ),
          matching: find.byIcon(Icons.more_vert),
        );
        await tester.tap(upiMenuButton);
        await tester.pumpAndSettle();

        await tester.tap(find.text('Set as Default'));
        await tester.pumpAndSettle();

        // UPI should now be default
        final upiCard = find.ancestor(
          of: find.text('UPI - test@okaxis'),
          matching: find.byType(Container),
        );
        expect(find.descendant(of: upiCard, matching: find.text('Default')), findsOneWidget);

        // Edit the Bitcoin payment method
        final bitcoinMenuButton = find.descendant(
          of: find.ancestor(
            of: find.text('My Bitcoin Wallet - bc1qxy...0wlh'),
            matching: find.byType(Container),
          ),
          matching: find.byIcon(Icons.more_vert),
        );
        await tester.tap(bitcoinMenuButton);
        await tester.pumpAndSettle();

        await tester.tap(find.text('Edit'));
        await tester.pumpAndSettle();

        // Should open edit screen
        expect(find.text('Edit Bitcoin'), findsOneWidget);

        // Update the label
        final editLabelField = find.byType(TextFormField).last;
        await tester.enterText(editLabelField, 'Updated Bitcoin Wallet');

        await tester.tap(find.text('Save Changes'));
        await tester.pumpAndSettle();

        // Should show updated name
        expect(find.text('Updated Bitcoin Wallet - bc1qxy...0wlh'), findsOneWidget);

        // Delete the Bitcoin payment method
        final updatedBitcoinMenuButton = find.descendant(
          of: find.ancestor(
            of: find.text('Updated Bitcoin Wallet - bc1qxy...0wlh'),
            matching: find.byType(Container),
          ),
          matching: find.byIcon(Icons.more_vert),
        );
        await tester.tap(updatedBitcoinMenuButton);
        await tester.pumpAndSettle();

        await tester.tap(find.text('Delete'));
        await tester.pumpAndSettle();

        // Should show confirmation dialog
        expect(find.text('Delete Payment Method'), findsOneWidget);
        expect(find.text('Are you sure you want to delete this payment method?'), findsOneWidget);

        await tester.tap(find.text('Delete'));
        await tester.pumpAndSettle();

        // Bitcoin method should be removed, only UPI should remain
        expect(find.text('Updated Bitcoin Wallet - bc1qxy...0wlh'), findsNothing);
        expect(find.text('UPI - test@okaxis'), findsOneWidget);
        expect(find.text('Payment method deleted successfully'), findsOneWidget);
      });
    });

    group('Form Validation Tests', () {
      testWidgets('should validate Bitcoin wallet address format', (WidgetTester tester) async {
        await paymentProvider.init();
        await tester.pumpWidget(createTestApp(home: const PaymentSettingsScreen()));
        await tester.pumpAndSettle();

        // Try to add Bitcoin with invalid address
        await tester.tap(find.text('Bitcoin'));
        await tester.pumpAndSettle();

        final addressField = find.byType(TextFormField).first;
        await tester.enterText(addressField, 'invalid-address');

        await tester.tap(find.text('Add Payment Method'));
        await tester.pump();

        // Should show validation error (if validation is implemented)
        // This would depend on the actual validation logic in the form
      });

      testWidgets('should validate UPI ID format', (WidgetTester tester) async {
        await paymentProvider.init();
        await tester.pumpWidget(createTestApp(home: const PaymentSettingsScreen()));
        await tester.pumpAndSettle();

        // Try to add UPI with invalid format
        await tester.tap(find.text('UPI'));
        await tester.pumpAndSettle();

        final upiIdField = find.byType(TextFormField).first;
        await tester.enterText(upiIdField, 'invalid-upi');

        await tester.tap(find.text('Add Payment Method'));
        await tester.pump();

        // Should show validation error (if validation is implemented)
      });

      testWidgets('should require all mandatory fields', (WidgetTester tester) async {
        await paymentProvider.init();
        await tester.pumpWidget(createTestApp(home: const PaymentSettingsScreen()));
        await tester.pumpAndSettle();

        // Try to add Credit Card without filling required fields
        await tester.tap(find.text('Credit Card'));
        await tester.pumpAndSettle();

        // Try to save without filling any fields
        await tester.tap(find.text('Add Payment Method'));
        await tester.pump();

        // Should show validation errors for required fields
        expect(find.text('Please enter card number'), findsOneWidget);
        expect(find.text('Please enter cardholder name'), findsOneWidget);
        expect(find.text('Please enter expiry date'), findsOneWidget);
        expect(find.text('Please enter cvv'), findsOneWidget);
      });
    });

    group('Data Persistence Tests', () {
      testWidgets('should persist payment methods across app restarts', (WidgetTester tester) async {
        // Add a payment method
        await paymentProvider.init();
        await paymentProvider.addPaymentMethod(PaymentMethod(
          type: PaymentType.upi,
          name: 'UPI - persistent@bank',
          isDefault: true,
          lastUsed: 'Added today',
        ));

        await tester.pumpWidget(createTestApp(home: const PaymentSettingsScreen()));
        await tester.pumpAndSettle();

        // Verify payment method is displayed
        expect(find.text('UPI - persistent@bank'), findsOneWidget);

        // Simulate app restart by creating new provider
        final newPaymentProvider = PaymentProvider();
        await newPaymentProvider.init();

        // Create new widget with new provider
        await tester.pumpWidget(MultiProvider(
          providers: [
            ChangeNotifierProvider<PaymentProvider>.value(value: newPaymentProvider),
            ChangeNotifierProvider<ThemeProvider>.value(value: themeProvider),
          ],
          child: MaterialApp(
            home: const PaymentSettingsScreen(),
            theme: ThemeData.light(),
          ),
        ));
        await tester.pumpAndSettle();

        // Payment method should still be there
        expect(find.text('UPI - persistent@bank'), findsOneWidget);
        expect(find.text('Default'), findsOneWidget);

        newPaymentProvider.dispose();
      });
    });

    group('Error Handling Tests', () {
      testWidgets('should handle provider errors gracefully', (WidgetTester tester) async {
        await paymentProvider.init();
        
        // Simulate an error in the provider
        await paymentProvider.updatePaymentMethod(PaymentMethod(
          id: 'non-existent',
          type: PaymentType.upi,
          name: 'Test',
          isDefault: false,
          lastUsed: 'Today',
        ));

        await tester.pumpWidget(createTestApp(home: const PaymentSettingsScreen()));
        await tester.pumpAndSettle();

        // Should show error state
        expect(find.text('Error loading payment methods'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);

        // Should be able to retry
        await tester.tap(find.text('Retry'));
        await tester.pump();

        // Should show loading state
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });
    });
  });
}
