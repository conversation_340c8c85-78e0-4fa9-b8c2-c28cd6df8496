// Test file to verify wallet calculation logic
import 'dart:io';

void main() {
  print('🧪 WALLET CALCULATIONS TEST');
  print('=' * 50);
  
  // Test GST calculation for Add Money
  testGSTCalculation();
  
  // Test withdrawal fee calculation
  testWithdrawalFeeCalculation();
  
  // Test minimum amount validation
  testMinimumAmountValidation();
  
  print('\n✅ All tests completed!');
}

void testGSTCalculation() {
  print('\n📊 Testing GST Calculation (18%)');
  print('-' * 30);
  
  final testCases = [
    {'base': 25.0, 'expectedGST': 4.5, 'expectedTotal': 29.5},
    {'base': 100.0, 'expectedGST': 18.0, 'expectedTotal': 118.0},
    {'base': 500.0, 'expectedGST': 90.0, 'expectedTotal': 590.0},
    {'base': 1000.0, 'expectedGST': 180.0, 'expectedTotal': 1180.0},
  ];
  
  for (final testCase in testCases) {
    final baseAmount = testCase['base'] as double;
    final gstAmount = baseAmount * 0.18;
    final totalAmount = baseAmount + gstAmount;
    
    final expectedGST = testCase['expectedGST'] as double;
    final expectedTotal = testCase['expectedTotal'] as double;
    
    print('Base: ₹${baseAmount.toStringAsFixed(2)} | GST: ₹${gstAmount.toStringAsFixed(2)} | Total: ₹${totalAmount.toStringAsFixed(2)}');
    
    assert(gstAmount == expectedGST, 'GST calculation failed for ₹$baseAmount');
    assert(totalAmount == expectedTotal, 'Total calculation failed for ₹$baseAmount');
  }
  
  print('✅ GST calculations are correct!');
}

void testWithdrawalFeeCalculation() {
  print('\n💸 Testing Withdrawal Fee Calculation (2% with min ₹5, max ₹50)');
  print('-' * 60);
  
  final testCases = [
    {'amount': 100.0, 'expectedFee': 5.0, 'expectedFinal': 95.0}, // Min fee applies
    {'amount': 500.0, 'expectedFee': 10.0, 'expectedFinal': 490.0}, // 2% fee
    {'amount': 1000.0, 'expectedFee': 20.0, 'expectedFinal': 980.0}, // 2% fee
    {'amount': 3000.0, 'expectedFee': 50.0, 'expectedFinal': 2950.0}, // Max fee applies
  ];
  
  for (final testCase in testCases) {
    final amount = testCase['amount'] as double;
    final fee = (amount * 0.02).clamp(5.0, 50.0);
    final finalAmount = amount - fee;
    
    final expectedFee = testCase['expectedFee'] as double;
    final expectedFinal = testCase['expectedFinal'] as double;
    
    print('Amount: ₹${amount.toStringAsFixed(2)} | Fee: ₹${fee.toStringAsFixed(2)} | Final: ₹${finalAmount.toStringAsFixed(2)}');
    
    assert(fee == expectedFee, 'Fee calculation failed for ₹$amount');
    assert(finalAmount == expectedFinal, 'Final amount calculation failed for ₹$amount');
  }
  
  print('✅ Withdrawal fee calculations are correct!');
}

void testMinimumAmountValidation() {
  print('\n🔍 Testing Minimum Amount Validation');
  print('-' * 35);
  
  // Test add money minimum (₹25)
  final addMoneyTestCases = [
    {'amount': 24, 'shouldPass': false},
    {'amount': 25, 'shouldPass': true},
    {'amount': 26, 'shouldPass': true},
    {'amount': 100, 'shouldPass': true},
  ];
  
  print('Add Money Validation (min ₹25):');
  for (final testCase in addMoneyTestCases) {
    final amount = testCase['amount'] as int;
    final shouldPass = testCase['shouldPass'] as bool;
    final isValid = amount >= 25;
    
    print('  ₹$amount: ${isValid ? 'PASS' : 'FAIL'}');
    assert(isValid == shouldPass, 'Add money validation failed for ₹$amount');
  }
  
  // Test withdrawal minimum (₹500)
  final withdrawalTestCases = [
    {'amount': 499, 'shouldPass': false},
    {'amount': 500, 'shouldPass': true},
    {'amount': 501, 'shouldPass': true},
    {'amount': 1000, 'shouldPass': true},
  ];
  
  print('Withdrawal Validation (min ₹500):');
  for (final testCase in withdrawalTestCases) {
    final amount = testCase['amount'] as int;
    final shouldPass = testCase['shouldPass'] as bool;
    final isValid = amount >= 500;
    
    print('  ₹$amount: ${isValid ? 'PASS' : 'FAIL'}');
    assert(isValid == shouldPass, 'Withdrawal validation failed for ₹$amount');
  }
  
  print('✅ Minimum amount validations are correct!');
}
