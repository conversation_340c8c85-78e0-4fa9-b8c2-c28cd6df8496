/**
 * End-to-End Tests for Daily Rewards System
 * Tests the actual API endpoints to verify fixes work correctly
 */

import request from 'supertest';
import { app } from '../../../app';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Advanture101$'
};

describe('Daily Rewards E2E Tests', () => {
  let authToken: string;
  let userId: string;

  beforeAll(async () => {
    // Login to get auth token
    const loginResponse = await request(app)
      .post('/api/v1/auth/login')
      .send(TEST_USER);

    expect(loginResponse.status).toBe(200);
    authToken = loginResponse.body.data.token;
    userId = loginResponse.body.data.user.id;
  });

  describe('Daily Reward Status', () => {
    it('should get daily reward status successfully', async () => {
      const response = await request(app)
        .get('/api/v1/rewards/daily-status')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('streak');
      expect(response.body.data).toHaveProperty('rewards');
      expect(response.body.data).toHaveProperty('hasClaimedToday');
      
      // Verify streak structure
      expect(response.body.data.streak).toHaveProperty('current_streak');
      expect(response.body.data.streak).toHaveProperty('longest_streak');
      
      // Verify rewards array
      expect(Array.isArray(response.body.data.rewards)).toBe(true);
      expect(response.body.data.rewards.length).toBeGreaterThan(0);
    });
  });

  describe('Daily Reward Claiming', () => {
    it('should handle multiple claim attempts correctly', async () => {
      // First claim attempt
      const firstClaimResponse = await request(app)
        .post('/api/v1/rewards/claim-daily')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Idempotency-Key', `test-${Date.now()}`);

      // Should either succeed or fail with "already claimed"
      expect([200, 400]).toContain(firstClaimResponse.status);
      
      if (firstClaimResponse.status === 200) {
        // If successful, verify response structure
        expect(firstClaimResponse.body.success).toBe(true);
        expect(firstClaimResponse.body.data).toHaveProperty('reward');
        expect(firstClaimResponse.body.data).toHaveProperty('streak');
        
        // Second claim attempt should fail
        const secondClaimResponse = await request(app)
          .post('/api/v1/rewards/claim-daily')
          .set('Authorization', `Bearer ${authToken}`)
          .set('X-Idempotency-Key', `test-${Date.now()}-2`);

        expect(secondClaimResponse.status).toBe(400);
        expect(secondClaimResponse.body.message).toContain('already claimed');
      } else {
        // If already claimed, verify error message
        expect(firstClaimResponse.body.message).toContain('already claimed');
      }
    });

    it('should handle idempotency correctly', async () => {
      const idempotencyKey = `idempotent-test-${Date.now()}`;
      
      // Make the same request twice with same idempotency key
      const firstResponse = await request(app)
        .post('/api/v1/rewards/claim-daily')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Idempotency-Key', idempotencyKey);

      const secondResponse = await request(app)
        .post('/api/v1/rewards/claim-daily')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Idempotency-Key', idempotencyKey);

      // Both should have same status (either both succeed or both fail with same error)
      expect(firstResponse.status).toBe(secondResponse.status);
      
      if (firstResponse.status === 400) {
        // Both should have same error message
        expect(firstResponse.body.message).toBe(secondResponse.body.message);
      }
    });
  });

  describe('Loyalty Integration', () => {
    it('should update loyalty points when claiming daily reward', async () => {
      // Get initial loyalty status
      const initialLoyaltyResponse = await request(app)
        .get('/api/v1/rewards/loyalty')
        .set('Authorization', `Bearer ${authToken}`);

      expect(initialLoyaltyResponse.status).toBe(200);
      const initialPoints = initialLoyaltyResponse.body.data.points;

      // Try to claim daily reward
      const claimResponse = await request(app)
        .post('/api/v1/rewards/claim-daily')
        .set('Authorization', `Bearer ${authToken}`)
        .set('X-Idempotency-Key', `loyalty-test-${Date.now()}`);

      // If claim was successful, check loyalty points increased
      if (claimResponse.status === 200) {
        const finalLoyaltyResponse = await request(app)
          .get('/api/v1/rewards/loyalty')
          .set('Authorization', `Bearer ${authToken}`);

        expect(finalLoyaltyResponse.status).toBe(200);
        const finalPoints = finalLoyaltyResponse.body.data.points;
        
        // Points should have increased
        expect(finalPoints).toBeGreaterThan(initialPoints);
      }
      // If already claimed, that's also a valid test result
    });
  });

  describe('Error Handling', () => {
    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/v1/rewards/daily-status');

      expect(response.status).toBe(401);
    });

    it('should handle invalid auth token', async () => {
      const response = await request(app)
        .get('/api/v1/rewards/daily-status')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
    });

    it('should validate claim request', async () => {
      const response = await request(app)
        .post('/api/v1/rewards/claim-daily')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ invalid: 'data' });

      // Should either succeed, fail with already claimed, or fail with validation error
      expect([200, 400, 422]).toContain(response.status);
    });
  });

  describe('Streak Calculation Verification', () => {
    it('should maintain consistent streak information', async () => {
      // Get status multiple times to ensure consistency
      const responses = await Promise.all([
        request(app)
          .get('/api/v1/rewards/daily-status')
          .set('Authorization', `Bearer ${authToken}`),
        request(app)
          .get('/api/v1/rewards/daily-status')
          .set('Authorization', `Bearer ${authToken}`),
        request(app)
          .get('/api/v1/rewards/daily-status')
          .set('Authorization', `Bearer ${authToken}`)
      ]);

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // All should return same streak data
      const streaks = responses.map(r => r.body.data.streak);
      expect(streaks[0]).toEqual(streaks[1]);
      expect(streaks[1]).toEqual(streaks[2]);

      // Streak should be non-negative
      expect(streaks[0].current_streak).toBeGreaterThanOrEqual(0);
      expect(streaks[0].longest_streak).toBeGreaterThanOrEqual(streaks[0].current_streak);
    });
  });

  describe('Admin Endpoints', () => {
    it('should allow admin to view daily rewards', async () => {
      const response = await request(app)
        .get('/api/v1/rewards/admin/daily-rewards')
        .set('Authorization', `Bearer ${authToken}`);

      // Should succeed for admin user
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should allow admin to view loyalty tiers', async () => {
      const response = await request(app)
        .get('/api/v1/rewards/admin/loyalty-tiers')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });
  });
});
