import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wiggyz_app/widgets/shared/golden_app_bar.dart';
import 'package:wiggyz_app/widgets/shared/standard_form_components.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:wiggyz_app/providers/user_provider.dart';
import 'package:wiggyz_app/services/user_service.dart';

class PrivacySecuritySettingsScreen extends StatefulWidget {
  const PrivacySecuritySettingsScreen({super.key});

  @override
  State<PrivacySecuritySettingsScreen> createState() => _PrivacySecuritySettingsScreenState();
}

class _PrivacySecuritySettingsScreenState extends State<PrivacySecuritySettingsScreen> {
  // Form controllers for password change
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  // Privacy settings
  bool _profileVisibility = true;
  bool _showOnlineStatus = true;
  bool _allowFriendRequests = true;
  bool _showGameStats = true;
  bool _dataCollection = true;
  bool _analyticsTracking = false;

  // Security settings
  bool _twoFactorEnabled = false;
  bool _loginNotifications = true;
  bool _deviceTracking = true;

  bool _isLoading = false;
  bool _isSaving = false;
  bool _isChangingPassword = false;
  bool _showPasswordSection = false;

  UserService? _userService;

  @override
  void initState() {
    super.initState();
    _initializeUserService();
    _loadPrivacySettings();
  }

  void _initializeUserService() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    _userService = UserService(userProvider, authProvider);
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _loadPrivacySettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      
      setState(() {
        // Privacy settings
        _profileVisibility = prefs.getBool('profile_visibility') ?? true;
        _showOnlineStatus = prefs.getBool('show_online_status') ?? true;
        _allowFriendRequests = prefs.getBool('allow_friend_requests') ?? true;
        _showGameStats = prefs.getBool('show_game_stats') ?? true;
        _dataCollection = prefs.getBool('data_collection') ?? true;
        _analyticsTracking = prefs.getBool('analytics_tracking') ?? false;

        // Security settings
        _twoFactorEnabled = prefs.getBool('two_factor_enabled') ?? false;
        _loginNotifications = prefs.getBool('login_notifications') ?? true;
        _deviceTracking = prefs.getBool('device_tracking') ?? true;
      });
    } catch (e) {
      debugPrint('Error loading privacy settings: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _savePrivacySettings() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save privacy settings
      await prefs.setBool('profile_visibility', _profileVisibility);
      await prefs.setBool('show_online_status', _showOnlineStatus);
      await prefs.setBool('allow_friend_requests', _allowFriendRequests);
      await prefs.setBool('show_game_stats', _showGameStats);
      await prefs.setBool('data_collection', _dataCollection);
      await prefs.setBool('analytics_tracking', _analyticsTracking);

      // Save security settings
      await prefs.setBool('two_factor_enabled', _twoFactorEnabled);
      await prefs.setBool('login_notifications', _loginNotifications);
      await prefs.setBool('device_tracking', _deviceTracking);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Privacy settings saved successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving privacy settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to save settings. Please try again.',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _changePassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isChangingPassword = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      // Here you would typically call an API to change the password
      // For now, we'll simulate the process
      await Future.delayed(const Duration(seconds: 2));

      // Clear the form
      _currentPasswordController.clear();
      _newPasswordController.clear();
      _confirmPasswordController.clear();

      setState(() {
        _showPasswordSection = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Password changed successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error changing password: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to change password. Please try again.',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isChangingPassword = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: GoldenAppBar(
        title: 'Privacy & Security',
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _savePrivacySettings,
            child: _isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                    ),
                  )
                : Text(
                    'Save',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFFFCC00),
              ),
            )
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoCard(isDarkMode),
                    const SizedBox(height: 24),
                    _buildSecuritySection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildPrivacySection(isDarkMode),
                    const SizedBox(height: 24),
                    _buildDataSection(isDarkMode),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildInfoCard(bool isDarkMode) {
    return StandardCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security_outlined,
                color: isDarkMode ? const Color(0xFFD4AF37) : Colors.red[700],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Privacy & Security',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? const Color(0xFFD4AF37) : Colors.red[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Manage your privacy preferences and security settings to keep your account safe and control how your information is used.',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Security Settings',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        _buildActionButton(
          icon: Icons.lock_outline,
          title: 'Change Password',
          subtitle: 'Update your account password',
          onTap: () {
            setState(() {
              _showPasswordSection = !_showPasswordSection;
            });
          },
        ),
        if (_showPasswordSection) _buildPasswordChangeSection(isDarkMode),
        _buildToggleOption(
          'Two-Factor Authentication',
          'Add an extra layer of security to your account',
          _twoFactorEnabled,
          (value) => setState(() => _twoFactorEnabled = value),
          Icons.verified_user_outlined,
        ),
        _buildToggleOption(
          'Login Notifications',
          'Get notified when someone logs into your account',
          _loginNotifications,
          (value) => setState(() => _loginNotifications = value),
          Icons.login,
        ),
        _buildToggleOption(
          'Device Tracking',
          'Track devices that access your account',
          _deviceTracking,
          (value) => setState(() => _deviceTracking = value),
          Icons.devices,
        ),
      ],
    );
  }

  Widget _buildPasswordChangeSection(bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: StandardCard(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Change Password',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(height: 16),
              StandardTextField(
                label: 'Current Password',
                hint: 'Enter your current password',
                controller: _currentPasswordController,
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your current password';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              StandardTextField(
                label: 'New Password',
                hint: 'Enter your new password',
                controller: _newPasswordController,
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a new password';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              StandardTextField(
                label: 'Confirm New Password',
                hint: 'Confirm your new password',
                controller: _confirmPasswordController,
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please confirm your new password';
                  }
                  if (value != _newPasswordController.text) {
                    return 'Passwords do not match';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: StandardSecondaryButton(
                      text: 'Cancel',
                      onPressed: () {
                        setState(() {
                          _showPasswordSection = false;
                        });
                        _currentPasswordController.clear();
                        _newPasswordController.clear();
                        _confirmPasswordController.clear();
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: StandardPrimaryButton(
                      text: 'Change Password',
                      onPressed: _isChangingPassword ? null : _changePassword,
                      isLoading: _isChangingPassword,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrivacySection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Privacy Settings',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        _buildToggleOption(
          'Profile Visibility',
          'Allow others to view your profile',
          _profileVisibility,
          (value) => setState(() => _profileVisibility = value),
          Icons.visibility_outlined,
        ),
        _buildToggleOption(
          'Show Online Status',
          'Let others see when you\'re online',
          _showOnlineStatus,
          (value) => setState(() => _showOnlineStatus = value),
          Icons.circle,
        ),
        _buildToggleOption(
          'Allow Friend Requests',
          'Let other players send you friend requests',
          _allowFriendRequests,
          (value) => setState(() => _allowFriendRequests = value),
          Icons.person_add_outlined,
        ),
        _buildToggleOption(
          'Show Game Statistics',
          'Display your game stats on your profile',
          _showGameStats,
          (value) => setState(() => _showGameStats = value),
          Icons.bar_chart_outlined,
        ),
      ],
    );
  }

  Widget _buildDataSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Data & Analytics',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        _buildToggleOption(
          'Data Collection',
          'Allow collection of usage data to improve the app',
          _dataCollection,
          (value) => setState(() => _dataCollection = value),
          Icons.data_usage_outlined,
        ),
        _buildToggleOption(
          'Analytics Tracking',
          'Help us understand how you use the app',
          _analyticsTracking,
          (value) => setState(() => _analyticsTracking = value),
          Icons.analytics_outlined,
        ),
        const SizedBox(height: 16),
        _buildActionButton(
          icon: Icons.download_outlined,
          title: 'Download My Data',
          subtitle: 'Get a copy of your personal data',
          onTap: () {
            _showDataDownloadDialog();
          },
        ),
        _buildActionButton(
          icon: Icons.delete_outline,
          title: 'Delete Account',
          subtitle: 'Permanently delete your account and data',
          isDestructive: true,
          onTap: () {
            _showDeleteAccountDialog();
          },
        ),
      ],
    );
  }

  Widget _buildToggleOption(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
    IconData icon,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: StandardCard(
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: onTap,
        child: StandardCard(
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isDestructive
                      ? Colors.red.withOpacity(0.1)
                      : Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: isDestructive ? Colors.red : Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: isDestructive
                            ? Colors.red
                            : (isDarkMode ? Colors.white : Colors.black),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDataDownloadDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Download Your Data',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'We\'ll prepare a copy of your personal data and send it to your registered email address. This may take up to 24 hours.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel', style: GoogleFonts.poppins()),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Data download request submitted. You\'ll receive an email shortly.',
                    style: GoogleFonts.poppins(),
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('Request Download', style: GoogleFonts.poppins()),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Account',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        content: Text(
          'This action cannot be undone. All your data, including game history, achievements, and wallet balance, will be permanently deleted.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel', style: GoogleFonts.poppins()),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Here you would implement the actual account deletion logic
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Account deletion is not available in this demo.',
                    style: GoogleFonts.poppins(),
                  ),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: Text(
              'Delete Account',
              style: GoogleFonts.poppins(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
