# WiggyZ Razorpay Payment Integration Guide

## Overview

This document provides a comprehensive guide for the secure Razorpay payment integration implemented in the WiggyZ application. The integration follows industry best practices for security, compliance, and user experience.

## Architecture

### Security-First Design
- **Server-Side Processing**: All payment operations are handled on the backend
- **No Client-Side Secrets**: Razorpay keys are never exposed to the frontend
- **Signature Verification**: All payments and webhooks are cryptographically verified
- **Rate Limiting**: Comprehensive rate limiting prevents abuse
- **Audit Logging**: All payment operations are logged for compliance

### Components

1. **Backend Services**
   - `razorpayService.ts` - Core Razorpay integration
   - `paymentGateway.ts` - Payment gateway abstraction
   - `walletController.ts` - Payment API endpoints
   - `webhookController.ts` - Webhook processing
   - `paymentSecurity.ts` - Security middleware

2. **Frontend Services**
   - `razorpay_service.dart` - Flutter Razorpay integration
   - `wallet_service.dart` - API communication
   - `add_money_screen.dart` - Payment UI

3. **Database Tables**
   - `wallet_transactions` - Transaction records
   - `payment_audit_logs` - Audit trail
   - `user_security_blocks` - Security blocks
   - `webhook_events` - Webhook processing

## Setup Instructions

### 1. Backend Configuration

#### Environment Variables
```bash
# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_key_secret_here
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret_here

# Payment Gateway Settings
PAYMENT_TIMEOUT_MINUTES=15
PAYMENT_MAX_AMOUNT=10000000  # ₹100,000 in paise
PAYMENT_MIN_AMOUNT=100       # ₹1 in paise
```

#### Install Dependencies
```bash
cd backend
npm install razorpay
```

#### Database Migration
```bash
# Run the payment security migration
psql -d your_database -f supabase/migrations/add_payment_security_tables.sql
```

### 2. Frontend Configuration

#### Dependencies
```yaml
dependencies:
  razorpay_flutter: ^1.3.7
  uuid: ^4.5.1
```

#### Install Dependencies
```bash
cd frontend
flutter pub get
```

### 3. Razorpay Dashboard Setup

1. **Create Razorpay Account**
   - Sign up at https://dashboard.razorpay.com
   - Complete KYC verification
   - Generate API keys

2. **Configure Webhooks**
   - URL: `https://your-domain.com/api/v1/wallet/webhook/razorpay`
   - Events: `payment.authorized`, `payment.captured`, `payment.failed`, `order.paid`
   - Secret: Generate and store in environment variables

3. **Payment Methods**
   - Enable UPI, Cards, Net Banking, Wallets
   - Configure payment limits
   - Set up auto-capture

## API Endpoints

### Payment Initiation
```http
POST /api/v1/wallet/topup
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 100.00,
  "currency": "INR",
  "payment_method": "upi"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "transaction_id": "uuid",
    "order_id": "order_razorpay_id",
    "key_id": "rzp_test_key",
    "amount": 10000,
    "currency": "INR"
  }
}
```

### Payment Verification
```http
POST /api/v1/wallet/verify-payment
Authorization: Bearer <token>
Content-Type: application/json

{
  "razorpay_order_id": "order_id",
  "razorpay_payment_id": "payment_id",
  "razorpay_signature": "signature",
  "transaction_id": "uuid"
}
```

### Webhook Endpoint
```http
POST /api/v1/wallet/webhook/razorpay
X-Razorpay-Signature: <signature>
Content-Type: application/json

{
  "entity": "event",
  "account_id": "acc_id",
  "event": "payment.captured",
  "payload": { ... }
}
```

## Security Features

### 1. Payment Security
- **Signature Verification**: All payments verified using HMAC-SHA256
- **Amount Validation**: Server-side amount limits and validation
- **Timeout Protection**: Payments expire after 15 minutes
- **Idempotency**: Duplicate payment prevention

### 2. Rate Limiting
- **Payment Initiation**: 5 requests per 5 minutes per user
- **Payment Verification**: 10 requests per 5 minutes per user
- **Webhooks**: 100 requests per minute per IP

### 3. Fraud Prevention
- **Suspicious Activity Detection**: Automatic blocking after failed attempts
- **IP Tracking**: Monitor requests from suspicious IPs
- **Device Fingerprinting**: Track payment patterns
- **Temporary Blocks**: Automatic user blocking for suspicious activity

### 4. Audit Logging
- **Comprehensive Logging**: All payment operations logged
- **Request/Response Tracking**: Full audit trail
- **Error Monitoring**: Detailed error tracking
- **Performance Metrics**: Response time monitoring

## Payment Flow

### 1. Frontend Initiation
```dart
// User initiates payment
final result = await _razorpayService.processWalletTopUp(
  context: context,
  amount: amount,
  currency: 'INR',
  userEmail: user.email,
  userPhone: user.phone,
  userName: user.name,
  transactionId: transactionId,
);
```

### 2. Backend Processing
1. Validate request and user authentication
2. Check rate limits and security blocks
3. Create Razorpay order
4. Store transaction record
5. Return order details to frontend

### 3. Payment Processing
1. Frontend opens Razorpay checkout
2. User completes payment
3. Razorpay returns payment details
4. Frontend sends verification request

### 4. Payment Verification
1. Backend verifies payment signature
2. Fetch payment details from Razorpay
3. Update wallet balance
4. Mark transaction as completed
5. Send confirmation to user

### 5. Webhook Processing
1. Razorpay sends webhook events
2. Verify webhook signature
3. Process payment status updates
4. Handle failed/disputed payments
5. Update transaction records

## Error Handling

### Common Error Scenarios
1. **Invalid Payment Signature**: Payment verification fails
2. **Payment Not Captured**: Payment authorized but not captured
3. **Network Errors**: Connection issues with Razorpay
4. **Rate Limiting**: Too many requests
5. **Insufficient Balance**: Wallet balance checks
6. **Expired Transactions**: Payment timeout

### Error Response Format
```json
{
  "success": false,
  "error": "Error code",
  "message": "Human-readable error message",
  "details": {
    "code": "PAYMENT_FAILED",
    "reason": "Invalid signature"
  }
}
```

## Testing

### Test Environment
- Use Razorpay test mode keys
- Test with provided test cards and UPI IDs
- Verify webhook processing with test events

### Test Cards
```
Success: 4111 1111 1111 1111
Failure: 4000 0000 0000 0002
```

### Test UPI IDs
```
Success: success@razorpay
Failure: failure@razorpay
```

### Running Tests
```bash
cd backend
npm test -- razorpay-integration.test.ts
```

## Monitoring and Analytics

### Key Metrics
- Payment success rate
- Average processing time
- Error rates by type
- User conversion rates
- Security incidents

### Logging
- All payment operations logged to `payment_audit_logs`
- Webhook events stored in `webhook_events`
- Security incidents tracked in `user_security_blocks`

### Alerts
- Failed payment rate > 5%
- Webhook processing failures
- Security block triggers
- High error rates

## Compliance

### PCI DSS
- No card data stored on servers
- All payments processed through Razorpay
- Secure transmission of payment data
- Regular security audits

### Data Protection
- User data encrypted in transit and at rest
- Payment data not stored locally
- Audit logs for compliance reporting
- GDPR compliance for user data

## Troubleshooting

### Common Issues
1. **Webhook Signature Mismatch**: Check webhook secret configuration
2. **Payment Verification Fails**: Verify API keys and signature generation
3. **Rate Limiting**: Implement exponential backoff
4. **Database Errors**: Check transaction isolation levels

### Debug Mode
Enable debug logging in development:
```bash
NODE_ENV=development
DEBUG=razorpay:*
```

## Support

### Documentation
- [Razorpay API Documentation](https://razorpay.com/docs/)
- [Flutter Integration Guide](https://razorpay.com/docs/payments/payment-gateway/flutter-plugin/)

### Contact
- Technical Support: <EMAIL>
- Security Issues: <EMAIL>
