/**
 * Razorpay Credential Validation Script
 * This script validates your Razorpay credentials step by step
 */

require('dotenv').config();

function validateCredentials() {
  console.log('🔐 Razorpay Credential Validation\n');

  const keyId = process.env.RAZORPAY_KEY_ID;
  const keySecret = process.env.RAZORPAY_KEY_SECRET;
  const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;

  console.log('📋 Environment Variables Check:');
  console.log(`RAZORPAY_KEY_ID: ${keyId ? '✅ Present' : '❌ Missing'}`);
  console.log(`RAZORPAY_KEY_SECRET: ${keySecret ? '✅ Present' : '❌ Missing'}`);
  console.log(`RAZORPAY_WEBHOOK_SECRET: ${webhookSecret ? '✅ Present' : '❌ Missing'}\n`);

  if (!keyId || !keySecret) {
    console.log('❌ Missing required credentials. Please check your .env file.\n');
    return false;
  }

  console.log('🔍 Credential Format Validation:');
  
  // Validate Key ID
  console.log(`\n📝 Key ID Analysis:`);
  console.log(`   Value: ${keyId}`);
  console.log(`   Length: ${keyId.length} characters`);
  
  if (keyId.startsWith('rzp_test_')) {
    console.log('   ✅ Correct test mode prefix');
  } else if (keyId.startsWith('rzp_live_')) {
    console.log('   ✅ Correct live mode prefix');
  } else {
    console.log('   ❌ Invalid prefix. Should start with "rzp_test_" or "rzp_live_"');
    return false;
  }

  if (keyId.length >= 20 && keyId.length <= 30) {
    console.log('   ✅ Reasonable length');
  } else {
    console.log('   ⚠️  Unusual length for Razorpay Key ID');
  }

  // Validate Key Secret
  console.log(`\n🔑 Key Secret Analysis:`);
  console.log(`   Length: ${keySecret.length} characters`);
  console.log(`   Preview: ${keySecret.substring(0, 8)}...${keySecret.substring(keySecret.length - 4)}`);
  
  if (keySecret.length >= 20 && keySecret.length <= 50) {
    console.log('   ✅ Reasonable length');
  } else {
    console.log('   ⚠️  Unusual length for Razorpay Key Secret');
  }

  // Check for common issues
  console.log(`\n🔍 Common Issues Check:`);
  
  if (keyId.includes('REPLACE_WITH') || keySecret.includes('REPLACE_WITH')) {
    console.log('   ❌ Contains placeholder text');
    return false;
  } else {
    console.log('   ✅ No placeholder text found');
  }

  if (keyId.includes(' ') || keySecret.includes(' ')) {
    console.log('   ❌ Contains spaces (should be removed)');
    return false;
  } else {
    console.log('   ✅ No spaces found');
  }

  // Check for duplicate prefixes
  const prefixCount = (keyId.match(/rzp_test_/g) || []).length + (keyId.match(/rzp_live_/g) || []).length;
  if (prefixCount > 1) {
    console.log('   ❌ Duplicate prefix detected');
    return false;
  } else {
    console.log('   ✅ No duplicate prefixes');
  }

  console.log('\n✅ All credential validations passed!');
  console.log('\n📝 Next Steps:');
  console.log('1. Run: node test-razorpay.js');
  console.log('2. If test fails, check your Razorpay dashboard');
  console.log('3. Ensure your Razorpay account is active');
  console.log('4. Verify you\'re using the correct environment (test vs live)');

  return true;
}

// Additional helper function to show expected format
function showExpectedFormat() {
  console.log('\n📖 Expected Credential Format:');
  console.log('RAZORPAY_KEY_ID=rzp_test_1234567890abcdef');
  console.log('RAZORPAY_KEY_SECRET=abcdef1234567890abcdef1234567890');
  console.log('RAZORPAY_WEBHOOK_SECRET=webhook_secret_1234567890abcdef');
  console.log('\nNote: Replace with your actual credentials from Razorpay Dashboard');
}

// Run validation
const isValid = validateCredentials();

if (!isValid) {
  showExpectedFormat();
  process.exit(1);
}

console.log('\n🚀 Ready to test Razorpay integration!');
