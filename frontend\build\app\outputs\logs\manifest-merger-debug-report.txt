-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:3:5-34:19
INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:razorpay_flutter] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-8:19
MERGED from [:razorpay_flutter] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-8:19
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:41:5-70:19
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:41:5-70:19
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71bf41b770dcd97b14bcc152c92ac6cc\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71bf41b770dcd97b14bcc152c92ac6cc\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29715046516099e8112e70e1edc6cfd\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29715046516099e8112e70e1edc6cfd\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c38d0c52db26d035b0a852871510ef3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c38d0c52db26d035b0a852871510ef3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2e653d1d5dc4e52e009f13b62651fa\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2e653d1d5dc4e52e009f13b62651fa\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\140cd5af1b0d89ff5b64fa80c7b435de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\140cd5af1b0d89ff5b64fa80c7b435de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:1:1-46:12
MERGED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:1:1-46:12
INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:flutter_image_compress_common] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_image_compress_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-35:12
MERGED from [:razorpay_flutter] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:fluttertoast] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ab742b591e9a998c1344f12423241e0\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:2:1-72:12
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:app_links] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\app_links\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:integration_test] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\integration_test\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:1-12:12
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ae67029696d4a58ab8e3a58b66d8c3f\transformed\jetified-play-services-wallet-18.1.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1910c4a3293cc639e2d0bd4d4b597dc\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b4b56d05ef6e8304a3f9e310780ae5\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e16d3bb41bcecabb76fd4173bd211006\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\461d1e6f7ef1490527bb879c1a19fdd4\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed744409f366dc630e9caef446df74f9\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71bf41b770dcd97b14bcc152c92ac6cc\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29715046516099e8112e70e1edc6cfd\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c38d0c52db26d035b0a852871510ef3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75d9c9becc8add1c40cc20695d1c23ac\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1850f0e83f835f00669c3f6bdf54876b\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71281ec1e3c4d56c2747130e2d749b7b\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d05388373eb97e747d83964dea730e\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb3562e8a02ff62507777d07e3be13da\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c5530c7aa87ba7c8f27b164add0588b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5bcf6a85dec63102baebd0854e2611e\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7606e8e2f2f273225170755513fb38dc\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\012bee14130c4081baa480acd77bc678\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9278e47d70ab8f0c61647c55fbcd2254\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b386f293d466ea65d6e89d9c1c82bb0\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f42c97ca94101bfdf0ee3785da3b8fca\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\011778aeb311839620223ffa0a5806f7\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c61a1148dd9027bc3344392a943f2247\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b484e35559b2270159ab560aced28f2c\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53812893cb0206ce937c3cd7ad4d5f54\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\115b9fb01a7c26ab180e1e50b20f731f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c74e14b1c43d27fa5767c314195f2d24\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e8519e1f478414a8659af5dd6c1e3ad\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17e6dae989b8c8112e0f5fc31b5e9d9e\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91331ccb5b3781f77f12394f8bde2ece\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3de78a143d34ef6d8706310619e65876\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4469588cab67c8ff85cb2e5a9f2ae674\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1061808cf055fcebfb9e1feb4d76831a\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ae35086ee3e8a8bdf635dbfe387f6a3\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d482636398f8e3d56f8653d9221bf91\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\262950aa36591a3f0441cc1d46f304d3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\729966db035e77546d8df80cbf3ce67e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fded382d86119696eb7e6a68ab14fc53\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b4dd88025db63dc25a6b69b236aef0f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.heifwriter:heifwriter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a91af9c4a7b16c8d5eecc15a5ec07705\transformed\heifwriter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5e94bf8c219b17bd0897c26cd8eed43\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\561ab86e3d4a222d8243c712e287ee35\transformed\rules-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc1a6c1a9f0c0ed3baffc7445ad91ea6\transformed\espresso-core-3.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\658a55f0aa312987d7b9d13af617716f\transformed\runner-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2e653d1d5dc4e52e009f13b62651fa\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5326ec2c8b16ea22073f35a63432923a\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26fc8f9cf15dada12cff40a1e11be254\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\140cd5af1b0d89ff5b64fa80c7b435de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b34d0c904a6b36bc29ee23ca453d7e77\transformed\monitor-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb16578ffb68e1cfe33268a961be02\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fe887b78be6c67e0b2f578b6a6f0b42\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2d1acadba087aa26d61702b11a9c5a8\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd7cebf0a4ad16d11dd184a3d6be2b1a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5164eab52f01f567ea8658b953376d0b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82bde2ff45b8352ccec935144a00a873\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20a311bc2eaad53ca8b3e48446c6681\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e84e53f46f18686f6926f112533d4b8d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6e34f60389d054428969595d922c007\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c63fcd471326ed47877a35da0536eb4\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d9e33277000b4b6297d4283ad55b146\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05455b684ba02a268afce69b32bae8b5\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:6:1-14:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d0e1751d79a1fc8325c8a218de1570e\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aac59d4fb2da372e54f8c09562ef8eb4\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:2:5-67
MERGED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:2:5-67
MERGED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:2:5-67
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:24:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:2:22-64
queries
ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:40:5-45:15
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:10:5-39:15
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:10:5-39:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:41:9-44:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:42:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:42:21-70
data
ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\main\AndroidManifest.xml:43:19-48
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_image_compress_common] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_image_compress_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:55
MERGED from [:flutter_image_compress_common] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_image_compress_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:55
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:razorpay_flutter] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:razorpay_flutter] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ab742b591e9a998c1344f12423241e0\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ab742b591e9a998c1344f12423241e0\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:app_links] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\app_links\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:app_links] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\app_links\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:integration_test] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\integration_test\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:integration_test] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\integration_test\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ae67029696d4a58ab8e3a58b66d8c3f\transformed\jetified-play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ae67029696d4a58ab8e3a58b66d8c3f\transformed\jetified-play-services-wallet-18.1.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1910c4a3293cc639e2d0bd4d4b597dc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1910c4a3293cc639e2d0bd4d4b597dc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b4b56d05ef6e8304a3f9e310780ae5\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b4b56d05ef6e8304a3f9e310780ae5\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e16d3bb41bcecabb76fd4173bd211006\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e16d3bb41bcecabb76fd4173bd211006\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\461d1e6f7ef1490527bb879c1a19fdd4\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\461d1e6f7ef1490527bb879c1a19fdd4\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed744409f366dc630e9caef446df74f9\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed744409f366dc630e9caef446df74f9\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71bf41b770dcd97b14bcc152c92ac6cc\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71bf41b770dcd97b14bcc152c92ac6cc\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29715046516099e8112e70e1edc6cfd\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29715046516099e8112e70e1edc6cfd\transformed\jetified-play-services-identity-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c38d0c52db26d035b0a852871510ef3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c38d0c52db26d035b0a852871510ef3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75d9c9becc8add1c40cc20695d1c23ac\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75d9c9becc8add1c40cc20695d1c23ac\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1850f0e83f835f00669c3f6bdf54876b\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1850f0e83f835f00669c3f6bdf54876b\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71281ec1e3c4d56c2747130e2d749b7b\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71281ec1e3c4d56c2747130e2d749b7b\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d05388373eb97e747d83964dea730e\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78d05388373eb97e747d83964dea730e\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb3562e8a02ff62507777d07e3be13da\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb3562e8a02ff62507777d07e3be13da\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c5530c7aa87ba7c8f27b164add0588b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c5530c7aa87ba7c8f27b164add0588b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5bcf6a85dec63102baebd0854e2611e\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5bcf6a85dec63102baebd0854e2611e\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7606e8e2f2f273225170755513fb38dc\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7606e8e2f2f273225170755513fb38dc\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\012bee14130c4081baa480acd77bc678\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\012bee14130c4081baa480acd77bc678\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9278e47d70ab8f0c61647c55fbcd2254\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9278e47d70ab8f0c61647c55fbcd2254\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b386f293d466ea65d6e89d9c1c82bb0\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b386f293d466ea65d6e89d9c1c82bb0\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f42c97ca94101bfdf0ee3785da3b8fca\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f42c97ca94101bfdf0ee3785da3b8fca\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\011778aeb311839620223ffa0a5806f7\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\011778aeb311839620223ffa0a5806f7\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c61a1148dd9027bc3344392a943f2247\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c61a1148dd9027bc3344392a943f2247\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b484e35559b2270159ab560aced28f2c\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b484e35559b2270159ab560aced28f2c\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53812893cb0206ce937c3cd7ad4d5f54\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53812893cb0206ce937c3cd7ad4d5f54\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\115b9fb01a7c26ab180e1e50b20f731f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\115b9fb01a7c26ab180e1e50b20f731f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c74e14b1c43d27fa5767c314195f2d24\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c74e14b1c43d27fa5767c314195f2d24\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e8519e1f478414a8659af5dd6c1e3ad\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e8519e1f478414a8659af5dd6c1e3ad\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17e6dae989b8c8112e0f5fc31b5e9d9e\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17e6dae989b8c8112e0f5fc31b5e9d9e\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91331ccb5b3781f77f12394f8bde2ece\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91331ccb5b3781f77f12394f8bde2ece\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3de78a143d34ef6d8706310619e65876\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3de78a143d34ef6d8706310619e65876\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4469588cab67c8ff85cb2e5a9f2ae674\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4469588cab67c8ff85cb2e5a9f2ae674\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1061808cf055fcebfb9e1feb4d76831a\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1061808cf055fcebfb9e1feb4d76831a\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ae35086ee3e8a8bdf635dbfe387f6a3\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ae35086ee3e8a8bdf635dbfe387f6a3\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d482636398f8e3d56f8653d9221bf91\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d482636398f8e3d56f8653d9221bf91\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\262950aa36591a3f0441cc1d46f304d3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\262950aa36591a3f0441cc1d46f304d3\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\729966db035e77546d8df80cbf3ce67e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\729966db035e77546d8df80cbf3ce67e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fded382d86119696eb7e6a68ab14fc53\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fded382d86119696eb7e6a68ab14fc53\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b4dd88025db63dc25a6b69b236aef0f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b4dd88025db63dc25a6b69b236aef0f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.heifwriter:heifwriter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a91af9c4a7b16c8d5eecc15a5ec07705\transformed\heifwriter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.heifwriter:heifwriter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a91af9c4a7b16c8d5eecc15a5ec07705\transformed\heifwriter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5e94bf8c219b17bd0897c26cd8eed43\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5e94bf8c219b17bd0897c26cd8eed43\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\561ab86e3d4a222d8243c712e287ee35\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\561ab86e3d4a222d8243c712e287ee35\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc1a6c1a9f0c0ed3baffc7445ad91ea6\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc1a6c1a9f0c0ed3baffc7445ad91ea6\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\658a55f0aa312987d7b9d13af617716f\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\658a55f0aa312987d7b9d13af617716f\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2e653d1d5dc4e52e009f13b62651fa\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2e653d1d5dc4e52e009f13b62651fa\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5326ec2c8b16ea22073f35a63432923a\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5326ec2c8b16ea22073f35a63432923a\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26fc8f9cf15dada12cff40a1e11be254\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26fc8f9cf15dada12cff40a1e11be254\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\140cd5af1b0d89ff5b64fa80c7b435de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\140cd5af1b0d89ff5b64fa80c7b435de\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b34d0c904a6b36bc29ee23ca453d7e77\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b34d0c904a6b36bc29ee23ca453d7e77\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb16578ffb68e1cfe33268a961be02\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb16578ffb68e1cfe33268a961be02\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fe887b78be6c67e0b2f578b6a6f0b42\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fe887b78be6c67e0b2f578b6a6f0b42\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2d1acadba087aa26d61702b11a9c5a8\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2d1acadba087aa26d61702b11a9c5a8\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd7cebf0a4ad16d11dd184a3d6be2b1a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd7cebf0a4ad16d11dd184a3d6be2b1a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5164eab52f01f567ea8658b953376d0b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5164eab52f01f567ea8658b953376d0b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82bde2ff45b8352ccec935144a00a873\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82bde2ff45b8352ccec935144a00a873\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20a311bc2eaad53ca8b3e48446c6681\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20a311bc2eaad53ca8b3e48446c6681\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e84e53f46f18686f6926f112533d4b8d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e84e53f46f18686f6926f112533d4b8d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6e34f60389d054428969595d922c007\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6e34f60389d054428969595d922c007\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c63fcd471326ed47877a35da0536eb4\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c63fcd471326ed47877a35da0536eb4\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d9e33277000b4b6297d4283ad55b146\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d9e33277000b4b6297d4283ad55b146\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05455b684ba02a268afce69b32bae8b5\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05455b684ba02a268afce69b32bae8b5\transformed\jetified-safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d0e1751d79a1fc8325c8a218de1570e\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d0e1751d79a1fc8325c8a218de1570e\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aac59d4fb2da372e54f8c09562ef8eb4\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aac59d4fb2da372e54f8c09562ef8eb4\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
	tools:overrideLibrary
		ADDED from [:flutter_image_compress_common] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\flutter_image_compress_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-52
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\android\app\src\debug\AndroidManifest.xml
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:11:9-17:18
action#android.intent.action.VIEW
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:12:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:12:21-62
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:18:9-27:18
category#android.intent.category.BROWSABLE
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:21:13-74
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:21:23-71
intent#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:28:9-30:18
action#android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:21-62
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:21-58
activity#com.razorpay.CheckoutActivity
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:42:9-50:20
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:45:13-37
	android:configChanges
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:44:13-83
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:46:13-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:43:13-57
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:47:13-49:29
provider#androidx.startup.InitializationProvider
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:52:9-60:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2e653d1d5dc4e52e009f13b62651fa\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2e653d1d5dc4e52e009f13b62651fa\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:56:13-31
	android:authorities
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:54:13-68
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:55:13-37
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:53:13-67
meta-data#com.razorpay.RazorpayInitializer
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:57:13-59:52
	android:value
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:59:17-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:58:17-64
activity#com.razorpay.MagicXActivity
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:62:9-65:75
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:64:13-37
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:65:13-72
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:63:13-55
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:67:9-69:58
	android:value
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:69:13-55
	android:name
		ADDED from [com.razorpay:standard-core:1.6.53] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c233b7c2cc391939d73a89297d579d4b\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:68:13-61
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:connectivity_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:23:5-79
	android:name
		ADDED from [:connectivity_plus] C:\Users\<USER>\Desktop\Node\wiggyz_backup\frontend\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58677a1d93d1ffa48fabf1da8209cd31\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20fc583024cbaf7bf40939221218f8bd\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb05b3e5082f8e06f15912b19b04136a\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f289db7a6d1207720a20b29ba51013\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abc6f6b6dedd29fe45a83aee8daa81c8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e5de7343c6205f9aa91c6c78c0ebb25\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.wiggyz_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.wiggyz_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe27d6300ddf570c01b16ce74371dc49\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e0c90e7c98755bfd63ac6b3e777b06d\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
