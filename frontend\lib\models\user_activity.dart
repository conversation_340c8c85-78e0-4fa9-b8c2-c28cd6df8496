/// Model for user activity data
class UserActivity {
  final String id;
  final String userId;
  final String activityType;
  final String title;
  final String? description;
  final double? amount;
  final String? relatedEntityId;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;

  const UserActivity({
    required this.id,
    required this.userId,
    required this.activityType,
    required this.title,
    this.description,
    this.amount,
    this.relatedEntityId,
    this.metadata,
    required this.createdAt,
  });

  /// Create UserActivity from JSON
  factory UserActivity.fromJson(Map<String, dynamic> json) {
    return UserActivity(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      activityType: json['activityType'] ?? '',
      title: json['title'] ?? '',
      description: json['description'],
      amount: json['amount']?.toDouble(),
      relatedEntityId: json['relatedEntityId'],
      metadata: json['metadata'] != null 
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// Convert UserActivity to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'activityType': activityType,
      'title': title,
      'description': description,
      'amount': amount,
      'relatedEntityId': relatedEntityId,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Create a copy with updated values
  UserActivity copyWith({
    String? id,
    String? userId,
    String? activityType,
    String? title,
    String? description,
    double? amount,
    String? relatedEntityId,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return UserActivity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      activityType: activityType ?? this.activityType,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Get formatted amount string
  String get formattedAmount {
    if (amount == null) return '';
    if (amount! >= 0) {
      return '+₹${amount!.toStringAsFixed(0)}';
    } else {
      return '-₹${amount!.abs().toStringAsFixed(0)}';
    }
  }

  /// Get activity type display name
  String get activityTypeDisplayName {
    switch (activityType) {
      case 'match_win':
        return 'Match Win';
      case 'match_loss':
        return 'Match Played';
      case 'tournament_join':
        return 'Tournament Joined';
      case 'tournament_win':
        return 'Tournament Win';
      case 'deposit':
        return 'Deposit';
      case 'withdrawal':
        return 'Withdrawal';
      case 'achievement_unlock':
        return 'Achievement';
      case 'reward_claim':
        return 'Reward';
      case 'level_up':
        return 'Level Up';
      case 'streak_milestone':
        return 'Streak Milestone';
      case 'referral_bonus':
        return 'Referral Bonus';
      default:
        return activityType.replaceAll('_', ' ').toUpperCase();
    }
  }

  /// Get activity icon based on type
  String get activityIcon {
    switch (activityType) {
      case 'match_win':
        return '🏆';
      case 'match_loss':
        return '🎮';
      case 'tournament_join':
        return '🎯';
      case 'tournament_win':
        return '👑';
      case 'deposit':
        return '💰';
      case 'withdrawal':
        return '💸';
      case 'achievement_unlock':
        return '🏅';
      case 'reward_claim':
        return '🎁';
      case 'level_up':
        return '⬆️';
      case 'streak_milestone':
        return '🔥';
      case 'referral_bonus':
        return '👥';
      default:
        return '📝';
    }
  }

  /// Check if activity is positive (gain)
  bool get isPositive {
    switch (activityType) {
      case 'match_win':
      case 'tournament_win':
      case 'deposit':
      case 'achievement_unlock':
      case 'reward_claim':
      case 'level_up':
      case 'streak_milestone':
      case 'referral_bonus':
        return true;
      case 'withdrawal':
        return false;
      default:
        return amount != null ? amount! >= 0 : true;
    }
  }

  /// Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  /// Get formatted date string
  String get formattedDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final activityDate = DateTime(createdAt.year, createdAt.month, createdAt.day);

    if (activityDate == today) {
      return 'Today';
    } else if (activityDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else if (now.difference(createdAt).inDays < 7) {
      const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return weekdays[createdAt.weekday - 1];
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  @override
  String toString() {
    return 'UserActivity(id: $id, type: $activityType, title: $title, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserActivity &&
           other.id == id &&
           other.userId == userId &&
           other.activityType == activityType &&
           other.title == title &&
           other.amount == amount;
  }

  @override
  int get hashCode {
    return Object.hash(id, userId, activityType, title, amount);
  }
}

/// Model for paginated activities response
class PaginatedActivities {
  final List<UserActivity> activities;
  final int totalCount;
  final bool hasMore;
  final int? nextOffset;

  const PaginatedActivities({
    required this.activities,
    required this.totalCount,
    required this.hasMore,
    this.nextOffset,
  });

  /// Create PaginatedActivities from JSON
  factory PaginatedActivities.fromJson(Map<String, dynamic> json) {
    final activitiesList = json['activities'] as List? ?? [];
    return PaginatedActivities(
      activities: activitiesList.map((item) => UserActivity.fromJson(item)).toList(),
      totalCount: json['totalCount'] ?? 0,
      hasMore: json['hasMore'] ?? false,
      nextOffset: json['nextOffset'],
    );
  }

  /// Convert PaginatedActivities to JSON
  Map<String, dynamic> toJson() {
    return {
      'activities': activities.map((activity) => activity.toJson()).toList(),
      'totalCount': totalCount,
      'hasMore': hasMore,
      'nextOffset': nextOffset,
    };
  }

  /// Create a copy with updated values
  PaginatedActivities copyWith({
    List<UserActivity>? activities,
    int? totalCount,
    bool? hasMore,
    int? nextOffset,
  }) {
    return PaginatedActivities(
      activities: activities ?? this.activities,
      totalCount: totalCount ?? this.totalCount,
      hasMore: hasMore ?? this.hasMore,
      nextOffset: nextOffset ?? this.nextOffset,
    );
  }

  /// Append more activities (for pagination)
  PaginatedActivities appendActivities(PaginatedActivities newPage) {
    return PaginatedActivities(
      activities: [...activities, ...newPage.activities],
      totalCount: newPage.totalCount,
      hasMore: newPage.hasMore,
      nextOffset: newPage.nextOffset,
    );
  }

  @override
  String toString() {
    return 'PaginatedActivities(count: ${activities.length}, total: $totalCount, hasMore: $hasMore)';
  }
}

/// Activity type constants
class ActivityTypes {
  static const String matchWin = 'match_win';
  static const String matchLoss = 'match_loss';
  static const String tournamentJoin = 'tournament_join';
  static const String tournamentWin = 'tournament_win';
  static const String deposit = 'deposit';
  static const String withdrawal = 'withdrawal';
  static const String achievementUnlock = 'achievement_unlock';
  static const String rewardClaim = 'reward_claim';
  static const String levelUp = 'level_up';
  static const String streakMilestone = 'streak_milestone';
  static const String referralBonus = 'referral_bonus';

  static const List<String> all = [
    matchWin,
    matchLoss,
    tournamentJoin,
    tournamentWin,
    deposit,
    withdrawal,
    achievementUnlock,
    rewardClaim,
    levelUp,
    streakMilestone,
    referralBonus,
  ];

  static const Map<String, String> displayNames = {
    matchWin: 'Match Wins',
    matchLoss: 'Matches Played',
    tournamentJoin: 'Tournaments Joined',
    tournamentWin: 'Tournament Wins',
    deposit: 'Deposits',
    withdrawal: 'Withdrawals',
    achievementUnlock: 'Achievements',
    rewardClaim: 'Rewards',
    levelUp: 'Level Ups',
    streakMilestone: 'Streak Milestones',
    referralBonus: 'Referral Bonuses',
  };
}
