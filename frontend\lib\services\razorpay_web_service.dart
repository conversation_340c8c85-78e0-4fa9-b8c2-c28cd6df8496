import 'dart:async';
import 'dart:js' as js;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:wiggyz_app/features/wallet/services/wallet_service.dart';

/// Web-compatible Razorpay service using JavaScript interop
/// This service provides Razorpay functionality for web platforms
class RazorpayWebService {
  static final RazorpayWebService _instance = RazorpayWebService._internal();
  factory RazorpayWebService() => _instance;
  RazorpayWebService._internal();

  final WalletService _walletService = WalletService();
  Completer<Map<String, dynamic>>? _paymentCompleter;

  /// Initialize the web service
  void initialize() {
    if (!kIsWeb) {
      print('RazorpayWebService should only be used on web platform');
      return;
    }

    // Set up JavaScript callbacks
    js.context['onRazorpayWebSuccess'] = (js.JsObject response) {
      _handlePaymentSuccess(response);
    };

    js.context['onRazorpayWebError'] = (js.JsObject response) {
      _handlePaymentError(response);
    };

    js.context['onRazorpayWebDismiss'] = () {
      _handlePaymentDismiss();
    };

    print('RazorpayWebService initialized for web platform');
  }

  /// Start payment using web SDK
  Future<Map<String, dynamic>> startWebPayment({
    required String orderId,
    required String keyId,
    required double amount,
    required String currency,
    required String name,
    required String description,
    required String userEmail,
    required String userPhone,
    required String userName,
  }) async {
    if (!kIsWeb) {
      throw Exception('Web payment service only available on web platform');
    }

    // Clean up any existing completer
    if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
      _paymentCompleter!.completeError(Exception('Payment cancelled - new payment started'));
    }

    _paymentCompleter = Completer<Map<String, dynamic>>();

    try {
      // Check if Razorpay web functions are available
      if (!js.context.hasProperty('razorpayWeb')) {
        throw Exception('Razorpay web SDK not loaded');
      }

      final options = js.JsObject.jsify({
        'key': keyId,
        'amount': (amount * 100).toInt(), // Amount in paise
        'currency': currency,
        'name': name,
        'description': description,
        'order_id': orderId,
        'prefill': {
          'name': userName,
          'email': userEmail,
          'contact': userPhone,
        },
        'theme': {
          'color': '#FFCC00', // WiggyZ brand color
        },
        'onSuccess': js.allowInterop((response) {
          js.context.callMethod('onRazorpayWebSuccess', [response]);
        }),
        'onError': js.allowInterop((response) {
          js.context.callMethod('onRazorpayWebError', [response]);
        }),
        'onDismiss': js.allowInterop(() {
          js.context.callMethod('onRazorpayWebDismiss');
        }),
      });

      // Start payment using the correct JavaScript function path
      print('Starting Razorpay web payment with order ID: $orderId');

      bool result = false;

      // Try using the razorpayWeb object first
      if (js.context.hasProperty('razorpayWeb')) {
        final razorpayWeb = js.context['razorpayWeb'];
        if (razorpayWeb != null) {
          try {
            result = razorpayWeb.callMethod('startPayment', [options]);
            print('Payment started via razorpayWeb.startPayment: $result');
          } catch (e) {
            print('Error calling razorpayWeb.startPayment: $e');
          }
        }
      }

      // Fallback to global function if the object method failed
      if (!result && js.context.hasProperty('startRazorpayWebPayment')) {
        try {
          result = js.context.callMethod('startRazorpayWebPayment', [options]);
          print('Payment started via global startRazorpayWebPayment: $result');
        } catch (e) {
          print('Error calling global startRazorpayWebPayment: $e');
        }
      }

      // The JavaScript function returns a boolean synchronously
      if (result != true) {
        throw Exception('Failed to initialize payment gateway - please check browser console for details');
      }

      print('Payment gateway opened successfully, waiting for user action...');
      return await _paymentCompleter!.future;
    } catch (e) {
      print('Error in startWebPayment: $e');
      if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
        _paymentCompleter!.completeError(e);
      }
      rethrow;
    }
  }

  /// Handle payment success
  void _handlePaymentSuccess(js.JsObject response) {
    try {
      if (_paymentCompleter == null || _paymentCompleter!.isCompleted) {
        print('Payment success received but completer is null or already completed');
        return;
      }

      final result = {
        'razorpay_payment_id': response['razorpay_payment_id'],
        'razorpay_order_id': response['razorpay_order_id'],
        'razorpay_signature': response['razorpay_signature'],
        'status': 'success',
      };

      print('Web payment success: $result');
      _paymentCompleter!.complete(result);
    } catch (e) {
      print('Error handling payment success: $e');
      if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
        _paymentCompleter!.completeError(e);
      }
    }
  }

  /// Handle payment error
  void _handlePaymentError(js.JsObject response) {
    try {
      if (_paymentCompleter == null || _paymentCompleter!.isCompleted) {
        print('Payment error received but completer is null or already completed');
        return;
      }

      final error = {
        'code': response['code'],
        'description': response['description'],
        'source': response['source'],
        'step': response['step'],
        'reason': response['reason'],
        'order_id': response['order_id'],
      };

      print('Web payment error: $error');
      _paymentCompleter!.completeError(
        Exception('Payment failed: ${error['description']}')
      );
    } catch (e) {
      print('Error handling payment error: $e');
      if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
        _paymentCompleter!.completeError(e);
      }
    }
  }

  /// Handle payment dismissal
  void _handlePaymentDismiss() {
    print('Web payment dismissed by user');

    if (_paymentCompleter == null || _paymentCompleter!.isCompleted) {
      print('Payment dismiss received but completer is null or already completed');
      return;
    }

    _paymentCompleter!.completeError(
      Exception('Payment was cancelled by user')
    );
  }

  /// Process complete wallet top-up flow for web
  Future<Map<String, dynamic>> processWebWalletTopUp({
    required BuildContext context,
    required double amount,
    required String currency,
    required String userEmail,
    required String userPhone,
    required String userName,
    String? transactionId, // Made optional - will be generated by backend
  }) async {
    try {
      // Step 1: Initiate top-up with backend to get Razorpay order
      final topUpData = {
        'amount': amount,
        'currency': currency,
        'payment_method': 'upi', // This will route to Razorpay
      };

      print('Initiating web wallet top-up: $topUpData');
      final topUpResponse = await _walletService.topUpWallet(topUpData);
      
      if (!topUpResponse.containsKey('data')) {
        throw Exception('Invalid response from server');
      }

      final data = topUpResponse['data'];
      final orderId = data['payment_token'] ?? data['order_id'];
      final keyId = data['key_id'] ?? 'rzp_test_iVqMylHzm5WcBc';
      final backendTransactionId = data['transaction_id']; // Get transaction ID from backend

      if (orderId == null) {
        throw Exception('No order ID received from server');
      }

      if (backendTransactionId == null) {
        throw Exception('No transaction ID received from server');
      }

      // Use the transaction ID from backend
      final actualTransactionId = backendTransactionId;

      print('Received order ID: $orderId, Key ID: $keyId, Transaction ID: $actualTransactionId');

      // Step 2: Start web payment
      final paymentResult = await startWebPayment(
        orderId: orderId,
        keyId: keyId,
        amount: amount,
        currency: currency,
        name: userName,
        description: 'Wallet Top-up',
        userEmail: userEmail,
        userPhone: userPhone,
        userName: userName,
      );

      print('Web payment completed: $paymentResult');

      // Step 3: Verify payment with backend
      final verificationResult = await _verifyWebPayment(
        razorpayOrderId: paymentResult['razorpay_order_id'],
        razorpayPaymentId: paymentResult['razorpay_payment_id'],
        razorpaySignature: paymentResult['razorpay_signature'],
        transactionId: actualTransactionId, // Use the transaction ID from backend
      );

      return verificationResult;
    } catch (e) {
      print('Error in web wallet top-up: $e');
      rethrow;
    }
  }

  /// Verify payment with backend
  Future<Map<String, dynamic>> _verifyWebPayment({
    required String razorpayOrderId,
    required String razorpayPaymentId,
    required String razorpaySignature,
    required String transactionId,
  }) async {
    try {
      final verificationData = {
        'razorpay_order_id': razorpayOrderId,
        'razorpay_payment_id': razorpayPaymentId,
        'razorpay_signature': razorpaySignature,
        'transaction_id': transactionId,
      };

      print('Verifying web payment: $verificationData');
      final result = await _walletService.verifyPayment(verificationData);
      
      return {
        'success': true,
        'transaction_id': transactionId,
        'payment_id': razorpayPaymentId,
        'order_id': razorpayOrderId,
        'verification_result': result,
      };
    } catch (e) {
      print('Error verifying web payment: $e');
      rethrow;
    }
  }

  /// Test payment functionality
  void testWebPayment() {
    if (!kIsWeb) {
      print('Test payment only available on web platform');
      return;
    }

    try {
      js.context.callMethod('razorpayWeb.testPayment');
    } catch (e) {
      print('Error testing web payment: $e');
    }
  }

  void dispose() {
    if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
      _paymentCompleter!.completeError(Exception('Service disposed'));
    }
    _paymentCompleter = null;
  }
}
