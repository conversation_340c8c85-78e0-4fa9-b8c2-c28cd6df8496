/**
 * Investigation script for WiggyZ wallet transaction recording issue
 * This script will help diagnose why transactions are not appearing in history
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './backend/.env' });

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

async function investigateTransactionIssue() {
  console.log('🔍 Investigating WiggyZ Wallet Transaction Recording Issue');
  console.log('=' * 60);

  try {
    // 1. Check wallet_transactions table structure
    await checkTableStructure();
    
    // 2. Check recent transactions
    await checkRecentTransactions();
    
    // 3. Check transaction counts by status
    await checkTransactionsByStatus();
    
    // 4. Check for any transactions with fee_breakdown metadata
    await checkFeeBreakdownTransactions();
    
    // 5. Check wallet table for recent activity
    await checkWalletActivity();
    
    // 6. Test transaction creation manually
    await testTransactionCreation();
    
  } catch (error) {
    console.error('❌ Investigation failed:', error);
  }
}

async function checkTableStructure() {
  console.log('\n📋 Checking wallet_transactions table structure...');
  
  try {
    // Get table columns
    const { data, error } = await supabase
      .from('wallet_transactions')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Error checking table structure:', error);
      return;
    }
    
    if (data && data.length > 0) {
      console.log('✅ Table exists with columns:', Object.keys(data[0]));
    } else {
      console.log('⚠️  Table exists but has no data');
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

async function checkRecentTransactions() {
  console.log('\n📊 Checking recent transactions...');
  
  try {
    const { data, error, count } = await supabase
      .from('wallet_transactions')
      .select('id, user_id, type, amount, status, created_at, metadata', { count: 'exact' })
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (error) {
      console.error('❌ Error fetching transactions:', error);
      return;
    }
    
    console.log(`📈 Total transactions in database: ${count}`);
    
    if (data && data.length > 0) {
      console.log('📋 Recent transactions:');
      data.forEach((tx, index) => {
        console.log(`  ${index + 1}. ID: ${tx.id}`);
        console.log(`     User: ${tx.user_id}`);
        console.log(`     Type: ${tx.type}`);
        console.log(`     Amount: ${tx.amount}`);
        console.log(`     Status: ${tx.status}`);
        console.log(`     Created: ${tx.created_at}`);
        console.log(`     Has Fee Breakdown: ${tx.metadata?.fee_breakdown ? 'Yes' : 'No'}`);
        console.log('');
      });
    } else {
      console.log('⚠️  No transactions found in database');
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

async function checkTransactionsByStatus() {
  console.log('\n📊 Checking transactions by status...');
  
  try {
    const statuses = ['pending', 'processing', 'completed', 'failed', 'cancelled'];
    
    for (const status of statuses) {
      const { count, error } = await supabase
        .from('wallet_transactions')
        .select('*', { count: 'exact', head: true })
        .eq('status', status);
      
      if (error) {
        console.error(`❌ Error checking ${status} transactions:`, error);
      } else {
        console.log(`  ${status.toUpperCase()}: ${count} transactions`);
      }
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

async function checkFeeBreakdownTransactions() {
  console.log('\n💰 Checking transactions with fee breakdown...');
  
  try {
    const { data, error } = await supabase
      .from('wallet_transactions')
      .select('id, user_id, type, amount, status, metadata, created_at')
      .not('metadata->fee_breakdown', 'is', null)
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (error) {
      console.error('❌ Error fetching fee breakdown transactions:', error);
      return;
    }
    
    if (data && data.length > 0) {
      console.log(`✅ Found ${data.length} transactions with fee breakdown:`);
      data.forEach((tx, index) => {
        console.log(`  ${index + 1}. ${tx.type} - ${tx.amount} (${tx.status})`);
        console.log(`     Fee breakdown:`, tx.metadata.fee_breakdown);
      });
    } else {
      console.log('⚠️  No transactions with fee breakdown found');
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

async function checkWalletActivity() {
  console.log('\n💳 Checking wallet activity...');
  
  try {
    const { data, error } = await supabase
      .from('wallets')
      .select('user_id, balance, currency, updated_at')
      .order('updated_at', { ascending: false })
      .limit(5);
    
    if (error) {
      console.error('❌ Error fetching wallet activity:', error);
      return;
    }
    
    if (data && data.length > 0) {
      console.log('📋 Recent wallet updates:');
      data.forEach((wallet, index) => {
        console.log(`  ${index + 1}. User: ${wallet.user_id}`);
        console.log(`     Balance: ${wallet.balance} ${wallet.currency}`);
        console.log(`     Updated: ${wallet.updated_at}`);
        console.log('');
      });
    } else {
      console.log('⚠️  No wallet activity found');
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

async function testTransactionCreation() {
  console.log('\n🧪 Testing transaction creation...');
  
  try {
    // Create a test transaction
    const testTransaction = {
      id: 'test-' + Date.now(),
      user_id: 'test-user-' + Date.now(),
      type: 'deposit',
      amount: 100.00,
      currency: 'INR',
      status: 'completed',
      payment_method: 'test',
      payment_gateway: 'test',
      metadata: {
        fee_breakdown: {
          gross_amount: 100.00,
          transaction_fee: 2.00,
          gst_on_fee: 0.36,
          total_deductions: 2.36,
          net_amount_credited: 97.64
        }
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    const { data, error } = await supabase
      .from('wallet_transactions')
      .insert(testTransaction)
      .select();
    
    if (error) {
      console.error('❌ Error creating test transaction:', error);
      console.log('Error details:', error.details);
      console.log('Error hint:', error.hint);
      console.log('Error message:', error.message);
    } else {
      console.log('✅ Test transaction created successfully:', data[0].id);
      
      // Clean up test transaction
      await supabase
        .from('wallet_transactions')
        .delete()
        .eq('id', testTransaction.id);
      
      console.log('🧹 Test transaction cleaned up');
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

async function checkTransactionHistoryAPI() {
  console.log('\n🌐 Testing transaction history API...');
  
  try {
    // This would require authentication, so we'll skip for now
    console.log('⚠️  API testing requires authentication - skipping for now');
    console.log('   To test API manually:');
    console.log('   GET /api/v1/wallet/transactions');
    console.log('   Headers: Authorization: Bearer <token>');
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the investigation
investigateTransactionIssue()
  .then(() => {
    console.log('\n✅ Investigation completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Investigation failed:', error);
    process.exit(1);
  });
