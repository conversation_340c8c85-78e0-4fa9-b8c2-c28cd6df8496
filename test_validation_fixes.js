#!/usr/bin/env node

/**
 * Test Validation Fixes
 * Tests the InputSanitizer validation fixes directly
 */

const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:5000/api/v1';

async function testValidationEndpoint() {
  console.log('🧪 Testing validation fixes...');
  
  // Test messages that should now pass after our validation fixes
  const testCases = [
    {
      name: 'Basic legitimate message',
      data: {
        subject: 'Need help with my account',
        category: 'general_inquiry',
        message: 'I am having trouble accessing my account. Can you please help me?'
      },
      shouldPass: true
    },
    {
      name: 'Message with tech terms (previously blocked)',
      data: {
        subject: 'Bot integration issue',
        category: 'technical_support',
        message: 'I am trying to integrate a bot with your API but getting errors. The spam detection seems to be blocking my requests.'
      },
      shouldPass: true
    },
    {
      name: 'Message with quotes and special chars',
      data: {
        subject: 'Payment & billing question',
        category: 'payment_issue',
        message: 'I have a question about my payment. My email is "<EMAIL>" and I need help with billing.'
      },
      shouldPass: true
    },
    {
      name: 'Message with URL (should still pass)',
      data: {
        subject: 'Website issue',
        category: 'technical_support',
        message: 'I am having trouble with the website at https://example.com. Can you help?'
      },
      shouldPass: true
    },
    {
      name: 'Empty message (should fail)',
      data: {
        subject: '',
        category: 'general_inquiry',
        message: ''
      },
      shouldPass: false
    },
    {
      name: 'Too short subject (should fail)',
      data: {
        subject: 'Hi',
        category: 'general_inquiry',
        message: 'This is a test message that should fail due to short subject.'
      },
      shouldPass: false
    }
  ];

  console.log('\n📋 Testing validation without authentication (expecting 401 but checking validation errors)...\n');

  let passedValidation = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    try {
      console.log(`🔍 Testing: ${testCase.name}`);
      
      const response = await axios.post(
        `${BASE_URL}/support/messages`,
        testCase.data,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      // If we get here, the request passed validation (but will fail auth)
      console.log(`   ✅ Passed validation (got ${response.status})`);
      if (testCase.shouldPass) passedValidation++;
      
    } catch (error) {
      const status = error.response?.status;
      const errorMsg = error.response?.data?.error || error.message;
      const details = error.response?.data?.details;

      if (status === 401 && errorMsg === 'Authentication required') {
        // This means validation passed, but auth failed (expected)
        console.log(`   ✅ Passed validation (auth required as expected)`);
        if (testCase.shouldPass) passedValidation++;
      } else if (status === 400) {
        // This means validation failed
        if (testCase.shouldPass) {
          console.log(`   ❌ Failed validation (unexpected): ${errorMsg}`);
          if (details) console.log(`      Details: ${JSON.stringify(details)}`);
        } else {
          console.log(`   ✅ Failed validation (expected): ${errorMsg}`);
          passedValidation++;
        }
      } else {
        console.log(`   ❓ Unexpected error (${status}): ${errorMsg}`);
      }
    }
  }

  console.log(`\n📊 Validation Test Results: ${passedValidation}/${totalTests} tests behaved as expected`);
  
  if (passedValidation === totalTests) {
    console.log('🎉 All validation tests passed! The InputSanitizer fixes are working correctly.');
  } else {
    console.log('⚠️  Some validation tests failed. The fixes may need adjustment.');
  }

  return passedValidation === totalTests;
}

async function testPublicEndpoints() {
  console.log('\n🌐 Testing public FAQ endpoints...');
  
  try {
    // Test FAQ categories
    const categoriesResponse = await axios.get(`${BASE_URL}/support/faq/categories`);
    console.log('✅ FAQ categories endpoint working');
    console.log(`   Found ${categoriesResponse.data.data.length} categories`);

    // Test FAQ items
    const itemsResponse = await axios.get(`${BASE_URL}/support/faq/items`);
    console.log('✅ FAQ items endpoint working');
    console.log(`   Found ${itemsResponse.data.data.length} items`);

    return true;
  } catch (error) {
    console.log('❌ Public endpoints test failed:', error.message);
    return false;
  }
}

async function runValidationTests() {
  console.log('🚀 Starting Validation Fixes Test\n');
  
  // Test public endpoints first
  const publicEndpointsWork = await testPublicEndpoints();
  
  // Test validation fixes
  const validationFixesWork = await testValidationEndpoint();
  
  console.log('\n📊 Overall Test Summary:');
  console.log('========================');
  console.log('✅ Public Endpoints:', publicEndpointsWork ? 'PASSED' : 'FAILED');
  console.log('✅ Validation Fixes:', validationFixesWork ? 'PASSED' : 'FAILED');
  
  if (publicEndpointsWork && validationFixesWork) {
    console.log('\n🎉 All tests passed! The backend validation fixes are working correctly.');
    console.log('\n📝 Key improvements made:');
    console.log('   • Reduced spam detection sensitivity');
    console.log('   • Removed tech terms from profanity filter');
    console.log('   • More lenient content modification checks');
    console.log('   • Improved SQL injection and XSS pattern detection');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the fixes.');
  }
}

// Run the tests
runValidationTests().catch(console.error);
