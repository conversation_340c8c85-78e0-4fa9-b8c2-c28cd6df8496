import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../core/api/api_config.dart';
import '../models/referral_models.dart';
import 'auth_service.dart';

/// Service class for all referral-related API operations
class ReferralService {
  final AuthService _authService;

  // Base URL for API endpoints
  final String _apiBaseUrl = kReleaseMode
      ? 'https://api.wiggyz.com/api/v1' // Production URL
      : ApiConfig.baseUrl; // Development URL

  ReferralService(this._authService);

  /// Get common headers for API requests
  Future<Map<String, String>> get _headers async {
    final token = await _authService.getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// Get user's referral information including referral code
  Future<ReferralInfo?> getUserReferralInfo() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$_apiBaseUrl${ApiConfig.getUserReferralInfoEndpoint}'),
        headers: headers,
      ).timeout(ApiConfig.connectionTimeout);

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        
        // Handle both direct data and nested data structure
        final data = responseData['data'] ?? responseData;
        
        if (data != null) {
          return ReferralInfo.fromJson(data as Map<String, dynamic>);
        }
        return null;
      } else if (response.statusCode == 401) {
        throw Exception('Authentication required. Please log in again.');
      } else if (response.statusCode == 404) {
        // User doesn't have a referral code yet, this is normal for new users
        return null;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to fetch referral information');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching referral info: $e');
      }
      rethrow;
    }
  }

  /// Get list of users referred by the current user
  Future<List<ReferredUser>> getReferredUsers() async {
    try {
      final headers = await _headers;
      final response = await http.get(
        Uri.parse('$_apiBaseUrl${ApiConfig.getReferredUsersEndpoint}'),
        headers: headers,
      ).timeout(ApiConfig.connectionTimeout);

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        
        // Handle both direct data and nested data structure
        final data = responseData['data'] ?? responseData;
        
        if (data is List) {
          return data.map((item) => ReferredUser.fromJson(item as Map<String, dynamic>)).toList();
        }
        return [];
      } else if (response.statusCode == 401) {
        throw Exception('Authentication required. Please log in again.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to fetch referred users');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching referred users: $e');
      }
      rethrow;
    }
  }

  /// Process a referral when a new user signs up with a referral code
  Future<Map<String, dynamic>> processReferral({
    required String referralCode,
    Map<String, dynamic>? deviceData,
    String? ipAddress,
    Map<String, dynamic>? geolocation,
  }) async {
    try {
      final headers = await _headers;
      final requestBody = {
        'referralCode': referralCode,
        if (deviceData != null) 'deviceData': deviceData,
        if (ipAddress != null) 'ipAddress': ipAddress,
        if (geolocation != null) 'geolocation': geolocation,
      };

      final response = await http.post(
        Uri.parse('$_apiBaseUrl${ApiConfig.processReferralEndpoint}'),
        headers: headers,
        body: json.encode(requestBody),
      ).timeout(ApiConfig.connectionTimeout);

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return responseData;
      } else {
        throw Exception(responseData['message'] ?? 'Failed to process referral');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error processing referral: $e');
      }
      rethrow;
    }
  }

  /// Claim referral reward for a specific referred user
  Future<Map<String, dynamic>> claimReferralReward({
    required String referredUserId,
    String? idempotencyKey,
  }) async {
    try {
      final headers = await _headers;
      
      // Add idempotency key to headers if provided
      if (idempotencyKey != null) {
        headers['x-idempotency-key'] = idempotencyKey;
      }

      final requestBody = {
        'referredUserId': referredUserId,
      };

      final response = await http.post(
        Uri.parse('$_apiBaseUrl${ApiConfig.claimReferralRewardEndpoint}'),
        headers: headers,
        body: json.encode(requestBody),
      ).timeout(ApiConfig.connectionTimeout);

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return responseData;
      } else {
        throw Exception(responseData['message'] ?? 'Failed to claim referral reward');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error claiming referral reward: $e');
      }
      rethrow;
    }
  }

  /// Get or create referral code for the current user
  /// This method ensures the user has a referral code and returns it
  Future<String> getOrCreateReferralCode() async {
    try {
      // First try to get existing referral info
      final referralInfo = await getUserReferralInfo();
      
      if (referralInfo != null) {
        return referralInfo.referralCode;
      }

      // If no referral info exists, the backend will create one when we call the endpoint
      // Let's try calling the endpoint again, as the backend creates a referral code automatically
      final newReferralInfo = await getUserReferralInfo();
      
      if (newReferralInfo != null) {
        return newReferralInfo.referralCode;
      }

      throw Exception('Unable to create referral code. Please try again later.');
    } catch (e) {
      if (kDebugMode) {
        print('Error getting or creating referral code: $e');
      }
      rethrow;
    }
  }

  /// Create referral share content with the user's referral code
  Future<ReferralShareContent> createShareContent({String? customMessage}) async {
    try {
      final referralCode = await getOrCreateReferralCode();
      return ReferralShareContent.create(
        referralCode: referralCode,
        customMessage: customMessage,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error creating share content: $e');
      }
      rethrow;
    }
  }

  /// Validate referral code format
  static bool isValidReferralCode(String code) {
    // Basic validation - adjust based on your referral code format
    if (code.isEmpty || code.length < 4) return false;
    
    // Check if it contains only alphanumeric characters
    final regex = RegExp(r'^[A-Z0-9]+$');
    return regex.hasMatch(code.toUpperCase());
  }

  /// Extract referral code from a referral URL
  static String? extractReferralCodeFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      
      // Check if it's a referral URL pattern like: https://wiggyz.com/refer/REFERRALCODE
      if (uri.pathSegments.length >= 2 && uri.pathSegments[0] == 'refer') {
        final code = uri.pathSegments[1];
        return isValidReferralCode(code) ? code : null;
      }
      
      // Check query parameters for referral code
      final referralCode = uri.queryParameters['ref'] ?? uri.queryParameters['referral'];
      return referralCode != null && isValidReferralCode(referralCode) ? referralCode : null;
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting referral code from URL: $e');
      }
      return null;
    }
  }
}
