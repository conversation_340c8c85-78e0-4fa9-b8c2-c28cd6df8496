/**
 * Referral Fraud Detection Service
 * Comprehensive fraud detection for referral system including device fingerprinting,
 * IP tracking, verification requirements, and abuse pattern detection
 */

import { supabase } from '../../../config/supabase';
import { logger } from '../../../utils/logger';
import crypto from 'crypto';

export interface FraudDetectionResult {
  fraudScore: number;
  riskFactors: string[];
  recommendedAction: 'approve' | 'review' | 'reject';
  requiresAdminReview: boolean;
  fraudDetectionId: string;
}

export interface DeviceFingerprint {
  userAgent: string;
  screenResolution: string;
  timezone: string;
  language: string;
  platform: string;
  cookiesEnabled: boolean;
  doNotTrack: boolean;
  plugins: string[];
  canvas?: string;
  webgl?: string;
}

export interface GeolocationData {
  country?: string;
  region?: string;
  city?: string;
  latitude?: number;
  longitude?: number;
  isp?: string;
  vpnDetected?: boolean;
}

export interface VerificationStatus {
  isEligible: boolean;
  phoneVerified: boolean;
  emailVerified: boolean;
  activityScore: number;
  daysSinceRegistration: number;
  missingRequirements: string[];
}

class ReferralFraudService {
  private readonly FRAUD_THRESHOLDS = {
    AUTO_REJECT: 0.8,
    ADMIN_REVIEW: 0.5,
    AUTO_APPROVE: 0.3
  };

  /**
   * Generate device fingerprint hash from device data
   */
  generateDeviceFingerprint(deviceData: DeviceFingerprint): string {
    const fingerprintString = [
      deviceData.userAgent,
      deviceData.screenResolution,
      deviceData.timezone,
      deviceData.language,
      deviceData.platform,
      deviceData.cookiesEnabled.toString(),
      deviceData.doNotTrack.toString(),
      deviceData.plugins.join(','),
      deviceData.canvas || '',
      deviceData.webgl || ''
    ].join('|');

    return crypto.createHash('sha256').update(fingerprintString).digest('hex');
  }

  /**
   * Perform comprehensive fraud detection on a referral
   */
  async detectReferralFraud(
    referrerId: string,
    referredId: string,
    deviceData: DeviceFingerprint,
    ipAddress: string,
    geolocation?: GeolocationData
  ): Promise<FraudDetectionResult> {
    try {
      const deviceFingerprint = this.generateDeviceFingerprint(deviceData);
      
      // Calculate fraud score using database function
      const { data: fraudScore, error: scoreError } = await supabase
        .rpc('calculate_referral_fraud_score', {
          p_device_fingerprint: deviceFingerprint,
          p_ip_address: ipAddress,
          p_referrer_id: referrerId,
          p_referred_id: referredId
        });

      if (scoreError) throw scoreError;

      // Update tracking tables
      await this.updateTrackingData(deviceFingerprint, ipAddress, deviceData, geolocation);

      // Identify specific risk factors
      const riskFactors = await this.identifyRiskFactors(
        referrerId,
        referredId,
        deviceFingerprint,
        ipAddress,
        geolocation
      );

      // Determine recommended action
      const recommendedAction = this.determineAction(fraudScore, riskFactors);
      const requiresAdminReview = fraudScore >= this.FRAUD_THRESHOLDS.ADMIN_REVIEW;

      // Create fraud detection record
      const { data: fraudDetection, error: createError } = await supabase
        .from('referral_fraud_detection')
        .insert({
          device_fingerprint: deviceFingerprint,
          ip_address: ipAddress,
          user_agent: deviceData.userAgent,
          geolocation: geolocation || {},
          fraud_score: fraudScore,
          risk_factors: riskFactors,
          status: recommendedAction === 'approve' ? 'approved' : 'pending',
          admin_reviewed: false
        })
        .select('id')
        .single();

      if (createError) throw createError;

      return {
        fraudScore,
        riskFactors,
        recommendedAction,
        requiresAdminReview,
        fraudDetectionId: fraudDetection.id
      };
    } catch (error) {
      logger.error(`Error in referral fraud detection: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Check user verification status and eligibility
   */
  async checkVerificationStatus(userId: string): Promise<VerificationStatus> {
    try {
      const { data, error } = await supabase
        .rpc('check_referral_eligibility', { p_user_id: userId });

      if (error) throw error;

      if (!data || data.length === 0) {
        return {
          isEligible: false,
          phoneVerified: false,
          emailVerified: false,
          activityScore: 0,
          daysSinceRegistration: 0,
          missingRequirements: ['user_not_found']
        };
      }

      const result = data[0];
      return {
        isEligible: result.is_eligible,
        phoneVerified: result.phone_verified,
        emailVerified: result.email_verified,
        activityScore: result.activity_score,
        daysSinceRegistration: result.days_since_registration,
        missingRequirements: result.missing_requirements || []
      };
    } catch (error) {
      logger.error(`Error checking verification status for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Update device and IP tracking data
   */
  private async updateTrackingData(
    deviceFingerprint: string,
    ipAddress: string,
    deviceData: DeviceFingerprint,
    geolocation?: GeolocationData
  ): Promise<void> {
    try {
      // Update device fingerprint tracking
      await supabase.rpc('update_device_fingerprint_tracking', {
        p_fingerprint_hash: deviceFingerprint,
        p_metadata: {
          userAgent: deviceData.userAgent,
          platform: deviceData.platform,
          language: deviceData.language,
          lastSeen: new Date().toISOString()
        }
      });

      // Update IP tracking
      await supabase.rpc('update_referral_ip_tracking', {
        p_ip_address: ipAddress,
        p_metadata: {
          geolocation: geolocation || {},
          lastSeen: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error(`Error updating tracking data: ${error instanceof Error ? error.message : String(error)}`);
      // Don't throw here as this is supplementary data
    }
  }

  /**
   * Identify specific risk factors
   */
  private async identifyRiskFactors(
    referrerId: string,
    referredId: string,
    deviceFingerprint: string,
    ipAddress: string,
    geolocation?: GeolocationData
  ): Promise<string[]> {
    const riskFactors: string[] = [];

    try {
      // Check device fingerprint usage
      const { data: deviceData } = await supabase
        .from('device_fingerprints')
        .select('user_count, is_suspicious')
        .eq('fingerprint_hash', deviceFingerprint)
        .single();

      if (deviceData?.user_count > 3) {
        riskFactors.push('device_multiple_users');
      }
      if (deviceData?.is_suspicious) {
        riskFactors.push('device_flagged_suspicious');
      }

      // Check IP address usage
      const { data: ipData } = await supabase
        .from('referral_ip_tracking')
        .select('referral_count, risk_score, is_blocked')
        .eq('ip_address', ipAddress)
        .single();

      if (ipData?.referral_count > 5) {
        riskFactors.push('ip_high_referral_count');
      }
      if (ipData?.risk_score > 0.7) {
        riskFactors.push('ip_high_risk_score');
      }
      if (ipData?.is_blocked) {
        riskFactors.push('ip_blocked');
      }

      // Check for VPN/Proxy usage
      if (geolocation?.vpnDetected) {
        riskFactors.push('vpn_detected');
      }

      // Check referrer's recent activity
      const { data: recentReferrals } = await supabase
        .from('user_referrals')
        .select('id')
        .eq('referrer_id', referrerId)
        .gte('signup_date', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

      if (recentReferrals && recentReferrals.length > 3) {
        riskFactors.push('referrer_high_frequency');
      }

      // Check for circular referrals
      const { data: circularCheck } = await supabase
        .from('user_referrals')
        .select('id')
        .or(`and(referrer_id.eq.${referredId},referred_id.eq.${referrerId}),and(referrer_id.eq.${referrerId},referred_id.eq.${referredId})`);

      if (circularCheck && circularCheck.length > 0) {
        riskFactors.push('circular_referral_detected');
      }

      // Check for same device usage between users
      const { data: sameDeviceUsers } = await supabase
        .from('user_referrals')
        .select('referrer_id, referred_id')
        .eq('device_fingerprint', deviceFingerprint)
        .neq('referrer_id', referrerId)
        .neq('referred_id', referredId);

      if (sameDeviceUsers && sameDeviceUsers.length > 0) {
        riskFactors.push('device_shared_with_other_referrals');
      }

    } catch (error) {
      logger.error(`Error identifying risk factors: ${error instanceof Error ? error.message : String(error)}`);
      riskFactors.push('risk_analysis_error');
    }

    return riskFactors;
  }

  /**
   * Determine recommended action based on fraud score and risk factors
   */
  private determineAction(fraudScore: number, riskFactors: string[]): 'approve' | 'review' | 'reject' {
    // Auto-reject for very high fraud scores
    if (fraudScore >= this.FRAUD_THRESHOLDS.AUTO_REJECT) {
      return 'reject';
    }

    // Critical risk factors that require rejection
    const criticalFactors = ['ip_blocked', 'circular_referral_detected', 'device_flagged_suspicious'];
    if (riskFactors.some(factor => criticalFactors.includes(factor))) {
      return 'reject';
    }

    // Require review for medium-high fraud scores
    if (fraudScore >= this.FRAUD_THRESHOLDS.ADMIN_REVIEW) {
      return 'review';
    }

    // Review for multiple risk factors even with lower scores
    const moderateFactors = ['device_multiple_users', 'ip_high_referral_count', 'vpn_detected'];
    const moderateFactorCount = riskFactors.filter(factor => moderateFactors.includes(factor)).length;
    if (moderateFactorCount >= 2) {
      return 'review';
    }

    // Auto-approve for low fraud scores
    if (fraudScore <= this.FRAUD_THRESHOLDS.AUTO_APPROVE) {
      return 'approve';
    }

    // Default to review for edge cases
    return 'review';
  }

  /**
   * Get fraud detection analytics for admin dashboard
   */
  async getFraudAnalytics(startDate?: Date, endDate?: Date): Promise<{
    totalReferrals: number;
    flaggedReferrals: number;
    approvedReferrals: number;
    rejectedReferrals: number;
    pendingReview: number;
    avgFraudScore: number;
    highRiskIps: number;
    suspiciousDevices: number;
  }> {
    try {
      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const end = endDate || new Date();

      const { data, error } = await supabase
        .rpc('get_referral_fraud_analytics', {
          p_start_date: start.toISOString(),
          p_end_date: end.toISOString()
        });

      if (error) throw error;

      return data[0] || {
        totalReferrals: 0,
        flaggedReferrals: 0,
        approvedReferrals: 0,
        rejectedReferrals: 0,
        pendingReview: 0,
        avgFraudScore: 0,
        highRiskIps: 0,
        suspiciousDevices: 0
      };
    } catch (error) {
      logger.error(`Error getting fraud analytics: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get flagged referrals requiring admin review
   */
  async getFlaggedReferrals(limit: number = 50): Promise<Array<{
    id: string;
    referrerId: string;
    referredId: string;
    fraudScore: number;
    riskFactors: string[];
    status: string;
    createdAt: string;
    referrerName?: string;
    referredName?: string;
  }>> {
    try {
      const { data, error } = await supabase
        .from('referral_fraud_detection')
        .select(`
          id,
          fraud_score,
          risk_factors,
          status,
          created_at,
          user_referrals!inner (
            referrer_id,
            referred_id,
            referrer:referrer_id (name),
            referred:referred_id (name)
          )
        `)
        .in('status', ['pending', 'flagged'])
        .gte('fraud_score', this.FRAUD_THRESHOLDS.ADMIN_REVIEW)
        .order('fraud_score', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data.map((item: any) => ({
        id: item.id,
        referrerId: item.user_referrals.referrer_id,
        referredId: item.user_referrals.referred_id,
        fraudScore: item.fraud_score,
        riskFactors: item.risk_factors || [],
        status: item.status,
        createdAt: item.created_at,
        referrerName: item.user_referrals.referrer?.name,
        referredName: item.user_referrals.referred?.name
      }));
    } catch (error) {
      logger.error(`Error getting flagged referrals: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Admin function to manually review and approve/reject a referral
   */
  async adminReviewReferral(
    referralId: string,
    adminId: string,
    action: 'approve' | 'reject',
    notes?: string
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('admin_review_referral', {
          p_referral_id: referralId,
          p_admin_id: adminId,
          p_action: action,
          p_notes: notes
        });

      if (error) throw error;

      logger.info(`Admin ${adminId} ${action}d referral ${referralId}: ${notes || 'No notes'}`);
      return data;
    } catch (error) {
      logger.error(`Error in admin review of referral ${referralId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}

export default new ReferralFraudService();
