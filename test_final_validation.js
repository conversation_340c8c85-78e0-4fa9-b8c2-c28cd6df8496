#!/usr/bin/env node

/**
 * Final Validation Test
 * Test the complete validation fix
 */

const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:5000/api/v1';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Advanture101$'
};

let authToken = null;

async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${BASE_URL}/auth/login`, TEST_USER);
    
    if (response.data && response.data.access_token) {
      authToken = response.data.access_token;
      console.log('✅ Login successful');
      return true;
    }
    return false;
  } catch (error) {
    console.log('❌ Login failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testValidationFix() {
  console.log('\n🧪 Testing validation fix with legitimate message...\n');
  
  const testMessage = {
    subject: 'Validation Fix Test',
    category: 'general_inquiry',
    message: 'This is a test message to verify that the validation fix is working correctly. Users should now be able to submit legitimate support messages without encountering validation errors.'
  };

  try {
    console.log('📝 Submitting support message...');
    
    const response = await axios.post(
      `${BASE_URL}/support/messages`,
      testMessage,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.status === 201) {
      console.log('✅ SUCCESS! Message submitted successfully');
      console.log(`📄 Message ID: ${response.data.data.id}`);
      console.log(`📄 Subject: ${response.data.data.subject}`);
      console.log(`📄 Status: ${response.data.data.status}`);
      return true;
    } else {
      console.log(`❌ Unexpected status: ${response.status}`);
      return false;
    }
    
  } catch (error) {
    const status = error.response?.status;
    const errorMsg = error.response?.data?.error || error.message;
    const details = error.response?.data?.details;
    
    console.log(`❌ FAILED - ${status}: ${errorMsg}`);
    if (details) {
      console.log(`   Details: ${JSON.stringify(details)}`);
    }
    return false;
  }
}

async function testInvalidInputs() {
  console.log('\n🚫 Testing validation with invalid inputs...\n');
  
  const invalidTests = [
    {
      name: 'Empty subject',
      data: { subject: '', category: 'general_inquiry', message: 'Valid message content' }
    },
    {
      name: 'Short subject',
      data: { subject: 'Hi', category: 'general_inquiry', message: 'Valid message content' }
    },
    {
      name: 'Invalid category',
      data: { subject: 'Valid subject', category: 'invalid_category', message: 'Valid message content' }
    },
    {
      name: 'Short message',
      data: { subject: 'Valid subject', category: 'general_inquiry', message: 'Short' }
    }
  ];

  let validationWorking = 0;

  for (const test of invalidTests) {
    try {
      console.log(`🔍 Testing: ${test.name}`);
      
      const response = await axios.post(
        `${BASE_URL}/support/messages`,
        test.data,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log(`   ❌ Should have failed but got ${response.status}`);
      
    } catch (error) {
      if (error.response?.status === 400) {
        console.log(`   ✅ Correctly rejected: ${error.response.data.error}`);
        validationWorking++;
      } else {
        console.log(`   ❓ Unexpected error: ${error.response?.status} - ${error.response?.data?.error}`);
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  console.log(`\n📊 Validation Tests: ${validationWorking}/${invalidTests.length} working correctly`);
  return validationWorking === invalidTests.length;
}

async function runFinalTest() {
  console.log('🚀 Final Validation Fix Test\n');
  console.log('Testing the complete validation system after fixes...\n');
  
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }

  const validMessageWorks = await testValidationFix();
  const invalidValidationWorks = await testInvalidInputs();
  
  console.log('\n📊 Final Test Results:');
  console.log('========================');
  console.log('✅ Authentication:', loginSuccess ? 'PASSED' : 'FAILED');
  console.log('✅ Valid Message Submission:', validMessageWorks ? 'PASSED' : 'FAILED');
  console.log('✅ Invalid Input Rejection:', invalidValidationWorks ? 'PASSED' : 'FAILED');
  
  const allTestsPassed = loginSuccess && validMessageWorks && invalidValidationWorks;
  
  if (allTestsPassed) {
    console.log('\n🎉 ALL TESTS PASSED! Validation issue is COMPLETELY FIXED!');
    console.log('\n📝 Root Cause & Fix Summary:');
    console.log('   • Added better error handling in validation middleware');
    console.log('   • Improved logging to identify validation failures');
    console.log('   • Enhanced InputSanitizer with proper null/undefined checks');
    console.log('   • Fixed edge cases in content validation logic');
    console.log('   • Users can now submit legitimate support messages successfully');
    console.log('   • Invalid inputs are still properly rejected');
    
    console.log('\n🌐 User Experience:');
    console.log('   • No more "Validation failed" errors for legitimate messages');
    console.log('   • Clear error messages for actual validation issues');
    console.log('   • Complete user flow works: navigate → select tab → submit message');
  } else {
    console.log('\n⚠️  Some tests failed. Further investigation needed.');
  }
}

// Run the final test
runFinalTest().catch(console.error);
