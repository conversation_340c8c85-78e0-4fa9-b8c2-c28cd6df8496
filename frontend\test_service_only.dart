import 'dart:convert';
import 'package:http/http.dart' as http;

/// Simple test to check the service layer without Flutter dependencies
void main() async {
  print('🧪 Testing Service Layer...');
  
  // Test configuration
  const apiBaseUrl = 'http://127.0.0.1:5000/api/v1';
  const claimEndpoint = '$apiBaseUrl/rewards/claim-daily';
  
  print('🌐 API Base URL: $apiBaseUrl');
  print('🎯 Claim Endpoint: $claimEndpoint');
  
  // Test with a mock auth token (this will fail auth but should reach the backend)
  print('\n📡 Testing claim endpoint with mock auth...');
  try {
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer mock-token-for-testing',
      'X-Idempotency-Key': 'test-${DateTime.now().millisecondsSinceEpoch}',
    };
    
    print('🔑 Headers: $headers');
    print('📡 Making POST request...');
    
    final response = await http.post(
      Uri.parse(claimEndpoint),
      headers: headers,
    );
    
    print('✅ Request completed!');
    print('📊 Status Code: ${response.statusCode}');
    print('📄 Response Body: ${response.body}');
    print('📋 Response Headers: ${response.headers}');
    
    if (response.statusCode == 401) {
      print('🔐 Expected 401 - Authentication failed (endpoint working)');
    } else if (response.statusCode == 403) {
      print('🔐 403 - Forbidden (endpoint working, auth issue)');
    } else if (response.statusCode == 404) {
      print('❌ 404 - Endpoint not found (route issue)');
    } else {
      print('ℹ️ Unexpected status code: ${response.statusCode}');
    }
    
  } catch (e) {
    print('❌ Network error: $e');
    print('🔍 This suggests a network connectivity issue');
  }
  
  print('\n🏁 Service test completed!');
}
