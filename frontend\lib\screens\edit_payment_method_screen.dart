import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:wiggyz_app/widgets/shared/golden_app_bar.dart';
import 'package:wiggyz_app/screens/payment_settings_screen.dart';

class EditPaymentMethodScreen extends StatefulWidget {
  final PaymentMethod paymentMethod;
  final Function(PaymentMethod) onSave;

  const EditPaymentMethodScreen({
    super.key,
    required this.paymentMethod,
    required this.onSave,
  });

  @override
  State<EditPaymentMethodScreen> createState() =>
      _EditPaymentMethodScreenState();
}

class _EditPaymentMethodScreenState extends State<EditPaymentMethodScreen> {
  final _formKey = GlobalKey<FormState>();
  late List<TextEditingController> _controllers;
  late List<String> _fieldLabels;
  late List<String> _fieldHints;
  late List<TextInputType> _keyboardTypes;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _initializeFields() {
    _controllers = [];
    _fieldLabels = [];
    _fieldHints = [];
    _keyboardTypes = [];

    switch (widget.paymentMethod.type) {
      case PaymentType.upi:
        _fieldLabels.addAll(['UPI ID', 'Name']);
        _fieldHints.addAll(['username@bank', 'Your full name']);
        _keyboardTypes.addAll([TextInputType.text, TextInputType.name]);

        // Pre-populate with existing data
        String existingUpiId = _extractUpiId(widget.paymentMethod.name);
        _controllers.add(TextEditingController(text: existingUpiId));
        _controllers.add(
          TextEditingController(text: 'User Name'),
        ); // Default name
        break;

      case PaymentType.creditCard:
      case PaymentType.debitCard:
        _fieldLabels.addAll([
          'Card Number',
          'Cardholder Name',
          'Expiry Date',
          'CVV',
        ]);
        _fieldHints.addAll([
          'XXXX XXXX XXXX XXXX',
          'Name on card',
          'MM/YY',
          'XXX',
        ]);
        _keyboardTypes.addAll([
          TextInputType.number,
          TextInputType.name,
          TextInputType.datetime,
          TextInputType.number,
        ]);

        // Pre-populate with masked data (for security, only show last 4 digits)
        String lastFour = _extractLastFour(widget.paymentMethod.name);
        _controllers.add(
          TextEditingController(text: '****-****-****-$lastFour'),
        );
        _controllers.add(TextEditingController(text: 'Cardholder Name'));
        _controllers.add(TextEditingController(text: '12/25'));
        _controllers.add(TextEditingController(text: '***'));
        break;

      case PaymentType.bankAccount:
        _fieldLabels.addAll([
          'Account Number',
          'IFSC Code',
          'Account Holder Name',
          'Bank Name',
        ]);
        _fieldHints.addAll([
          'Your bank account number',
          'Bank IFSC code',
          'Name as per bank records',
          'Your bank name',
        ]);
        _keyboardTypes.addAll([
          TextInputType.number,
          TextInputType.text,
          TextInputType.name,
          TextInputType.text,
        ]);

        // Pre-populate with existing data
        String lastFour = _extractLastFour(widget.paymentMethod.name);
        String bankName = _extractBankName(widget.paymentMethod.name);
        _controllers.add(TextEditingController(text: '****-****-$lastFour'));
        _controllers.add(TextEditingController(text: 'BANK0001234'));
        _controllers.add(TextEditingController(text: 'Account Holder'));
        _controllers.add(TextEditingController(text: bankName));
        break;

      case PaymentType.paypal:
        _fieldLabels.addAll(['PayPal Email', 'Full Name']);
        _fieldHints.addAll(['<EMAIL>', 'Your full name']);
        _keyboardTypes.addAll([TextInputType.emailAddress, TextInputType.name]);

        // Pre-populate with existing data
        String email = _extractPayPalEmail(widget.paymentMethod.name);
        _controllers.add(TextEditingController(text: email));
        _controllers.add(TextEditingController(text: 'Full Name'));
        break;

      case PaymentType.bitcoin:
        _fieldLabels.addAll(['Bitcoin Wallet Address', 'Wallet Label']);
        _fieldHints.addAll([
          '******************************************',
          'Optional label for this wallet',
        ]);
        _keyboardTypes.addAll([TextInputType.text, TextInputType.text]);

        // Pre-populate with existing data
        String walletLabel = _extractBitcoinLabel(widget.paymentMethod.name);
        String maskedAddress = _extractBitcoinAddress(
          widget.paymentMethod.name,
        );
        _controllers.add(TextEditingController(text: maskedAddress));
        _controllers.add(TextEditingController(text: walletLabel));
        break;
    }
  }

  // Helper methods to extract data from payment method names
  String _extractUpiId(String name) {
    if (name.contains(' - ')) {
      return name.split(' - ')[1];
    }
    return '';
  }

  String _extractLastFour(String name) {
    if (name.contains('••••')) {
      return name.split('••••')[1];
    }
    return '';
  }

  String _extractBankName(String name) {
    if (name.contains(' Account')) {
      return name.split(' Account')[0];
    }
    return '';
  }

  String _extractPayPalEmail(String name) {
    if (name.contains(' - ')) {
      return name.split(' - ')[1];
    }
    return '';
  }

  String _extractBitcoinLabel(String name) {
    if (name.contains(' - ')) {
      return name.split(' - ')[0];
    }
    return 'Bitcoin Wallet';
  }

  String _extractBitcoinAddress(String name) {
    if (name.contains(' - ')) {
      return name.split(' - ')[1];
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final String methodName = _getMethodName();

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: GoldenAppBar(title: 'Edit $methodName'),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoCard(isDarkMode, methodName),
                const SizedBox(height: 24),
                _buildFormFields(isDarkMode),
                const SizedBox(height: 32),
                _buildActionButtons(isDarkMode),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getMethodName() {
    switch (widget.paymentMethod.type) {
      case PaymentType.upi:
        return 'UPI';
      case PaymentType.creditCard:
        return 'Credit Card';
      case PaymentType.debitCard:
        return 'Debit Card';
      case PaymentType.bankAccount:
        return 'Bank Account';
      case PaymentType.paypal:
        return 'PayPal';
      case PaymentType.bitcoin:
        return 'Bitcoin';
    }
  }

  Widget _buildInfoCard(bool isDarkMode, String methodName) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: isDarkMode ? const Color(0xFFD4AF37) : Colors.green[700],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Edit Payment Method',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color:
                      isDarkMode ? const Color(0xFFD4AF37) : Colors.green[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Update your $methodName payment method details below.',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Details',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(_fieldLabels.length, (index) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _fieldLabels[index],
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _controllers[index],
                  keyboardType: _keyboardTypes[index],
                  decoration: InputDecoration(
                    hintText: _fieldHints[index],
                    hintStyle: GoogleFonts.poppins(
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                    filled: true,
                    fillColor:
                        isDarkMode
                            ? Colors.grey[800]!.withValues(alpha: 0.5)
                            : Colors.grey[100],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                  ),
                  style: GoogleFonts.poppins(
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter ${_fieldLabels[index].toLowerCase()}';
                    }
                    return null;
                  },
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildActionButtons(bool isDarkMode) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveChanges,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Text(
                      'Save Changes',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => context.pop(),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Theme.of(context).primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _saveChanges() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate API call delay
    await Future.delayed(const Duration(seconds: 1));

    // Create updated payment method name based on the type and user input
    String updatedName = _createUpdatedPaymentMethodName();

    // Create updated payment method
    final updatedMethod = PaymentMethod(
      type: widget.paymentMethod.type,
      name: updatedName,
      isDefault: widget.paymentMethod.isDefault,
      lastUsed: 'Updated today',
    );

    // Call the callback to save changes
    widget.onSave(updatedMethod);

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${_getMethodName()} updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
      context.pop();
    }
  }

  String _createUpdatedPaymentMethodName() {
    switch (widget.paymentMethod.type) {
      case PaymentType.upi:
        return 'UPI - ${_controllers[0].text}';
      case PaymentType.creditCard:
      case PaymentType.debitCard:
        String cardNumber = _controllers[0].text;
        String lastFour = '';
        if (cardNumber.length >= 4) {
          lastFour = cardNumber.substring(cardNumber.length - 4);
        }
        String cardType =
            widget.paymentMethod.type == PaymentType.creditCard
                ? 'Credit'
                : 'Debit';
        return '$cardType Card ••••$lastFour';
      case PaymentType.bankAccount:
        String accountNumber = _controllers[0].text;
        String lastFour = '';
        if (accountNumber.length >= 4) {
          lastFour = accountNumber.substring(accountNumber.length - 4);
        }
        return '${_controllers[3].text} Account ••••$lastFour';
      case PaymentType.paypal:
        return 'PayPal - ${_controllers[0].text}';
      case PaymentType.bitcoin:
        String walletAddress = _controllers[0].text;
        String maskedAddress = '';
        if (walletAddress.length > 10) {
          String first6 = walletAddress.substring(0, 6);
          String last4 = walletAddress.substring(walletAddress.length - 4);
          maskedAddress = '$first6....$last4';
        } else {
          maskedAddress = walletAddress;
        }
        String label =
            _controllers[1].text.isNotEmpty
                ? _controllers[1].text
                : 'Bitcoin Wallet';
        return '$label - $maskedAddress';
    }
  }
}
