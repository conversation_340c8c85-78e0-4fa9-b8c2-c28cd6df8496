#!/usr/bin/env node

/**
 * Main Deployment Script for WiggyZ Backend to Vercel
 * Handles TypeScript compilation, Vercel deployment, and configuration updates
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const EnvironmentManager = require('./environment-manager');

class DeploymentManager {
  constructor() {
    this.rootDir = path.resolve(__dirname, '../..');
    this.backendDir = path.join(this.rootDir, 'backend');
    this.envManager = new EnvironmentManager();
    this.deploymentLog = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, message, type };
    this.deploymentLog.push(logEntry);
    
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} ${message}`);
  }

  validatePrerequisites() {
    this.log('Validating deployment prerequisites...');
    
    // Check if Vercel CLI is installed
    try {
      execSync('vercel --version', { stdio: 'pipe' });
      this.log('Vercel CLI is installed', 'success');
    } catch (error) {
      this.log('Vercel CLI not found. Please install with: npm install -g vercel', 'error');
      throw new Error('Missing Vercel CLI');
    }
    
    // Check if backend directory exists
    if (!fs.existsSync(this.backendDir)) {
      this.log('Backend directory not found', 'error');
      throw new Error('Backend directory missing');
    }
    
    // Check if package.json exists
    const packageJsonPath = path.join(this.backendDir, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      this.log('Backend package.json not found', 'error');
      throw new Error('Backend package.json missing');
    }
    
    // Check if vercel.json exists
    const vercelConfigPath = path.join(this.backendDir, 'vercel.json');
    if (!fs.existsSync(vercelConfigPath)) {
      this.log('vercel.json configuration not found', 'error');
      throw new Error('Vercel configuration missing');
    }
    
    this.log('All prerequisites validated', 'success');
  }

  buildBackend() {
    this.log('Building TypeScript backend...');
    
    try {
      // Change to backend directory
      process.chdir(this.backendDir);
      
      // Clean previous build (Windows compatible)
      if (fs.existsSync('dist')) {
        if (process.platform === 'win32') {
          execSync('rmdir /s /q dist', { stdio: 'inherit' });
        } else {
          execSync('rm -rf dist', { stdio: 'inherit' });
        }
      }
      
      // Run TypeScript compilation
      execSync('npm run build', { stdio: 'inherit' });
      
      // Verify build output
      if (!fs.existsSync('dist/server.js')) {
        throw new Error('Build output not found');
      }
      
      this.log('Backend build completed successfully', 'success');
    } catch (error) {
      this.log(`Backend build failed: ${error.message}`, 'error');
      throw error;
    }
  }

  deployToVercel(environment = 'production') {
    this.log(`Deploying to Vercel (${environment})...`);
    
    try {
      // Ensure we're in the backend directory
      process.chdir(this.backendDir);
      
      // Deploy based on environment
      const deployCommand = environment === 'production' ? 'vercel --prod --yes' : 'vercel --yes';

      this.log(`Running: ${deployCommand}`);
      const output = execSync(deployCommand, { encoding: 'utf8', stdio: 'pipe' });
      
      // Extract deployment URL from output
      const urlMatch = output.match(/https:\/\/[^\s]+/);
      const deploymentUrl = urlMatch ? urlMatch[0] : null;
      
      if (deploymentUrl) {
        this.log(`Deployment successful! URL: ${deploymentUrl}`, 'success');
        return deploymentUrl;
      } else {
        this.log('Deployment completed but URL not found in output', 'warn');
        return null;
      }
    } catch (error) {
      this.log(`Vercel deployment failed: ${error.message}`, 'error');
      throw error;
    }
  }

  updateEnvironmentConfigs(deploymentUrl, environment = 'production') {
    this.log('Updating environment configurations...');
    
    try {
      // Update the environments.json with the actual deployment URL
      const envConfigPath = path.join(this.rootDir, 'config', 'environments.json');
      const environments = JSON.parse(fs.readFileSync(envConfigPath, 'utf8'));
      
      if (deploymentUrl) {
        environments[environment].backend.url = deploymentUrl;
        environments[environment].frontend.apiBaseUrl = `${deploymentUrl}/api/v1`;
        environments[environment].admin.apiBaseUrl = `${deploymentUrl}/api/v1`;
        
        fs.writeFileSync(envConfigPath, JSON.stringify(environments, null, 2));
        this.log(`Updated ${environment} environment configuration`, 'success');
      }
      
      // Switch to the deployed environment
      this.envManager.switchEnvironment(environment);
      
    } catch (error) {
      this.log(`Failed to update environment configs: ${error.message}`, 'error');
      throw error;
    }
  }

  validateDeployment(deploymentUrl) {
    this.log('Validating deployment...');
    
    if (!deploymentUrl) {
      this.log('No deployment URL to validate', 'warn');
      return false;
    }
    
    try {
      // Test health endpoint
      const healthUrl = `${deploymentUrl}/health`;
      this.log(`Testing health endpoint: ${healthUrl}`);
      
      // Use curl or similar to test the endpoint
      // For now, just log the URL
      this.log(`Health check URL: ${healthUrl}`, 'success');
      this.log('Manual validation required - please test the deployment', 'warn');
      
      return true;
    } catch (error) {
      this.log(`Deployment validation failed: ${error.message}`, 'error');
      return false;
    }
  }

  saveDeploymentLog() {
    const logPath = path.join(this.rootDir, 'deployment-logs', `deployment-${Date.now()}.json`);
    const logDir = path.dirname(logPath);
    
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    fs.writeFileSync(logPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      logs: this.deploymentLog
    }, null, 2));
    
    this.log(`Deployment log saved to: ${logPath}`);
  }

  async deploy(options = {}) {
    const { environment = 'production', skipBuild = false, skipValidation = false } = options;
    
    try {
      this.log(`Starting deployment to ${environment}...`);
      
      // Validate prerequisites
      this.validatePrerequisites();
      
      // Build backend (unless skipped)
      if (!skipBuild) {
        this.buildBackend();
      }
      
      // Deploy to Vercel
      const deploymentUrl = this.deployToVercel(environment);
      
      // Update environment configurations
      this.updateEnvironmentConfigs(deploymentUrl, environment);
      
      // Validate deployment (unless skipped)
      if (!skipValidation) {
        this.validateDeployment(deploymentUrl);
      }
      
      this.log('Deployment completed successfully!', 'success');
      
      if (deploymentUrl) {
        this.log(`🚀 Your backend is live at: ${deploymentUrl}`);
        this.log(`📱 Flutter apps should now use: ${deploymentUrl}/api/v1`);
        this.log(`🖥️  Admin dashboard should now use: ${deploymentUrl}/api/v1`);
      }
      
      return { success: true, url: deploymentUrl };
      
    } catch (error) {
      this.log(`Deployment failed: ${error.message}`, 'error');
      return { success: false, error: error.message };
    } finally {
      this.saveDeploymentLog();
    }
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const manager = new DeploymentManager();
  
  // Parse command line arguments
  const options = {};
  let environment = 'production';
  
  args.forEach((arg, index) => {
    if (arg === '--env' && args[index + 1]) {
      environment = args[index + 1];
    } else if (arg === '--skip-build') {
      options.skipBuild = true;
    } else if (arg === '--skip-validation') {
      options.skipValidation = true;
    }
  });
  
  options.environment = environment;
  
  console.log('🚀 WiggyZ Backend Deployment to Vercel');
  console.log(`📍 Target environment: ${environment}`);
  
  manager.deploy(options)
    .then(result => {
      if (result.success) {
        console.log('\n✅ Deployment completed successfully!');
        process.exit(0);
      } else {
        console.log('\n❌ Deployment failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Deployment crashed:', error.message);
      process.exit(1);
    });
}

module.exports = DeploymentManager;
