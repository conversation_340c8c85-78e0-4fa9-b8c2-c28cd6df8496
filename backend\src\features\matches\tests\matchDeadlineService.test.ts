/**
 * Tests for Match Deadline Service
 * 
 * Tests automated result finalization logic, edge cases, and background job functionality
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { MatchDeadlineService } from '../services/matchDeadlineService';
import { supabase } from '../../../config/supabase';
import { getRedisClient } from '../../../config/redis';

// Mock dependencies
jest.mock('../../../config/supabase');
jest.mock('../../../config/redis');
jest.mock('../services/matchNotificationService');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockRedis = {
  set: jest.fn(),
  del: jest.fn(),
};

describe('MatchDeadlineService', () => {
  let service: MatchDeadlineService;
  let mockRpc: jest.Mock;
  let mockFrom: jest.Mock;
  let mockSelect: jest.Mock;
  let mockUpdate: jest.Mock;
  let mockInsert: jest.Mock;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup Supabase mocks
    mockRpc = jest.fn();
    mockFrom = jest.fn();
    mockSelect = jest.fn();
    mockUpdate = jest.fn();
    mockInsert = jest.fn();

    mockSupabase.rpc = mockRpc;
    mockSupabase.from = mockFrom;

    // Chain mock setup
    mockFrom.mockReturnValue({
      select: mockSelect,
      update: mockUpdate,
      insert: mockInsert,
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
    });

    mockSelect.mockReturnValue({
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis(),
    });

    mockUpdate.mockReturnValue({
      eq: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
    });

    // Mock Redis
    (getRedisClient as jest.Mock).mockReturnValue(mockRedis);

    service = new MatchDeadlineService();
  });

  afterEach(() => {
    service.stopDeadlineMonitoring();
  });

  describe('getExpiredMatches', () => {
    it('should return expired matches from database function', async () => {
      const mockExpiredMatches = [
        {
          match_id: 'match-1',
          deadline: '2025-01-01T10:00:00Z',
          participants_submitted: 1,
          total_participants: 2,
          match_status: 'live'
        }
      ];

      mockRpc.mockResolvedValue({
        data: mockExpiredMatches,
        error: null
      });

      const result = await service['getExpiredMatches']();

      expect(mockRpc).toHaveBeenCalledWith('get_expired_matches_for_finalization');
      expect(result).toEqual(mockExpiredMatches);
    });

    it('should handle database errors gracefully', async () => {
      mockRpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      });

      await expect(service['getExpiredMatches']()).rejects.toThrow('Failed to get expired matches: Database error');
    });
  });

  describe('finalizeExpiredMatch - No Submissions', () => {
    it('should mark all participants as losers when no one submitted', async () => {
      const expiredMatch = {
        match_id: 'match-1',
        deadline: '2025-01-01T10:00:00Z',
        participants_submitted: 0,
        total_participants: 2,
        match_status: 'live'
      };

      const mockMatch = {
        id: 'match-1',
        title: 'Test Match',
        result_submission_deadline: '2025-01-01T10:00:00Z',
        match_participants: [
          { id: 'p1', user_id: 'user-1', participant_type: 'player', result_submitted_at: null },
          { id: 'p2', user_id: 'user-2', participant_type: 'player', result_submitted_at: null }
        ]
      };

      // Mock getting match details
      mockSelect.mockResolvedValueOnce({
        data: mockMatch,
        error: null
      });

      // Mock updating participants
      mockUpdate.mockResolvedValue({ error: null });

      // Mock updating match status
      mockUpdate.mockResolvedValue({ error: null });

      // Mock updating deadline status
      mockUpdate.mockResolvedValue({ error: null });

      // Mock audit log insertion
      mockInsert.mockResolvedValue({ error: null });

      await service['finalizeExpiredMatch'](expiredMatch);

      // Verify participants were marked as losers
      expect(mockUpdate).toHaveBeenCalledWith({
        is_winner: false,
        auto_assigned_result: true,
        auto_assignment_reason: 'No result submitted by deadline'
      });

      // Verify match was marked as completed
      expect(mockUpdate).toHaveBeenCalledWith(expect.objectContaining({
        status: 'completed',
        auto_finalized: true,
        auto_finalized_reason: 'Deadline expired: 0/2 submitted'
      }));
    });
  });

  describe('finalizeExpiredMatch - Partial Submissions', () => {
    it('should handle partial submissions correctly', async () => {
      const expiredMatch = {
        match_id: 'match-1',
        deadline: '2025-01-01T10:00:00Z',
        participants_submitted: 1,
        total_participants: 2,
        match_status: 'live'
      };

      const mockMatch = {
        id: 'match-1',
        title: 'Test Match',
        result_submission_deadline: '2025-01-01T10:00:00Z',
        match_participants: [
          { 
            id: 'p1', 
            user_id: 'user-1', 
            participant_type: 'player', 
            result_submitted_at: '2025-01-01T09:30:00Z',
            result_position: 1,
            result_score: 100
          },
          { 
            id: 'p2', 
            user_id: 'user-2', 
            participant_type: 'player', 
            result_submitted_at: null 
          }
        ]
      };

      mockSelect.mockResolvedValueOnce({
        data: mockMatch,
        error: null
      });

      mockUpdate.mockResolvedValue({ error: null });
      mockInsert.mockResolvedValue({ error: null });

      await service['finalizeExpiredMatch'](expiredMatch);

      // Verify non-submitted participant was marked as loser
      expect(mockUpdate).toHaveBeenCalledWith({
        is_winner: false,
        auto_assigned_result: true,
        auto_assignment_reason: 'No result submitted by deadline'
      });

      // Verify submitted participant was marked as winner
      expect(mockUpdate).toHaveBeenCalledWith({ is_winner: true });
    });
  });

  describe('determineWinner', () => {
    it('should determine winner by position (lower is better)', () => {
      const participants = [
        { id: 'p1', user_id: 'user-1', result_position: 2, result_score: 80 },
        { id: 'p2', user_id: 'user-2', result_position: 1, result_score: 70 },
        { id: 'p3', user_id: 'user-3', result_position: 3, result_score: 90 }
      ];

      const winner = service['determineWinner'](participants);
      expect(winner.user_id).toBe('user-2'); // Position 1 wins
    });

    it('should determine winner by score when no positions', () => {
      const participants = [
        { id: 'p1', user_id: 'user-1', result_position: null, result_score: 80 },
        { id: 'p2', user_id: 'user-2', result_position: null, result_score: 100 },
        { id: 'p3', user_id: 'user-3', result_position: null, result_score: 90 }
      ];

      const winner = service['determineWinner'](participants);
      expect(winner.user_id).toBe('user-2'); // Highest score wins
    });

    it('should return first submitted when no position or score', () => {
      const participants = [
        { id: 'p1', user_id: 'user-1', result_submitted_at: '2025-01-01T09:32:00Z' },
        { id: 'p2', user_id: 'user-2', result_submitted_at: '2025-01-01T09:30:00Z' },
        { id: 'p3', user_id: 'user-3', result_submitted_at: '2025-01-01T09:35:00Z' }
      ];

      const winner = service['determineWinner'](participants);
      expect(winner.user_id).toBe('user-2'); // First submitted wins
    });

    it('should return null for empty participants', () => {
      const winner = service['determineWinner']([]);
      expect(winner).toBeNull();
    });

    it('should return single participant as winner', () => {
      const participants = [
        { id: 'p1', user_id: 'user-1', result_position: 1 }
      ];

      const winner = service['determineWinner'](participants);
      expect(winner.user_id).toBe('user-1');
    });
  });

  describe('Redis Locking', () => {
    it('should acquire lock successfully', async () => {
      mockRedis.set.mockResolvedValue('OK');

      const lockAcquired = await service['acquireLock']();
      expect(lockAcquired).toBe(true);
      expect(mockRedis.set).toHaveBeenCalledWith(
        'match_deadline_processor_lock',
        expect.any(String),
        'PX',
        240000, // 4 minutes
        'NX'
      );
    });

    it('should fail to acquire lock when already held', async () => {
      mockRedis.set.mockResolvedValue(null);

      const lockAcquired = await service['acquireLock']();
      expect(lockAcquired).toBe(false);
    });

    it('should release lock successfully', async () => {
      mockRedis.del.mockResolvedValue(1);

      await service['releaseLock']();
      expect(mockRedis.del).toHaveBeenCalledWith('match_deadline_processor_lock');
    });
  });

  describe('Background Processing', () => {
    it('should start and stop deadline monitoring', () => {
      const setIntervalSpy = jest.spyOn(global, 'setInterval');
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');

      service.startDeadlineMonitoring();
      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 300000); // 5 minutes

      service.stopDeadlineMonitoring();
      expect(clearIntervalSpy).toHaveBeenCalled();

      setIntervalSpy.mockRestore();
      clearIntervalSpy.mockRestore();
    });

    it('should not start monitoring if already running', () => {
      const setIntervalSpy = jest.spyOn(global, 'setInterval');

      service.startDeadlineMonitoring();
      service.startDeadlineMonitoring(); // Second call should be ignored

      expect(setIntervalSpy).toHaveBeenCalledTimes(1);

      service.stopDeadlineMonitoring();
      setIntervalSpy.mockRestore();
    });
  });

  describe('Manual Processing', () => {
    it('should process expired matches manually', async () => {
      const mockExpiredMatches = [
        {
          match_id: 'match-1',
          deadline: '2025-01-01T10:00:00Z',
          participants_submitted: 0,
          total_participants: 2,
          match_status: 'live'
        }
      ];

      mockRpc.mockResolvedValue({
        data: mockExpiredMatches,
        error: null
      });

      // Mock successful finalization
      mockSelect.mockResolvedValue({
        data: {
          id: 'match-1',
          match_participants: [
            { id: 'p1', user_id: 'user-1', participant_type: 'player', result_submitted_at: null }
          ]
        },
        error: null
      });

      mockUpdate.mockResolvedValue({ error: null });
      mockInsert.mockResolvedValue({ error: null });

      const result = await service.manualProcessExpiredMatches();

      expect(result.processed).toBe(1);
      expect(result.errors).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors during finalization', async () => {
      const expiredMatch = {
        match_id: 'match-1',
        deadline: '2025-01-01T10:00:00Z',
        participants_submitted: 0,
        total_participants: 2,
        match_status: 'live'
      };

      mockSelect.mockResolvedValue({
        data: null,
        error: { message: 'Match not found' }
      });

      await expect(service['finalizeExpiredMatch'](expiredMatch))
        .rejects.toThrow('Failed to get match details: Match not found');
    });

    it('should update deadline status with error on failure', async () => {
      const expiredMatch = {
        match_id: 'match-1',
        deadline: '2025-01-01T10:00:00Z',
        participants_submitted: 0,
        total_participants: 2,
        match_status: 'live'
      };

      mockSelect.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      });

      mockUpdate.mockResolvedValue({ error: null });

      try {
        await service['finalizeExpiredMatch'](expiredMatch);
      } catch (error) {
        // Expected to throw
      }

      // Verify error was logged to deadline status
      expect(mockUpdate).toHaveBeenCalledWith(expect.objectContaining({
        auto_finalization_attempted: true,
        auto_finalization_error: expect.stringContaining('Database error')
      }));
    });
  });

  describe('Audit Logging', () => {
    it('should log audit entry for automated actions', async () => {
      const auditEntry = {
        match_id: 'match-1',
        action_type: 'auto_finalized' as const,
        triggered_by: 'cron_job' as const,
        affected_participants: ['user-1', 'user-2'],
        action_details: { scenario: 'no_submissions' },
        reason: 'No participants submitted results by deadline',
        deadline_time: '2025-01-01T10:00:00Z',
        participants_submitted: 0,
        participants_defaulted: 2,
        total_participants: 2
      };

      mockInsert.mockResolvedValue({ error: null });

      await service['logAuditEntry'](auditEntry);

      expect(mockInsert).toHaveBeenCalledWith(expect.objectContaining({
        match_id: 'match-1',
        action_type: 'auto_finalized',
        triggered_by: 'cron_job',
        affected_participants: ['user-1', 'user-2'],
        reason: 'No participants submitted results by deadline'
      }));
    });

    it('should handle audit logging errors gracefully', async () => {
      const auditEntry = {
        match_id: 'match-1',
        action_type: 'auto_finalized' as const,
        triggered_by: 'cron_job' as const,
        affected_participants: [],
        action_details: {},
        reason: 'Test',
        participants_submitted: 0,
        participants_defaulted: 0,
        total_participants: 0
      };

      mockInsert.mockResolvedValue({
        error: { message: 'Audit log error' }
      });

      // Should not throw even if audit logging fails
      await expect(service['logAuditEntry'](auditEntry)).resolves.not.toThrow();
    });
  });
});
