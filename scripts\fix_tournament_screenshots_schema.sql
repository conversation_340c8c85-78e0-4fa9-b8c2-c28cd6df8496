-- Fix Tournament Screenshots Schema
-- Date: 2025-07-25
-- Description: Add missing screenshot_urls field to tournament_results table and ensure proper indexing

\echo 'Fixing tournament screenshots schema...'

-- ==================== ADD MISSING SCREENSHOT_URLS FIELD ====================

-- Add screenshot_urls array field to tournament_results table
ALTER TABLE public.tournament_results 
ADD COLUMN IF NOT EXISTS screenshot_urls TEXT[];

-- Add submitted_at field if missing (for admin dashboard compatibility)
ALTER TABLE public.tournament_results 
ADD COLUMN IF NOT EXISTS submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- ==================== UPDATE EXISTING DATA ====================

-- Migrate existing screenshot_url data to screenshot_urls array
UPDATE public.tournament_results 
SET screenshot_urls = ARRAY[screenshot_url]
WHERE screenshot_url IS NOT NULL 
  AND (screenshot_urls IS NULL OR array_length(screenshot_urls, 1) IS NULL);

-- ==================== CREATE INDEXES FOR PERFORMANCE ====================

-- Index for screenshot_urls queries
CREATE INDEX IF NOT EXISTS idx_tournament_results_screenshot_urls 
ON public.tournament_results USING GIN (screenshot_urls);

-- Index for submitted_at queries (admin dashboard sorting)
CREATE INDEX IF NOT EXISTS idx_tournament_results_submitted_at 
ON public.tournament_results (submitted_at DESC);

-- Composite index for admin verification queries
CREATE INDEX IF NOT EXISTS idx_tournament_results_verification_submitted 
ON public.tournament_results (verification_status, submitted_at DESC);

-- ==================== VERIFY SCHEMA CHANGES ====================

\echo 'Verifying tournament_results table structure...'

-- Show the updated table structure
\d public.tournament_results

-- Show sample data to verify migration
SELECT 
  id,
  tournament_id,
  user_id,
  final_position,
  screenshot_url,
  screenshot_urls,
  verification_status,
  submitted_at,
  created_at
FROM public.tournament_results 
LIMIT 5;

\echo 'Tournament screenshots schema fix completed successfully!'
