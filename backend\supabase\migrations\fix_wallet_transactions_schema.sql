-- Fix wallet_transactions table schema to match walletService.ts expectations
-- This migration adds missing columns and aliases existing ones

-- Add missing payment_gateway column (alias for gateway_provider)
ALTER TABLE wallet_transactions 
ADD COLUMN IF NOT EXISTS payment_gateway VARCHAR(50);

-- Add missing payment_token column
ALTER TABLE wallet_transactions 
ADD COLUMN IF NOT EXISTS payment_token VARCHAR(255);

-- Add missing payment_method column (alias for payment_method_id)
ALTER TABLE wallet_transactions 
ADD COLUMN IF NOT EXISTS payment_method VARCHAR(100);

-- Add columns from the payment security migration if they don't exist
ALTER TABLE wallet_transactions 
ADD COLUMN IF NOT EXISTS payment_gateway_response JSONB,
ADD COLUMN IF NOT EXISTS error_message TEXT,
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP WITH TIME ZONE;

-- Create a function to sync gateway_provider to payment_gateway
CREATE OR REPLACE FUNCTION sync_payment_gateway()
R<PERSON>URNS TRIGGER AS $$
BEGIN
  -- If payment_gateway is provided, sync to gateway_provider
  IF NEW.payment_gateway IS NOT NULL THEN
    NEW.gateway_provider = NEW.payment_gateway;
  END IF;
  
  -- If gateway_provider is provided but payment_gateway is null, sync the other way
  IF NEW.gateway_provider IS NOT NULL AND NEW.payment_gateway IS NULL THEN
    NEW.payment_gateway = NEW.gateway_provider;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to keep both columns in sync
DROP TRIGGER IF EXISTS sync_payment_gateway_trigger ON wallet_transactions;
CREATE TRIGGER sync_payment_gateway_trigger
  BEFORE INSERT OR UPDATE ON wallet_transactions
  FOR EACH ROW
  EXECUTE FUNCTION sync_payment_gateway();

-- Update existing records to sync gateway_provider to payment_gateway
UPDATE wallet_transactions 
SET payment_gateway = gateway_provider 
WHERE gateway_provider IS NOT NULL AND payment_gateway IS NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_payment_gateway ON wallet_transactions(payment_gateway);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_payment_token ON wallet_transactions(payment_token);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_expires_at ON wallet_transactions(expires_at);
CREATE INDEX IF NOT EXISTS idx_wallet_transactions_status_created ON wallet_transactions(status, created_at);

-- Add comments for clarity
COMMENT ON COLUMN wallet_transactions.payment_gateway IS 'Payment gateway used (razorpay, stripe, etc.) - synced with gateway_provider';
COMMENT ON COLUMN wallet_transactions.payment_token IS 'Payment gateway specific token/order ID';
COMMENT ON COLUMN wallet_transactions.payment_method IS 'Payment method used (card, upi, netbanking, etc.)';
