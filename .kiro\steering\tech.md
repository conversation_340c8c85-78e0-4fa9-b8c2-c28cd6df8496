# Technology Stack

## Architecture Overview

WiggyZ follows a multi-platform architecture with separate backend API, Flutter mobile app, and Next.js admin dashboard.

## Backend Stack

- **Runtime**: Node.js 18+
- **Language**: TypeScript
- **Framework**: Express.js
- **Database**: Supabase (PostgreSQL)
- **Authentication**: JWT with Supabase Auth
- **Payment Processing**: Razorpay
- **File Storage**: Supabase Storage
- **Caching**: Redis (ioredis)
- **Testing**: Jest with ts-jest
- **Security**: Helmet, CORS, rate limiting

## Frontend Stack

### Flutter Mobile App
- **Framework**: Flutter 3.7.2+
- **Language**: Dart
- **State Management**: Provider pattern
- **Navigation**: GoRouter
- **HTTP Client**: http package
- **Local Storage**: SharedPreferences, FlutterSecureStorage
- **Image Handling**: ImagePicker, CachedNetworkImage
- **Payments**: razorpay_flutter

### Admin Dashboard
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui
- **State Management**: React hooks + Context

## Development Commands

### Backend
```bash
cd backend
npm install
npm run dev          # Start development server
npm run build        # Compile TypeScript
npm run test         # Run Jest tests
npm run setup        # Initialize database
```

### Flutter App
```bash
cd frontend
flutter pub get      # Install dependencies
flutter run          # Run on connected device
flutter build web    # Build for web
flutter test         # Run tests
```

### Root Project
```bash
npm run dev          # Start backend dev server
npm run task         # Run task management
```

## Database Schema

- **Supabase PostgreSQL** with Row Level Security (RLS)
- **Key Tables**: users, matches, tournaments, match_participants, match_results, transactions
- **File Storage**: Supabase Storage for screenshots and profile images

## Security Features

- JWT token authentication with refresh tokens
- Role-based access control (player, admin, manager)
- Input validation with Joi
- Rate limiting and DDoS protection
- Secure file upload with validation
- SQL injection prevention through Supabase client