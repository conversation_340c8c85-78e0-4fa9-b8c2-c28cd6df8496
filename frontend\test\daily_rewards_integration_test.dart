import 'package:flutter_test/flutter_test.dart';
import 'package:wiggyz_app/models/daily_reward_models.dart';
import 'package:wiggyz_app/models/reward_models.dart';

void main() {
  group('Daily Rewards Integration Tests', () {
    test('should handle backend response with minimal streak data', () {
      // Simulate backend response with minimal streak data
      final backendResponse = {
        'streak': {
          'id': 'test-streak-123',
          'user_id': 'test-user',
          'current_streak': 1,
          'longest_streak': 5,
          'last_login_date': null,
          'created_at': '2025-07-21T10:00:00Z',
          'updated_at': '2025-07-21T10:00:00Z',
        },
        'rewards': [
          {
            'id': 'reward-1',
            'type_id': 'daily-login-type',
            'title': 'Day 1 Reward',
            'description': 'First day login reward',
            'points': 10,
            'diamond_value': 5,
            'requirements': {'day': 1},
            'is_active': true,
            'created_at': '2025-07-21T10:00:00Z',
            'updated_at': '2025-07-21T10:00:00Z',
          }
        ],
        'hasClaimedToday': false,
      };

      // Test that DailyRewardStatusModel can parse this response
      final status = DailyRewardStatusModel.fromJson(backendResponse);
      
      expect(status.streak.currentStreak, 1);
      expect(status.streak.longestStreak, 5);
      expect(status.rewards.length, 1);
      expect(status.hasClaimedToday, false);
      expect(status.canClaimToday, true);
    });

    test('should handle backend response with null streak data', () {
      // Simulate backend response with fallback streak data
      final backendResponse = {
        'streak': {
          'id': 'default-streak-test-user',
          'user_id': 'test-user',
          'current_streak': 0,
          'longest_streak': 0,
          'last_login_date': null,
          'created_at': '2025-07-21T10:00:00Z',
          'updated_at': '2025-07-21T10:00:00Z',
        },
        'rewards': [],
        'hasClaimedToday': false,
      };

      final status = DailyRewardStatusModel.fromJson(backendResponse);
      
      expect(status.streak.currentStreak, 0);
      expect(status.streak.longestStreak, 0);
      expect(status.rewards.length, 0);
      expect(status.hasClaimedToday, false);
      expect(status.canClaimToday, false); // No rewards available
    });

    test('should create calendar with proper day states', () {
      final backendResponse = {
        'streak': {
          'id': 'test-streak',
          'user_id': 'test-user',
          'current_streak': 2,
          'longest_streak': 5,
          'last_login_date': '2025-07-20T10:00:00Z',
          'created_at': '2025-07-21T10:00:00Z',
          'updated_at': '2025-07-21T10:00:00Z',
        },
        'rewards': [
          {
            'id': 'reward-1',
            'type_id': 'daily-login-type',
            'title': 'Day 1 Reward',
            'points': 10,
            'diamond_value': 5,
            'requirements': {'day': 1},
            'is_active': true,
            'created_at': '2025-07-21T10:00:00Z',
            'updated_at': '2025-07-21T10:00:00Z',
          },
          {
            'id': 'reward-2',
            'type_id': 'daily-login-type',
            'title': 'Day 2 Reward',
            'points': 15,
            'diamond_value': 10,
            'requirements': {'day': 2},
            'is_active': true,
            'created_at': '2025-07-21T10:00:00Z',
            'updated_at': '2025-07-21T10:00:00Z',
          },
          {
            'id': 'reward-3',
            'type_id': 'daily-login-type',
            'title': 'Day 3 Reward',
            'points': 20,
            'diamond_value': 15,
            'requirements': {'day': 3},
            'is_active': true,
            'created_at': '2025-07-21T10:00:00Z',
            'updated_at': '2025-07-21T10:00:00Z',
          }
        ],
        'hasClaimedToday': false,
      };

      final status = DailyRewardStatusModel.fromJson(backendResponse);
      final calendar = DailyRewardsCalendar.fromStatus(status);
      
      expect(calendar.days.length, 3);
      
      // Day 1 should be claimed (past day)
      final day1 = calendar.days[0];
      expect(day1.day, 1);
      expect(day1.isClaimed, true);
      expect(day1.isCurrentDay, false);
      expect(day1.canClaim, false);
      
      // Day 2 should be current and claimable
      final day2 = calendar.days[1];
      expect(day2.day, 2);
      expect(day2.isClaimed, false);
      expect(day2.isCurrentDay, true);
      expect(day2.canClaim, true);
      
      // Day 3 should be future and locked
      final day3 = calendar.days[2];
      expect(day3.day, 3);
      expect(day3.isClaimed, false);
      expect(day3.isCurrentDay, false);
      expect(day3.canClaim, false);
      expect(day3.isFutureDay, true);
    });

    test('should handle empty rewards configuration gracefully', () {
      final backendResponse = {
        'streak': {
          'id': 'test-streak',
          'user_id': 'test-user',
          'current_streak': 1,
          'longest_streak': 1,
          'last_login_date': '2025-07-21T10:00:00Z',
          'created_at': '2025-07-21T10:00:00Z',
          'updated_at': '2025-07-21T10:00:00Z',
        },
        'rewards': [],
        'hasClaimedToday': false,
      };

      final status = DailyRewardStatusModel.fromJson(backendResponse);
      final calendar = DailyRewardsCalendar.fromStatus(status);
      
      expect(calendar.days.length, 0);
      expect(calendar.canClaimToday, false);
      expect(status.maxRewardDay, 0);
    });

    test('should handle malformed reward data gracefully', () {
      final backendResponse = {
        'streak': {
          'id': 'test-streak',
          'user_id': 'test-user',
          'current_streak': 1,
          'longest_streak': 1,
          'created_at': '2025-07-21T10:00:00Z',
          'updated_at': '2025-07-21T10:00:00Z',
        },
        'rewards': [
          {
            'id': 'reward-1',
            'title': 'Malformed Reward',
            // Missing required fields
          }
        ],
        'hasClaimedToday': false,
      };

      // Should not throw an exception
      expect(() {
        final status = DailyRewardStatusModel.fromJson(backendResponse);
        final calendar = DailyRewardsCalendar.fromStatus(status);
        return calendar;
      }, returnsNormally);
    });
  });
}
