/// Performance tests for the reward system
/// Tests loading times, memory usage, and optimization effectiveness

import 'package:flutter_test/flutter_test.dart';
import 'package:wiggyz_app/utils/reward_performance_monitor.dart';
import 'package:wiggyz_app/utils/reward_cache_manager.dart';
import 'package:wiggyz_app/config/reward_performance_config.dart';
import 'package:wiggyz_app/models/reward_models.dart';
import 'package:wiggyz_app/models/achievement_models.dart';

void main() {
  group('Reward Performance Tests', () {
    late RewardPerformanceMonitor performanceMonitor;
    late RewardCacheManager cacheManager;

    setUp(() {
      performanceMonitor = RewardPerformanceMonitor();
      cacheManager = RewardCacheManager();
    });

    tearDown(() {
      performanceMonitor.clearMetrics();
    });

    group('Performance Monitoring Tests', () {
      test('should track operation duration correctly', () async {
        performanceMonitor.startOperation('test_operation');
        
        // Simulate work
        await Future.delayed(const Duration(milliseconds: 100));
        
        performanceMonitor.endOperation('test_operation', success: true);
        
        final metrics = performanceMonitor.getRecentMetrics(limit: 1);
        expect(metrics.length, 1);
        expect(metrics.first.operation, 'test_operation');
        expect(metrics.first.success, true);
        expect(metrics.first.duration.inMilliseconds, greaterThan(90));
        expect(metrics.first.duration.inMilliseconds, lessThan(200));
      });

      test('should detect slow operations', () async {
        performanceMonitor.startOperation('slow_operation');
        
        // Simulate slow work
        await Future.delayed(const Duration(milliseconds: 2500));
        
        performanceMonitor.endOperation('slow_operation', success: true);
        
        final summary = performanceMonitor.getPerformanceSummary();
        expect(summary['slow_operation']['slowOperations'], 1);
      });

      test('should calculate success rate correctly', () {
        // Add successful operations
        for (int i = 0; i < 8; i++) {
          performanceMonitor.startOperation('test_op');
          performanceMonitor.endOperation('test_op', success: true);
        }
        
        // Add failed operations
        for (int i = 0; i < 2; i++) {
          performanceMonitor.startOperation('test_op');
          performanceMonitor.endOperation('test_op', success: false);
        }
        
        final summary = performanceMonitor.getPerformanceSummary();
        expect(summary['test_op']['successRate'], 0.8);
      });

      test('should determine operation health correctly', () {
        // Add healthy operations
        for (int i = 0; i < 10; i++) {
          performanceMonitor.startOperation('healthy_op');
          performanceMonitor.endOperation('healthy_op', success: true);
        }
        
        expect(performanceMonitor.isOperationHealthy('healthy_op'), true);
        
        // Add unhealthy operations
        for (int i = 0; i < 10; i++) {
          performanceMonitor.startOperation('unhealthy_op');
          performanceMonitor.endOperation('unhealthy_op', success: false);
        }
        
        expect(performanceMonitor.isOperationHealthy('unhealthy_op'), false);
      });
    });

    group('Memory Management Tests', () {
      test('should optimize large data lists', () {
        final memoryManager = RewardMemoryManager();
        
        // Create large list
        final largeList = List.generate(200, (index) => 'item_$index');
        
        memoryManager.optimizeRewardData(largeList, maxItems: 50);
        
        expect(largeList.length, 50);
        expect(largeList.first, 'item_150'); // Should keep the last 50 items
      });

      test('should track memory usage', () {
        final memoryManager = RewardMemoryManager();
        
        memoryManager.trackMemoryUsage('test_component', 1024);
        
        final usage = memoryManager.getMemoryUsage();
        expect(usage['test_component'], 1024);
      });
    });

    group('Cache Performance Tests', () {
      test('should cache and retrieve data efficiently', () async {
        // This test would need SharedPreferences mocking
        // Measure cache write performance
        final stopwatch = Stopwatch()..start();
        
        final testRewards = List.generate(100, (index) => UserRewardModel(
          id: 'reward_$index',
          userId: 'user_1',
          rewardId: 'r_$index',
          claimedAt: DateTime.now(),
        ));
        
        // await cacheManager.cacheUserRewards(testRewards);
        
        stopwatch.stop();
        
        // Cache write should be fast (< 100ms for 100 items)
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('should respect cache duration settings', () {
        final duration = RewardPerformanceConfig.getCacheDuration('daily_rewards');
        expect(duration, RewardPerformanceConfig.dailyRewardsCacheDuration);
        
        final achievementDuration = RewardPerformanceConfig.getCacheDuration('achievements');
        expect(achievementDuration, RewardPerformanceConfig.achievementsCacheDuration);
      });
    });

    group('List Performance Tests', () {
      test('should efficiently compare large lists', () {
        final list1 = List.generate(1000, (index) => 'item_$index');
        final list2 = List.generate(1000, (index) => 'item_$index');
        final list3 = List.generate(1000, (index) => 'different_$index');
        
        final stopwatch = Stopwatch()..start();
        
        final areEqual1 = RewardPerformanceUtils.areListsEqual(list1, list2);
        final areEqual2 = RewardPerformanceUtils.areListsEqual(list1, list3);
        
        stopwatch.stop();
        
        expect(areEqual1, true);
        expect(areEqual2, false);
        
        // Comparison should be fast (< 10ms for 1000 items)
        expect(stopwatch.elapsedMilliseconds, lessThan(10));
      });

      test('should paginate data efficiently', () {
        final largeList = List.generate(1000, (index) => 'item_$index');
        
        final stopwatch = Stopwatch()..start();
        
        final page1 = RewardPerformanceUtils.paginateData(largeList, 0, 20);
        final page2 = RewardPerformanceUtils.paginateData(largeList, 1, 20);
        
        stopwatch.stop();
        
        expect(page1.length, 20);
        expect(page1.first, 'item_0');
        expect(page1.last, 'item_19');
        
        expect(page2.length, 20);
        expect(page2.first, 'item_20');
        expect(page2.last, 'item_39');
        
        // Pagination should be very fast (< 1ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(1));
      });
    });

    group('Configuration Tests', () {
      test('should provide correct configuration values', () {
        expect(RewardPerformanceConfig.maxUserRewards, 100);
        expect(RewardPerformanceConfig.slowOperationThreshold.inSeconds, 2);
        expect(RewardPerformanceConfig.enablePerformanceMonitoring, true);
      });

      test('should return correct memory limits', () {
        expect(RewardPerformanceConfig.getMemoryLimit('user_rewards'), 100);
        expect(RewardPerformanceConfig.getMemoryLimit('achievements'), 50);
        expect(RewardPerformanceConfig.getMemoryLimit('unknown'), 100); // default
      });

      test('should check feature flags correctly', () {
        expect(RewardPerformanceConfig.isFeatureEnabled('performance_monitoring'), true);
        expect(RewardPerformanceConfig.isFeatureEnabled('unknown_feature'), false);
      });
    });

    group('Stress Tests', () {
      test('should handle large number of operations', () async {
        final stopwatch = Stopwatch()..start();
        
        // Simulate 1000 operations
        for (int i = 0; i < 1000; i++) {
          performanceMonitor.startOperation('stress_test');
          performanceMonitor.endOperation('stress_test', success: true);
        }
        
        stopwatch.stop();
        
        // Should handle 1000 operations quickly (< 100ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
        
        final summary = performanceMonitor.getPerformanceSummary();
        expect(summary['stress_test']['count'], 1000);
      });

      test('should maintain performance with large datasets', () {
        final largeAchievementList = List.generate(500, (index) => AchievementModel(
          id: 'achievement_$index',
          userId: 'user_1',
          title: 'Achievement $index',
          description: 'Description $index',
          target: 100,
          current: index % 100,
          isCompleted: index % 100 == 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));
        
        final stopwatch = Stopwatch()..start();
        
        // Filter completed achievements
        final completed = largeAchievementList.where((a) => a.isCompleted).toList();
        
        // Filter in progress
        final inProgress = largeAchievementList.where((a) => !a.isCompleted).toList();
        
        stopwatch.stop();
        
        expect(completed.length, greaterThan(0));
        expect(inProgress.length, greaterThan(0));
        
        // Filtering should be fast even with large datasets (< 10ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(10));
      });
    });

    group('Memory Leak Tests', () {
      test('should not leak memory with repeated operations', () {
        // This test would need more sophisticated memory monitoring
        // For now, we test that cleanup works properly
        
        for (int i = 0; i < 100; i++) {
          performanceMonitor.startOperation('memory_test');
          performanceMonitor.endOperation('memory_test', success: true);
        }
        
        final initialMetricsCount = performanceMonitor.getRecentMetrics().length;
        
        performanceMonitor.clearMetrics();
        
        final finalMetricsCount = performanceMonitor.getRecentMetrics().length;
        
        expect(finalMetricsCount, 0);
        expect(initialMetricsCount, greaterThan(0));
      });
    });
  });
}
