{"timestamp": "2025-07-31T07:36:45.079Z", "logs": [{"timestamp": "2025-07-31T07:35:19.433Z", "message": "Starting deployment to staging...", "type": "info"}, {"timestamp": "2025-07-31T07:35:19.437Z", "message": "Validating deployment prerequisites...", "type": "info"}, {"timestamp": "2025-07-31T07:35:21.934Z", "message": "Vercel CLI is installed", "type": "success"}, {"timestamp": "2025-07-31T07:35:21.936Z", "message": "All prerequisites validated", "type": "success"}, {"timestamp": "2025-07-31T07:35:21.937Z", "message": "Building TypeScript backend...", "type": "info"}, {"timestamp": "2025-07-31T07:36:02.539Z", "message": "Backend build completed successfully", "type": "success"}, {"timestamp": "2025-07-31T07:36:02.542Z", "message": "Deploying to Vercel (staging)...", "type": "info"}, {"timestamp": "2025-07-31T07:36:02.549Z", "message": "Running: vercel --yes", "type": "info"}, {"timestamp": "2025-07-31T07:36:45.060Z", "message": "Deployment successful! URL: https://wiggyz-backend-fxui5976h-tausifraja977-gmailcoms-projects.vercel.app", "type": "success"}, {"timestamp": "2025-07-31T07:36:45.061Z", "message": "Updating environment configurations...", "type": "info"}, {"timestamp": "2025-07-31T07:36:45.063Z", "message": "Updated staging environment configuration", "type": "success"}, {"timestamp": "2025-07-31T07:36:45.073Z", "message": "Validating deployment...", "type": "info"}, {"timestamp": "2025-07-31T07:36:45.073Z", "message": "Testing health endpoint: https://wiggyz-backend-fxui5976h-tausifraja977-gmailcoms-projects.vercel.app/health", "type": "info"}, {"timestamp": "2025-07-31T07:36:45.073Z", "message": "Health check URL: https://wiggyz-backend-fxui5976h-tausifraja977-gmailcoms-projects.vercel.app/health", "type": "success"}, {"timestamp": "2025-07-31T07:36:45.074Z", "message": "Manual validation required - please test the deployment", "type": "warn"}, {"timestamp": "2025-07-31T07:36:45.077Z", "message": "Deployment completed successfully!", "type": "success"}, {"timestamp": "2025-07-31T07:36:45.077Z", "message": "🚀 Your backend is live at: https://wiggyz-backend-fxui5976h-tausifraja977-gmailcoms-projects.vercel.app", "type": "info"}, {"timestamp": "2025-07-31T07:36:45.077Z", "message": "📱 Flutter apps should now use: https://wiggyz-backend-fxui5976h-tausifraja977-gmailcoms-projects.vercel.app/api/v1", "type": "info"}, {"timestamp": "2025-07-31T07:36:45.078Z", "message": "🖥️  Admin dashboard should now use: https://wiggyz-backend-fxui5976h-tausifraja977-gmailcoms-projects.vercel.app/api/v1", "type": "info"}]}