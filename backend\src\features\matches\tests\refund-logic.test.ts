/**
 * Test suite for match refund logic
 * Tests the automatic refund functionality for expired matches with no participants
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { supabase } from '../../../config/supabase';
import { MatchDeadlineService } from '../services/matchDeadlineService';
import { walletService } from '../../wallet/services/walletService';
import { matchNotificationService } from '../services/matchNotificationService';

// Mock dependencies
jest.mock('../../../config/supabase');
jest.mock('../../wallet/services/walletService');
jest.mock('../services/matchNotificationService');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockWalletService = walletService as jest.Mocked<typeof walletService>;
const mockNotificationService = matchNotificationService as jest.Mocked<typeof matchNotificationService>;

describe('Match Refund Logic', () => {
  let matchDeadlineService: MatchDeadlineService;
  
  beforeEach(() => {
    jest.clearAllMocks();
    matchDeadlineService = new MatchDeadlineService();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('processCreatorRefundIfEligible', () => {
    const mockMatch = {
      id: 'match-123',
      created_by: 'user-456',
      entry_fee: 100,
      refund_processed: false,
      title: 'Test Match',
      result_submission_deadline: '2025-07-22T10:00:00Z'
    };

    it('should process refund for eligible match (no participants, has entry fee)', async () => {
      // Setup: Match with entry fee, no participants except creator
      const participants = [
        { user_id: 'user-456', participant_type: 'player' } // Only creator
      ];

      // Mock database function success
      mockSupabase.rpc.mockResolvedValueOnce({
        data: { success: true, refund_amount: 100, creator_id: 'user-456' },
        error: null
      });

      // Mock wallet service success
      mockWalletService.processRefund.mockResolvedValueOnce({
        wallet: { balance: 1100 },
        transaction: { id: 'tx-123', amount: 100 }
      });

      // Mock notification service
      mockNotificationService.notifyRefundProcessed.mockResolvedValueOnce();

      // Call the private method through reflection (for testing)
      const service = matchDeadlineService as any;
      await service.processCreatorRefundIfEligible(mockMatch, participants);

      // Verify database function was called
      expect(mockSupabase.rpc).toHaveBeenCalledWith('process_creator_refund', {
        match_id_param: 'match-123'
      });

      // Verify wallet refund was processed
      expect(mockWalletService.processRefund).toHaveBeenCalledWith(
        'user-456',
        100,
        'Match entry fee refund - Match expired with no participants',
        { match_id: 'match-123', refund_reason: 'No participants joined before deadline' }
      );

      // Verify notification was sent
      expect(mockNotificationService.notifyRefundProcessed).toHaveBeenCalledWith(
        mockMatch,
        100
      );
    });

    it('should not process refund when match has no entry fee', async () => {
      const matchNoFee = { ...mockMatch, entry_fee: 0 };
      const participants = [
        { user_id: 'user-456', participant_type: 'player' }
      ];

      const service = matchDeadlineService as any;
      await service.processCreatorRefundIfEligible(matchNoFee, participants);

      // Should not call any refund functions
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
      expect(mockWalletService.processRefund).not.toHaveBeenCalled();
      expect(mockNotificationService.notifyRefundProcessed).not.toHaveBeenCalled();
    });

    it('should not process refund when participants have joined', async () => {
      const participants = [
        { user_id: 'user-456', participant_type: 'player' }, // Creator
        { user_id: 'user-789', participant_type: 'player' }  // Other participant
      ];

      const service = matchDeadlineService as any;
      await service.processCreatorRefundIfEligible(mockMatch, participants);

      // Should not call any refund functions
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
      expect(mockWalletService.processRefund).not.toHaveBeenCalled();
      expect(mockNotificationService.notifyRefundProcessed).not.toHaveBeenCalled();
    });

    it('should not process refund when already processed', async () => {
      const processedMatch = { ...mockMatch, refund_processed: true };
      const participants = [
        { user_id: 'user-456', participant_type: 'player' }
      ];

      const service = matchDeadlineService as any;
      await service.processCreatorRefundIfEligible(processedMatch, participants);

      // Should not call any refund functions
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
      expect(mockWalletService.processRefund).not.toHaveBeenCalled();
      expect(mockNotificationService.notifyRefundProcessed).not.toHaveBeenCalled();
    });

    it('should handle database function failure gracefully', async () => {
      const participants = [
        { user_id: 'user-456', participant_type: 'player' }
      ];

      // Mock database function failure
      mockSupabase.rpc.mockResolvedValueOnce({
        data: null,
        error: { message: 'Database error' }
      });

      const service = matchDeadlineService as any;
      
      // Should not throw error (graceful handling)
      await expect(service.processCreatorRefundIfEligible(mockMatch, participants))
        .resolves.not.toThrow();

      // Should not proceed to wallet update
      expect(mockWalletService.processRefund).not.toHaveBeenCalled();
      expect(mockNotificationService.notifyRefundProcessed).not.toHaveBeenCalled();
    });

    it('should handle wallet service failure gracefully', async () => {
      const participants = [
        { user_id: 'user-456', participant_type: 'player' }
      ];

      // Mock database function success
      mockSupabase.rpc.mockResolvedValueOnce({
        data: { success: true, refund_amount: 100, creator_id: 'user-456' },
        error: null
      });

      // Mock wallet service failure
      mockWalletService.processRefund.mockRejectedValueOnce(
        new Error('Wallet service unavailable')
      );

      const service = matchDeadlineService as any;
      
      // Should not throw error (graceful handling)
      await expect(service.processCreatorRefundIfEligible(mockMatch, participants))
        .resolves.not.toThrow();

      // Should not proceed to notification
      expect(mockNotificationService.notifyRefundProcessed).not.toHaveBeenCalled();
    });
  });

  describe('Database Functions', () => {
    it('should test is_match_eligible_for_creator_refund function', async () => {
      // Test the database function directly
      mockSupabase.rpc.mockResolvedValueOnce({
        data: true,
        error: null
      });

      const { data: isEligible } = await supabase.rpc('is_match_eligible_for_creator_refund', {
        match_id_param: 'match-123'
      });

      expect(isEligible).toBe(true);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('is_match_eligible_for_creator_refund', {
        match_id_param: 'match-123'
      });
    });

    it('should test process_creator_refund function', async () => {
      const expectedResult = {
        success: true,
        refund_amount: 100,
        creator_id: 'user-456',
        timestamp: expect.any(String)
      };

      mockSupabase.rpc.mockResolvedValueOnce({
        data: expectedResult,
        error: null
      });

      const { data: result } = await supabase.rpc('process_creator_refund', {
        match_id_param: 'match-123'
      });

      expect(result).toEqual(expectedResult);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('process_creator_refund', {
        match_id_param: 'match-123'
      });
    });
  });
});
