/**
 * Atomic Match Service - Provides transactional match creation and payment processing
 * Ensures data consistency by wrapping match creation and payment in database transactions
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { supabase } from '../../../config/supabase';
import { walletService } from '../../wallet/services/walletService';
import { logger } from '../../../utils/logger';
import { CreateMatchData, Match, MatchParticipant } from '../types/matchTypes';

export class AtomicMatchService {
  private supabase: SupabaseClient;

  constructor() {
    this.supabase = supabase;
  }

  /**
   * Create a standalone match with atomic payment processing
   * Uses database transactions to ensure consistency
   */
  async createStandaloneMatchAtomic(matchData: CreateMatchData, userId: string): Promise<Match> {
    logger.info(`Starting atomic match creation for user ${userId}`);

    // Validate input data first
    this.validateMatchData(matchData);

    try {
      // Check user balance if entry fee is required
      if (matchData.entry_fee && matchData.entry_fee > 0) {
        const hasSufficientBalance = await this.validateSufficientBalance(userId, matchData.entry_fee);
        if (!hasSufficientBalance) {
          const currentBalance = await this.getCurrentBalance(userId);
          throw new Error(`Insufficient funds. Current balance: ${currentBalance}, Required: ${matchData.entry_fee}`);
        }
      }

      // Prepare match payload
      const matchPayload = {
        ...matchData,
        match_type: 'standalone',
        created_by: userId,
        result_submission_deadline: matchData.result_submission_deadline ||
          new Date(new Date(matchData.match_time).getTime() + 30 * 60000).toISOString(),
        tournament_id: null,
        status: 'scheduled'
      };

      // Start atomic operation using Supabase transaction
      const { data: createdMatch, error: createError } = await this.supabase
        .from('matches')
        .insert([matchPayload])
        .select(`
          *,
          games (id, name, image),
          users!matches_created_by_fkey (id, name, email)
        `)
        .single();

      if (createError) {
        throw new Error(`Failed to create match: ${createError.message}`);
      }

      // Auto-join creator with atomic payment processing
      try {
        await this.joinMatchAtomic(createdMatch.id, userId, 'player');
        logger.info(`Atomic match creation successful - Match ID: ${createdMatch.id}`);
        return createdMatch;
      } catch (joinError) {
        // Rollback match creation if auto-join fails
        const errorMessage = joinError instanceof Error ? joinError.message : String(joinError);
        await this.rollbackMatchCreation(createdMatch.id, `Auto-join failed: ${errorMessage}`);
        throw joinError;
      }
    } catch (error) {
      logger.error(`Atomic match creation failed for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Join a match with atomic payment processing
   * Ensures payment and participation are processed together
   */
  async joinMatchAtomic(matchId: string, userId: string, participantType: 'player' | 'spectator' = 'player'): Promise<MatchParticipant> {
    logger.info(`Starting atomic match join for user ${userId}, match ${matchId}`);

    try {
      // Get match details first
      const { data: match, error: matchError } = await this.supabase
        .from('matches')
        .select(`
          *,
          match_participants (user_id, participant_type)
        `)
        .eq('id', matchId)
        .single();

      if (matchError || !match) {
        throw new Error(`Match not found: ${matchId}`);
      }

      // Validate match state
      if (match.status !== 'scheduled') {
        throw new Error(`Cannot join match with status: ${match.status}`);
      }

      // Check if user is already a participant
      const existingParticipant = match.match_participants?.find(
        (p: any) => p.user_id === userId
      );

      if (existingParticipant) {
        throw new Error('User is already a participant in this match');
      }

      // Check match capacity for players
      if (participantType === 'player') {
        const playerCount = match.match_participants?.filter(
          (p: any) => p.participant_type === 'player'
        ).length || 0;

        if (playerCount >= match.max_participants) {
          throw new Error('Match is full');
        }

        // Process entry fee if applicable
        if (match.entry_fee && match.entry_fee > 0) {
          const hasSufficientBalance = await this.validateSufficientBalance(userId, match.entry_fee);
          if (!hasSufficientBalance) {
            const currentBalance = await this.getCurrentBalance(userId);
            throw new Error(`Insufficient funds. Current balance: ${currentBalance}, Required: ${match.entry_fee}`);
          }

          // Process payment atomically
          await walletService.processMatchEntryFee(userId, matchId, match.entry_fee, 'NPR');
        }
      }

      // Create participant record
      const participantData = {
        match_id: matchId,
        user_id: userId,
        participant_type: participantType,
        joined_at: new Date().toISOString()
      };

      const { data: participant, error: participantError } = await this.supabase
        .from('match_participants')
        .insert([participantData])
        .select()
        .single();

      if (participantError) {
        // If participant creation fails and payment was processed, we need to refund
        if (participantType === 'player' && match.entry_fee && match.entry_fee > 0) {
          try {
            await this.refundEntryFee(userId, matchId, match.entry_fee);
          } catch (refundError) {
            logger.error(`Failed to refund entry fee for user ${userId}: ${refundError instanceof Error ? refundError.message : String(refundError)}`);
            logger.security(`CRITICAL: Payment processed but participant creation failed for user ${userId}, match ${matchId} - manual refund required`);
          }
        }
        throw new Error(`Failed to create participant record: ${participantError.message}`);
      }

      logger.info(`Atomic match join successful - User: ${userId}, Match: ${matchId}`);
      return participant;
    } catch (error) {
      logger.error(`Atomic match join failed for user ${userId}, match ${matchId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Refund entry fee in case of failed operations
   */
  private async refundEntryFee(userId: string, matchId: string, amount: number): Promise<void> {
    try {
      // Add refund transaction
      const { error } = await this.supabase
        .from('transactions')
        .insert([{
          user_id: userId,
          amount: amount,
          type: 'refund',
          description: `Refund for failed match join: ${matchId}`,
          status: 'completed',
          created_at: new Date().toISOString()
        }]);

      if (error) {
        throw new Error(`Failed to create refund transaction: ${error.message}`);
      }

      // Get current wallet balance and update it
      const { data: currentWallet, error: getWalletError } = await this.supabase
        .from('wallets')
        .select('balance')
        .eq('user_id', userId)
        .single();

      if (getWalletError) {
        throw new Error(`Failed to get current wallet balance: ${getWalletError.message}`);
      }

      // Update wallet balance
      const { error: walletError } = await this.supabase
        .from('wallets')
        .update({
          balance: currentWallet.balance + amount,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (walletError) {
        throw new Error(`Failed to update wallet balance: ${walletError.message}`);
      }

      logger.info(`Refunded ${amount} to user ${userId} for match ${matchId}`);
    } catch (error) {
      logger.error(`Refund failed for user ${userId}, match ${matchId}: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Validate user has sufficient balance before match operations
   */
  async validateSufficientBalance(userId: string, amount: number): Promise<boolean> {
    try {
      if (amount <= 0) return true;

      return await walletService.checkSufficientBalance(userId, amount);
    } catch (error) {
      logger.error(`Balance validation failed for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  /**
   * Get current wallet balance for user
   */
  async getCurrentBalance(userId: string): Promise<number> {
    try {
      return await walletService.getCurrentBalance(userId);
    } catch (error) {
      logger.error(`Failed to get balance for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
      return 0;
    }
  }

  /**
   * Rollback match creation if payment fails
   */
  async rollbackMatchCreation(matchId: string, reason: string): Promise<void> {
    try {
      logger.warn(`Rolling back match creation for match ${matchId}, reason: ${reason}`);

      // Delete match participants first
      await this.supabase
        .from('match_participants')
        .delete()
        .eq('match_id', matchId);

      // Delete the match
      await this.supabase
        .from('matches')
        .delete()
        .eq('id', matchId);

      logger.info(`Successfully rolled back match ${matchId}`);
    } catch (error) {
      logger.error(`Failed to rollback match ${matchId}: ${error instanceof Error ? error.message : String(error)}`);
      logger.security(`CRITICAL: Failed to rollback match ${matchId} - manual intervention required`);
    }
  }

  /**
   * Validate match creation data
   */
  private validateMatchData(matchData: CreateMatchData): void {
    if (!matchData.game_id || !matchData.match_time) {
      throw new Error('Missing required fields: game_id and match_time are required');
    }

    const matchTime = new Date(matchData.match_time);
    const now = new Date();
    if (matchTime <= now) {
      throw new Error('Scheduled start time must be in the future');
    }

    if (matchData.max_participants < 2 || matchData.max_participants > 100) {
      throw new Error('Max participants must be between 2 and 100');
    }

    if (matchData.entry_fee && matchData.entry_fee < 0) {
      throw new Error('Entry fee cannot be negative');
    }
  }
}

export const atomicMatchService = new AtomicMatchService();
