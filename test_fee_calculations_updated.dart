/**
 * Comprehensive test file for the updated WiggyZ wallet fee structure
 * Tests the new 2% transaction fee + 18% GST on fee calculation
 */

void main() {
  print('🧪 Testing Updated WiggyZ Wallet Fee Structure');
  print('=' * 60);
  
  testAddMoneyFeeCalculations();
  testWithdrawalFeeCalculations();
  testMinimumAmountValidations();
  testEdgeCases();
  testRealWorldScenarios();
  
  print('\n✅ All fee calculation tests passed!');
  print('The new fee structure is working correctly.');
}

void testAddMoneyFeeCalculations() {
  print('\n💰 Testing Add Money Fee Calculations');
  print('-' * 40);
  
  final testCases = [
    {'amount': 100, 'expectedFee': 2.0, 'expectedGST': 0.36, 'expectedNet': 97.64},
    {'amount': 500, 'expectedFee': 10.0, 'expectedGST': 1.8, 'expectedNet': 488.2},
    {'amount': 1000, 'expectedFee': 20.0, 'expectedGST': 3.6, 'expectedNet': 976.4},
    {'amount': 25, 'expectedFee': 0.5, 'expectedGST': 0.09, 'expectedNet': 24.41},
    {'amount': 50, 'expectedFee': 1.0, 'expectedGST': 0.18, 'expectedNet': 48.82},
  ];
  
  for (final testCase in testCases) {
    final amount = testCase['amount'] as int;
    final expectedFee = testCase['expectedFee'] as double;
    final expectedGST = testCase['expectedGST'] as double;
    final expectedNet = testCase['expectedNet'] as double;
    
    final result = calculateAddMoneyFees(amount.toDouble());
    
    print('Amount: ₹$amount');
    print('  Transaction Fee (2%): ₹${result.transactionFee.toStringAsFixed(2)} (expected: ₹${expectedFee.toStringAsFixed(2)})');
    print('  GST on Fee (18%): ₹${result.gstOnFee.toStringAsFixed(2)} (expected: ₹${expectedGST.toStringAsFixed(2)})');
    print('  Total Deductions: ₹${result.totalDeductions.toStringAsFixed(2)}');
    print('  Amount to Wallet: ₹${result.amountToWallet.toStringAsFixed(2)} (expected: ₹${expectedNet.toStringAsFixed(2)})');
    print('  Amount Charged: ₹${result.amountCharged.toStringAsFixed(2)}');
    
    // Verify calculations
    assert((result.transactionFee - expectedFee).abs() < 0.01, 'Transaction fee mismatch for ₹$amount');
    assert((result.gstOnFee - expectedGST).abs() < 0.01, 'GST calculation mismatch for ₹$amount');
    assert((result.amountToWallet - expectedNet).abs() < 0.01, 'Net amount mismatch for ₹$amount');
    assert(result.amountCharged == amount, 'Charged amount should equal input amount');
    
    print('  ✅ PASS\n');
  }
}

void testWithdrawalFeeCalculations() {
  print('\n💸 Testing Withdrawal Fee Calculations');
  print('-' * 40);
  
  final testCases = [
    {'amount': 100, 'expectedFee': 2.0, 'expectedGST': 0.36, 'expectedNet': 97.64},
    {'amount': 500, 'expectedFee': 10.0, 'expectedGST': 1.8, 'expectedNet': 488.2},
    {'amount': 1000, 'expectedFee': 20.0, 'expectedGST': 3.6, 'expectedNet': 976.4},
    {'amount': 200, 'expectedFee': 4.0, 'expectedGST': 0.72, 'expectedNet': 195.28},
  ];
  
  for (final testCase in testCases) {
    final amount = testCase['amount'] as int;
    final expectedFee = testCase['expectedFee'] as double;
    final expectedGST = testCase['expectedGST'] as double;
    final expectedNet = testCase['expectedNet'] as double;
    
    final result = calculateWithdrawalFees(amount.toDouble());
    
    print('Withdrawal Amount: ₹$amount');
    print('  Transaction Fee (2%): ₹${result.transactionFee.toStringAsFixed(2)} (expected: ₹${expectedFee.toStringAsFixed(2)})');
    print('  GST on Fee (18%): ₹${result.gstOnFee.toStringAsFixed(2)} (expected: ₹${expectedGST.toStringAsFixed(2)})');
    print('  Total Deductions: ₹${result.totalDeductions.toStringAsFixed(2)}');
    print('  Amount Deducted from Wallet: ₹${result.amountFromWallet.toStringAsFixed(2)}');
    print('  Amount Transferred to Bank: ₹${result.amountToBank.toStringAsFixed(2)} (expected: ₹${expectedNet.toStringAsFixed(2)})');
    
    // Verify calculations
    assert((result.transactionFee - expectedFee).abs() < 0.01, 'Transaction fee mismatch for ₹$amount');
    assert((result.gstOnFee - expectedGST).abs() < 0.01, 'GST calculation mismatch for ₹$amount');
    assert((result.amountToBank - expectedNet).abs() < 0.01, 'Net amount mismatch for ₹$amount');
    assert(result.amountFromWallet == amount, 'Wallet deduction should equal input amount');
    
    print('  ✅ PASS\n');
  }
}

void testMinimumAmountValidations() {
  print('\n🔍 Testing Minimum Amount Validations');
  print('-' * 40);
  
  // Test add money minimum (₹25)
  final addMoneyTestCases = [
    {'amount': 24, 'shouldPass': false},
    {'amount': 25, 'shouldPass': true, 'netAmount': 24.41},
    {'amount': 26, 'shouldPass': true},
    {'amount': 30, 'shouldPass': true, 'netAmount': 29.29},
  ];
  
  print('Add Money Validation (min ₹25):');
  for (final testCase in addMoneyTestCases) {
    final amount = testCase['amount'] as int;
    final shouldPass = testCase['shouldPass'] as bool;
    final isValid = amount >= 25;
    
    if (isValid && shouldPass) {
      final result = calculateAddMoneyFees(amount.toDouble());
      final netAmount = testCase['netAmount'] as double?;
      
      print('  ₹$amount: PASS (net: ₹${result.amountToWallet.toStringAsFixed(2)})');
      
      if (netAmount != null) {
        assert((result.amountToWallet - netAmount).abs() < 0.01, 'Net amount validation failed for ₹$amount');
      }
      
      // Ensure net amount is meaningful (at least ₹20)
      assert(result.amountToWallet >= 20, 'Net amount should be at least ₹20 for ₹$amount');
    } else {
      print('  ₹$amount: ${isValid ? 'PASS' : 'FAIL (below minimum)'}');
    }
    
    assert(isValid == shouldPass, 'Add money validation failed for ₹$amount');
  }
  
  // Test withdrawal minimum (₹100)
  final withdrawalTestCases = [
    {'amount': 99, 'shouldPass': false},
    {'amount': 100, 'shouldPass': true, 'netAmount': 97.64},
    {'amount': 150, 'shouldPass': true, 'netAmount': 146.46},
  ];
  
  print('\nWithdrawal Validation (min ₹100):');
  for (final testCase in withdrawalTestCases) {
    final amount = testCase['amount'] as int;
    final shouldPass = testCase['shouldPass'] as bool;
    final isValid = amount >= 100;
    
    if (isValid && shouldPass) {
      final result = calculateWithdrawalFees(amount.toDouble());
      final netAmount = testCase['netAmount'] as double?;
      
      print('  ₹$amount: PASS (net: ₹${result.amountToBank.toStringAsFixed(2)})');
      
      if (netAmount != null) {
        assert((result.amountToBank - netAmount).abs() < 0.01, 'Net amount validation failed for ₹$amount');
      }
      
      // Ensure net amount is meaningful (at least ₹80)
      assert(result.amountToBank >= 80, 'Net amount should be at least ₹80 for ₹$amount');
    } else {
      print('  ₹$amount: ${isValid ? 'PASS' : 'FAIL (below minimum)'}');
    }
    
    assert(isValid == shouldPass, 'Withdrawal validation failed for ₹$amount');
  }
  
  print('✅ Minimum amount validations are correct!');
}

void testEdgeCases() {
  print('\n🎯 Testing Edge Cases');
  print('-' * 25);
  
  // Test very small amounts
  final smallAmount = calculateAddMoneyFees(25.0);
  print('Minimum add money (₹25):');
  print('  Net amount: ₹${smallAmount.amountToWallet.toStringAsFixed(2)}');
  assert(smallAmount.amountToWallet > 20, 'Minimum add money should yield meaningful amount');
  
  // Test very large amounts
  final largeAmount = calculateAddMoneyFees(10000.0);
  print('\nLarge add money (₹10,000):');
  print('  Transaction fee: ₹${largeAmount.transactionFee.toStringAsFixed(2)}');
  print('  GST on fee: ₹${largeAmount.gstOnFee.toStringAsFixed(2)}');
  print('  Net amount: ₹${largeAmount.amountToWallet.toStringAsFixed(2)}');
  
  // Test decimal amounts
  final decimalAmount = calculateAddMoneyFees(99.99);
  print('\nDecimal amount (₹99.99):');
  print('  Net amount: ₹${decimalAmount.amountToWallet.toStringAsFixed(2)}');
  
  print('✅ Edge cases handled correctly!');
}

void testRealWorldScenarios() {
  print('\n🌍 Testing Real-World Scenarios');
  print('-' * 35);
  
  // Scenario 1: User wants to add ₹500 to wallet
  print('Scenario 1: User adds ₹500 to wallet');
  final addMoney = calculateAddMoneyFees(500.0);
  print('  User pays: ₹${addMoney.amountCharged.toStringAsFixed(2)}');
  print('  Wallet receives: ₹${addMoney.amountToWallet.toStringAsFixed(2)}');
  print('  Platform keeps: ₹${addMoney.totalDeductions.toStringAsFixed(2)} (fees)');
  
  // Scenario 2: User withdraws ₹1000 from wallet
  print('\nScenario 2: User withdraws ₹1000 from wallet');
  final withdrawal = calculateWithdrawalFees(1000.0);
  print('  Deducted from wallet: ₹${withdrawal.amountFromWallet.toStringAsFixed(2)}');
  print('  Transferred to bank: ₹${withdrawal.amountToBank.toStringAsFixed(2)}');
  print('  Platform keeps: ₹${withdrawal.totalDeductions.toStringAsFixed(2)} (fees)');
  
  // Scenario 3: Round-trip calculation
  print('\nScenario 3: Round-trip (add ₹1000, then withdraw ₹1000)');
  final addResult = calculateAddMoneyFees(1000.0);
  final withdrawResult = calculateWithdrawalFees(1000.0);
  final totalFees = addResult.totalDeductions + withdrawResult.totalDeductions;
  print('  Total fees for round-trip: ₹${totalFees.toStringAsFixed(2)}');
  print('  Net loss to user: ₹${totalFees.toStringAsFixed(2)}');
  
  print('✅ Real-world scenarios calculated correctly!');
}

// Fee calculation classes
class AddMoneyFeeResult {
  final double baseAmount;
  final double transactionFee;
  final double gstOnFee;
  final double totalDeductions;
  final double amountToWallet;
  final double amountCharged;
  
  AddMoneyFeeResult({
    required this.baseAmount,
    required this.transactionFee,
    required this.gstOnFee,
    required this.totalDeductions,
    required this.amountToWallet,
    required this.amountCharged,
  });
}

class WithdrawalFeeResult {
  final double withdrawalAmount;
  final double transactionFee;
  final double gstOnFee;
  final double totalDeductions;
  final double amountFromWallet;
  final double amountToBank;
  
  WithdrawalFeeResult({
    required this.withdrawalAmount,
    required this.transactionFee,
    required this.gstOnFee,
    required this.totalDeductions,
    required this.amountFromWallet,
    required this.amountToBank,
  });
}

// Fee calculation functions
AddMoneyFeeResult calculateAddMoneyFees(double baseAmount) {
  final transactionFee = baseAmount * 0.02; // 2% transaction fee
  final gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
  final totalDeductions = transactionFee + gstOnFee;
  final amountToWallet = baseAmount - totalDeductions; // Net amount credited to wallet
  final amountCharged = baseAmount; // User pays the amount they entered
  
  return AddMoneyFeeResult(
    baseAmount: baseAmount,
    transactionFee: transactionFee,
    gstOnFee: gstOnFee,
    totalDeductions: totalDeductions,
    amountToWallet: amountToWallet,
    amountCharged: amountCharged,
  );
}

WithdrawalFeeResult calculateWithdrawalFees(double withdrawalAmount) {
  final transactionFee = withdrawalAmount * 0.02; // 2% transaction fee
  final gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
  final totalDeductions = transactionFee + gstOnFee;
  final amountFromWallet = withdrawalAmount; // Full amount deducted from wallet
  final amountToBank = withdrawalAmount - totalDeductions; // Net amount transferred to bank
  
  return WithdrawalFeeResult(
    withdrawalAmount: withdrawalAmount,
    transactionFee: transactionFee,
    gstOnFee: gstOnFee,
    totalDeductions: totalDeductions,
    amountFromWallet: amountFromWallet,
    amountToBank: amountToBank,
  );
}
