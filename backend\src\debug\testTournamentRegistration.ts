import { supabase } from '../config/supabase';

/**
 * Test tournament registration with the new fallback logic
 */
async function testTournamentRegistration() {
  try {
    console.log('=== TESTING TOURNAMENT REGISTRATION ===');
    
    // Get a test tournament
    const { data: tournaments, error: tournamentsError } = await supabase
      .from('tournaments')
      .select('id, title, entry_fee, status')
      .limit(1);
    
    if (tournamentsError || !tournaments || tournaments.length === 0) {
      console.log('❌ No tournaments found for testing');
      return;
    }
    
    const testTournament = tournaments[0];
    console.log(`Using tournament: ${testTournament.title} (${testTournament.id})`);
    console.log(`Entry fee: ${testTournament.entry_fee}, Status: ${testTournament.status}`);
    
    // Test user ID
    const testUserId = '00000000-0000-0000-0000-000000000001';
    
    console.log('\n=== TESTING REGISTRATION INSERT ===');
    
    // Test the same logic as in the service
    let registrationResult: any;
    
    try {
      // First attempt with all expected columns
      const fullInsertData = {
        tournament_id: testTournament.id,
        user_id: testUserId,
        status: 'registered',
        participant_type: 'participant',
        joined_at: new Date().toISOString()
      };
      
      console.log('1. Attempting insert with all columns...');
      const { data: fullData, error: fullError } = await supabase
        .from('tournament_participants')
        .insert(fullInsertData)
        .select()
        .single();
      
      if (fullError) {
        console.log('❌ Full insert failed:', fullError.message);
        
        // If error is due to missing columns, try with basic fields only
        if (fullError.code === '42703' && (fullError.message.includes('participant_type') || fullError.message.includes('joined_at'))) {
          console.log('2. Missing columns detected, retrying with basic fields...');
          
          const basicInsertData = {
            tournament_id: testTournament.id,
            user_id: testUserId,
            status: 'registered',
            registration_date: new Date().toISOString() // Use existing column instead of joined_at
          };
          
          const { data: basicData, error: basicError } = await supabase
            .from('tournament_participants')
            .insert(basicInsertData)
            .select()
            .single();
          
          if (basicError) {
            console.log('❌ Basic insert also failed:', basicError.message);
            console.log('Error details:', {
              code: basicError.code,
              details: basicError.details,
              hint: basicError.hint
            });
            return false;
          }
          
          console.log('✅ Registration successful with basic fields!');
          registrationResult = basicData;
        } else {
          console.log('❌ Insert failed with different error:', fullError.message);
          return false;
        }
      } else {
        console.log('✅ Registration successful with all fields!');
        registrationResult = fullData;
      }
    } catch (insertError) {
      console.log('❌ Exception during registration:', insertError);
      return false;
    }
    
    if (registrationResult) {
      console.log('\n✅ TOURNAMENT REGISTRATION TEST PASSED!');
      console.log('Registration result:', registrationResult);
      
      // Clean up test record
      console.log('\nCleaning up test record...');
      await supabase
        .from('tournament_participants')
        .delete()
        .eq('id', registrationResult.id);
      console.log('✓ Test record cleaned up');
      
      return true;
    } else {
      console.log('\n❌ TOURNAMENT REGISTRATION TEST FAILED!');
      return false;
    }
    
  } catch (error) {
    console.error('Error in testTournamentRegistration:', error);
    return false;
  }
}

// Run the test
testTournamentRegistration().then((success) => {
  if (success) {
    console.log('\n🎉 Tournament registration is working! The fix should resolve the 500 error.');
  } else {
    console.log('\n❌ Tournament registration still has issues.');
  }
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});
