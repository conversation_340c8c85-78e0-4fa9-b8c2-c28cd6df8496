/**
 * Setup script for the enhanced referral system
 * Initializes database, settings, and validates the system
 */

import { supabase } from '../config/supabase';
import { logger } from '../utils/logger';
import ReferralSystemValidator from './validate-referral-system';

class ReferralSystemSetup {
  async runMigrations(): Promise<void> {
    logger.info('🔄 Running database migrations...');

    try {
      // Check if migrations have already been run
      const { data: existingTable, error } = await supabase
        .from('referral_points')
        .select('id')
        .limit(1);

      if (!error) {
        logger.info('✅ Database migrations already applied');
        return;
      }

      logger.info('📝 Migrations need to be applied manually. Please run:');
      logger.info('   psql -f backend/supabase/migrations/20250720000000_enhanced_referral_system.sql');
      logger.info('   psql -f backend/supabase/migrations/20250720000001_referral_functions.sql');
      
    } catch (error) {
      logger.error(`❌ Error checking migrations: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  async initializeSettings(): Promise<void> {
    logger.info('⚙️ Initializing referral settings...');

    const defaultSettings = [
      { key: 'referral_reward_points', value: '25', description: 'Points awarded to both referrer and referee' },
      { key: 'verification_required', value: 'true', description: 'Whether verification is required before rewards' },
      { key: 'minimum_activity_days', value: '7', description: 'Minimum days of activity before reward eligibility' },
      { key: 'cooldown_period_hours', value: '24', description: 'Hours between referral rewards for same user' },
      { key: 'max_referrals_per_ip_daily', value: '5', description: 'Maximum referrals allowed per IP per day' },
      { key: 'max_referrals_per_device_daily', value: '3', description: 'Maximum referrals allowed per device per day' },
      { key: 'fraud_threshold_score', value: '0.7', description: 'Fraud score threshold for automatic rejection' },
      { key: 'admin_review_threshold', value: '0.5', description: 'Fraud score threshold requiring admin review' }
    ];

    try {
      for (const setting of defaultSettings) {
        const { data: existing, error: checkError } = await supabase
          .from('referral_settings')
          .select('id')
          .eq('setting_key', setting.key)
          .single();

        if (checkError && checkError.code === 'PGRST116') {
          // Setting doesn't exist, create it
          const { error: insertError } = await supabase
            .from('referral_settings')
            .insert({
              setting_key: setting.key,
              setting_value: setting.value,
              description: setting.description
            });

          if (insertError) {
            logger.error(`❌ Failed to create setting ${setting.key}: ${insertError.message || String(insertError)}`);
          } else {
            logger.info(`✅ Created setting: ${setting.key} = ${setting.value}`);
          }
        } else if (!checkError) {
          logger.info(`✅ Setting already exists: ${setting.key}`);
        } else {
          logger.error(`❌ Error checking setting ${setting.key}: ${checkError.message || String(checkError)}`);
        }
      }
    } catch (error) {
      logger.error(`❌ Error initializing settings: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  async createTestData(): Promise<void> {
    logger.info('🧪 Creating test data...');

    try {
      // Create a test user if needed (for development only)
      const testUserId = 'test-user-123';
      
      // Initialize referral points for test user
      const { error: pointsError } = await supabase
        .from('referral_points')
        .upsert({
          user_id: testUserId,
          total_points: 50,
          earned_from_referrals: 50,
          used_for_matches: 0
        });

      if (pointsError) {
        logger.warn('⚠️ Could not create test referral points (user may not exist)');
      } else {
        logger.info('✅ Created test referral points data');
      }

      // Create test referral settings if they don't exist
      logger.info('✅ Test data creation completed');
      
    } catch (error) {
      logger.warn(`⚠️ Test data creation failed (this is normal in production): ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async validateSystem(): Promise<boolean> {
    logger.info('🔍 Validating system...');

    try {
      const validator = new ReferralSystemValidator();
      const results = await validator.runValidation();
      
      const failed = results.filter(r => r.status === 'fail').length;
      
      if (failed > 0) {
        logger.error('❌ System validation failed');
        return false;
      } else {
        logger.info('✅ System validation passed');
        return true;
      }
    } catch (error) {
      logger.error(`❌ Validation error: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  async setupIndexes(): Promise<void> {
    logger.info('📊 Setting up performance indexes...');

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_referral_points_user_id ON referral_points(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_referral_point_transactions_user_id ON referral_point_transactions(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_referral_point_transactions_created_at ON referral_point_transactions(created_at);',
      'CREATE INDEX IF NOT EXISTS idx_referral_fraud_detection_fraud_score ON referral_fraud_detection(fraud_score);',
      'CREATE INDEX IF NOT EXISTS idx_device_fingerprints_hash ON device_fingerprints(fingerprint_hash);',
      'CREATE INDEX IF NOT EXISTS idx_referral_ip_tracking_ip ON referral_ip_tracking(ip_address);'
    ];

    try {
      for (const indexSql of indexes) {
        const { error } = await supabase.rpc('exec_sql', { sql: indexSql });
        if (error) {
          logger.warn(`⚠️ Could not create index: ${error.message}`);
        }
      }
      logger.info('✅ Performance indexes setup completed');
    } catch (error) {
      logger.warn(`⚠️ Index creation failed (may require admin privileges): ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async runSetup(): Promise<void> {
    logger.info('🚀 Starting referral system setup...');

    try {
      await this.runMigrations();
      await this.initializeSettings();
      await this.setupIndexes();
      await this.createTestData();
      
      const isValid = await this.validateSystem();
      
      if (isValid) {
        logger.info('🎉 Referral system setup completed successfully!');
        logger.info('');
        logger.info('Next steps:');
        logger.info('1. Configure settings via admin dashboard');
        logger.info('2. Test referral processing with verification');
        logger.info('3. Monitor fraud detection analytics');
        logger.info('4. Set up alerts for admin review queue');
      } else {
        logger.error('❌ Setup completed with validation errors. Please fix issues before using the system.');
        process.exit(1);
      }
      
    } catch (error) {
      logger.error(`❌ Setup failed: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new ReferralSystemSetup();
  setup.runSetup()
    .then(() => {
      logger.info('✅ Setup script completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error(`❌ Setup script failed: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    });
}

export default ReferralSystemSetup;
