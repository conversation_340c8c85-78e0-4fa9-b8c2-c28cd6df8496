import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import '../models/referral_models.dart';
import 'referral_service.dart';
import 'auth_service.dart';

/// Service class for handling referral sharing functionality
class ReferralSharingService {
  final ReferralService _referralService;
  
  ReferralSharingService({ReferralService? referralService})
      : _referralService = referralService ?? ReferralService(AuthService());

  /// Share referral code and link through available sharing apps
  Future<ReferralShareResult> shareReferral({
    String? customMessage,
    String? subject,
  }) async {
    try {
      // Get the referral share content
      final shareContent = await _referralService.createShareContent(
        customMessage: customMessage,
      );

      // Check if sharing is available on this platform
      if (!await _isSharingAvailable()) {
        return ReferralShareResult.unavailable();
      }

      // Share the content using the basic share method
      await Share.share(shareContent.shareText);

      // Since Share.share doesn't return a result, we assume success
      return ReferralShareResult.success();
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing referral: $e');
      }
      return ReferralShareResult.error('Failed to share referral: ${e.toString()}');
    }
  }

  /// Share referral with specific text content
  Future<ReferralShareResult> shareReferralWithText(String text) async {
    try {
      if (!await _isSharingAvailable()) {
        return ReferralShareResult.unavailable();
      }

      await Share.share(text);

      // Since Share.share doesn't return a result, we assume success
      return ReferralShareResult.success();
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing referral text: $e');
      }
      return ReferralShareResult.error('Failed to share: ${e.toString()}');
    }
  }

  /// Copy referral code to clipboard as a fallback option
  Future<bool> copyReferralCodeToClipboard() async {
    try {
      final referralCode = await _referralService.getOrCreateReferralCode();
      await Clipboard.setData(ClipboardData(text: referralCode));
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error copying referral code to clipboard: $e');
      }
      return false;
    }
  }

  /// Copy full referral message to clipboard
  Future<bool> copyReferralMessageToClipboard({String? customMessage}) async {
    try {
      final shareContent = await _referralService.createShareContent(
        customMessage: customMessage,
      );
      await Clipboard.setData(ClipboardData(text: shareContent.shareText));
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error copying referral message to clipboard: $e');
      }
      return false;
    }
  }

  /// Get referral share content without sharing
  Future<ReferralShareContent> getReferralShareContent({String? customMessage}) async {
    return await _referralService.createShareContent(customMessage: customMessage);
  }

  /// Get just the referral code
  Future<String> getReferralCode() async {
    return await _referralService.getOrCreateReferralCode();
  }

  /// Check if sharing is available on the current platform
  Future<bool> _isSharingAvailable() async {
    try {
      // Share Plus supports iOS, Android, macOS, Windows, and Linux
      // But we'll do a basic check for mobile platforms
      return Platform.isAndroid || Platform.isIOS;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking sharing availability: $e');
      }
      return false;
    }
  }

  /// Share referral via specific platform (if supported)
  /// This is a placeholder for future platform-specific sharing
  Future<ReferralShareResult> shareToSpecificPlatform({
    required String platform,
    String? customMessage,
  }) async {
    try {
      // For now, we'll use the general share method
      // In the future, this could be extended to support platform-specific sharing
      return await shareReferral(customMessage: customMessage);
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing to specific platform: $e');
      }
      return ReferralShareResult.error('Failed to share to $platform: ${e.toString()}');
    }
  }

  /// Create a shareable referral message with custom formatting
  Future<String> createCustomReferralMessage({
    String? greeting,
    String? callToAction,
    bool includeEmojis = true,
    bool includeDownloadLinks = true,
  }) async {
    try {
      final referralCode = await _referralService.getOrCreateReferralCode();
      
      final emoji = includeEmojis ? '🎮' : '';
      final giftEmoji = includeEmojis ? '🎁' : '';
      final linkEmoji = includeEmojis ? '🔗' : '';
      final androidEmoji = includeEmojis ? '🤖' : '';
      final iosEmoji = includeEmojis ? '🍎' : '';
      
      final greetingText = greeting ?? 'Hey! Join me on WiggyZ';
      final actionText = callToAction ?? 'Use my referral code and we both get 25 points';
      
      var message = '$emoji $greetingText!\n\n'
          '$actionText: $referralCode $giftEmoji\n\n';
      
      if (includeDownloadLinks) {
        const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.wiggyz.app';
        const String appStoreUrl = 'https://apps.apple.com/app/wiggyz/id123456789';
        final String webUrl = 'https://wiggyz.com/refer/$referralCode';

        message += 'Download WiggyZ:\n'
            '$androidEmoji Android: $playStoreUrl\n'
            '$iosEmoji iOS: $appStoreUrl\n\n'
            '$linkEmoji Direct link: $webUrl';
      }
      
      return message;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating custom referral message: $e');
      }
      rethrow;
    }
  }

  /// Validate if the device supports sharing
  static bool get isShareSupported {
    try {
      return Platform.isAndroid || Platform.isIOS || Platform.isMacOS || Platform.isWindows;
    } catch (e) {
      return false;
    }
  }

  /// Get platform-specific sharing capabilities
  static Map<String, bool> get sharingCapabilities {
    return {
      'nativeShare': Platform.isAndroid || Platform.isIOS,
      'clipboard': true,
      'customApps': Platform.isAndroid || Platform.isIOS,
      'email': Platform.isAndroid || Platform.isIOS || Platform.isMacOS || Platform.isWindows,
      'sms': Platform.isAndroid || Platform.isIOS,
    };
  }

  /// Share via email (if supported)
  Future<ReferralShareResult> shareViaEmail({
    required String email,
    String? customMessage,
  }) async {
    try {
      final shareContent = await _referralService.createShareContent(
        customMessage: customMessage,
      );

      // Use Share.share with email-specific formatting
      final emailBody = shareContent.shareText;

      await Share.share(emailBody);

      // Since Share.share doesn't return a result, we assume success
      return ReferralShareResult.success(platform: 'email');
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing via email: $e');
      }
      return ReferralShareResult.error('Failed to share via email: ${e.toString()}');
    }
  }
}
