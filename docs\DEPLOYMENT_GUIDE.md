# WiggyZ Backend Deployment Guide

## Overview

This guide covers the complete deployment system for the WiggyZ backend to Vercel, including environment management and application configuration.

## Quick Start

### Prerequisites

- Node.js 18+ installed
- Vercel CLI installed (`npm install -g vercel`)
- Access to Vercel account with WiggyZ project

### Deploy to Production

```bash
cd backend
npm run deploy:full
```

### Deploy to Staging

```bash
cd backend
npm run deploy:full:staging
```

## Deployment System Components

### 1. Environment Management

The deployment system uses a centralized environment configuration:

- **Configuration File**: `config/environments.json`
- **Current Environment**: `config/current-environment.json`
- **Manager Script**: `scripts/deployment/environment-manager.js`

#### Available Environments

- **Development**: Local backend (http://127.0.0.1:8080)
- **Staging**: Vercel staging deployment
- **Production**: Vercel production deployment

#### Switch Environments

```bash
# Switch to production
npm run env:switch switch production

# Switch to development
npm run env:switch switch development

# Check current environment
npm run env:switch current
```

### 2. Deployment Scripts

#### Main Deployment Script
- **File**: `scripts/deployment/deploy.js`
- **Purpose**: Orchestrates full deployment process
- **Features**: Build, deploy, configure, validate

#### Environment Manager
- **File**: `scripts/deployment/environment-manager.js`
- **Purpose**: Manages environment configurations
- **Features**: Switch environments, update app configs

#### Validators
- **File**: `scripts/deployment/validators.js`
- **Purpose**: Pre/post deployment validation
- **Features**: Environment variables, connectivity, build validation

#### Vercel Config Generator
- **File**: `scripts/deployment/vercel-config.js`
- **Purpose**: Dynamic Vercel configuration generation
- **Features**: Environment-specific configs, region selection

### 3. Testing and Monitoring

#### Deployment Status Checker
```bash
npm run env:status
```

#### API Endpoint Testing
```bash
npm run test:api [url]
```

#### App Connectivity Testing
```bash
npm run test:connectivity
```

## Available NPM Scripts

### Deployment Scripts
- `npm run deploy` - Simple Vercel production deployment
- `npm run deploy:staging` - Simple Vercel staging deployment
- `npm run deploy:full` - Full deployment with configuration updates
- `npm run deploy:full:staging` - Full staging deployment

### Environment Management
- `npm run env:switch` - Switch between environments
- `npm run env:validate` - Validate environment configuration
- `npm run env:status` - Check deployment system status

### Configuration
- `npm run vercel:config` - Generate Vercel configuration

### Testing
- `npm run test:api` - Test API endpoints
- `npm run test:connectivity` - Test app connectivity

## Configuration Files

### Vercel Configuration (`backend/vercel.json`)

```json
{
  "version": 2,
  "name": "wiggyz-backend",
  "builds": [
    {
      "src": "dist/vercel.js",
      "use": "@vercel/node",
      "config": {
        "maxLambdaSize": "50mb",
        "maxDuration": 30
      }
    }
  ],
  "routes": [
    {
      "src": "/health",
      "dest": "/dist/vercel.js"
    },
    {
      "src": "/api/v1/(.*)",
      "dest": "/dist/vercel.js"
    },
    {
      "src": "/(.*)",
      "dest": "/dist/vercel.js"
    }
  ],
  "env": {
    "NODE_ENV": "production"
  },
  "regions": ["bom1"]
}
```

### Environment Configuration (`config/environments.json`)

```json
{
  "development": {
    "backend": {
      "url": "http://127.0.0.1:8080",
      "port": 8080,
      "protocol": "http"
    },
    "frontend": {
      "apiBaseUrl": "http://127.0.0.1:8080/api/v1"
    },
    "admin": {
      "apiBaseUrl": "http://127.0.0.1:8080/api/v1"
    },
    "environment": "development",
    "debug": true
  },
  "production": {
    "backend": {
      "url": "https://wiggyz-backend-dt566rys4-tausifraja977-gmailcoms-projects.vercel.app",
      "protocol": "https"
    },
    "frontend": {
      "apiBaseUrl": "https://wiggyz-backend-dt566rys4-tausifraja977-gmailcoms-projects.vercel.app/api/v1"
    },
    "admin": {
      "apiBaseUrl": "https://wiggyz-backend-dt566rys4-tausifraja977-gmailcoms-projects.vercel.app/api/v1"
    },
    "environment": "production",
    "debug": false
  }
}
```

## Serverless Considerations

The backend is optimized for Vercel serverless deployment:

### What's Different in Serverless Mode

1. **No Background Services**: Match deadline monitoring and other background services are disabled
2. **No Persistent Connections**: Redis connections are disabled by default
3. **Stateless Functions**: Each request is handled independently
4. **Cold Starts**: First request may be slower due to function initialization

### Serverless Entry Point

The `src/vercel.ts` file provides a serverless-compatible version of the app without:
- Background service initialization
- Persistent Redis connections
- Process event listeners

## Troubleshooting

### Common Issues

#### 1. Authentication Required Error
If you see "Authentication Required" when accessing the deployed URL, this is normal for Vercel deployments. The API endpoints will work correctly from the Flutter and Admin apps.

#### 2. Build Failures
```bash
# Check build status
npm run env:status

# Rebuild
npm run build

# Validate configuration
npm run env:validate
```

#### 3. Environment Configuration Issues
```bash
# Check current environment
npm run env:switch current

# Validate configuration
npm run env:validate

# Reset to development
npm run env:switch switch development
```

### Logs and Debugging

- **Deployment Logs**: Saved to `deployment-logs/` directory
- **Validation Reports**: Generated during validation runs
- **Vercel Logs**: Available in Vercel dashboard

## Best Practices

1. **Always validate before deployment**:
   ```bash
   npm run env:validate
   ```

2. **Test after deployment**:
   ```bash
   npm run test:api
   npm run test:connectivity
   ```

3. **Check deployment status**:
   ```bash
   npm run env:status
   ```

4. **Use staging for testing**:
   ```bash
   npm run deploy:full:staging
   ```

## Support

For deployment issues:
1. Check deployment status: `npm run env:status`
2. Validate configuration: `npm run env:validate`
3. Review deployment logs in `deployment-logs/` directory
4. Check Vercel dashboard for function logs
