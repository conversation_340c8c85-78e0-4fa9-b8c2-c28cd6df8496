import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../core/api/api_config.dart';
import '../models/game_model.dart';

/// Service for managing games data from the API
class GamesService {
  static const String _cacheKey = 'games_cache';
  static const String _cacheTimestampKey = 'games_cache_timestamp';
  static const Duration _cacheExpiry = Duration(hours: 1);

  /// Get all games with optional filtering
  Future<List<Game>> getAllGames({
    String? category,
    bool? featured,
    bool forceRefresh = false,
  }) async {
    try {
      // Check cache first unless force refresh is requested
      if (!forceRefresh) {
        final cachedGames = await _getCachedGames();
        if (cachedGames != null) {
          debugPrint('Returning cached games');
          return _filterGames(
            cachedGames,
            category: category,
            featured: featured,
          );
        }
      }

      // Build query parameters
      final queryParams = <String, String>{};
      if (category != null) queryParams['category'] = category;
      if (featured != null) queryParams['featured'] = featured.toString();

      final uri = Uri.parse(
        '${ApiConfig.baseUrl}/games',
      ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final gamesList = data['data'] as List;
          final games =
              gamesList.map((item) => _mapApiGameToModel(item)).toList();

          // Cache the result
          await _cacheGames(games);

          debugPrint('Successfully fetched ${games.length} games from API');
          return games;
        }
      } else {
        debugPrint('Failed to fetch games: ${response.statusCode}');
        debugPrint('Response: ${response.body}');
      }

      // Return fallback data if API fails
      return _getFallbackGames();
    } catch (e) {
      debugPrint('Error fetching games: $e');
      return _getFallbackGames();
    }
  }

  /// Get featured games
  Future<List<Game>> getFeaturedGames({bool forceRefresh = false}) async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/games/featured'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final gamesList = data['data'] as List;
          final games =
              gamesList.map((item) => _mapApiGameToModel(item)).toList();

          debugPrint('Successfully fetched ${games.length} featured games');
          return games;
        }
      } else {
        debugPrint('Failed to fetch featured games: ${response.statusCode}');
      }

      // Return fallback featured games
      return _getFallbackGames()
          .where((game) => game.rating != null && game.rating! >= 4.5)
          .toList();
    } catch (e) {
      debugPrint('Error fetching featured games: $e');
      return _getFallbackGames()
          .where((game) => game.rating != null && game.rating! >= 4.5)
          .toList();
    }
  }

  /// Get popular games (by player count)
  Future<List<Game>> getPopularGames({
    int limit = 10,
    bool forceRefresh = false,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/games/popular?limit=$limit'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final gamesList = data['data'] as List;
          final games =
              gamesList.map((item) => _mapApiGameToModel(item)).toList();

          debugPrint('Successfully fetched ${games.length} popular games');
          return games;
        }
      } else {
        debugPrint('Failed to fetch popular games: ${response.statusCode}');
      }

      // Return fallback popular games
      final fallbackGames = _getFallbackGames();
      fallbackGames.sort(
        (a, b) => (b.playersOnline ?? 0).compareTo(a.playersOnline ?? 0),
      );
      return fallbackGames.take(limit).toList();
    } catch (e) {
      debugPrint('Error fetching popular games: $e');
      final fallbackGames = _getFallbackGames();
      fallbackGames.sort(
        (a, b) => (b.playersOnline ?? 0).compareTo(a.playersOnline ?? 0),
      );
      return fallbackGames.take(limit).toList();
    }
  }

  /// Get games by category
  Future<List<Game>> getGamesByCategory(String category) async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/games/category/$category'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final gamesList = data['data'] as List;
          final games =
              gamesList.map((item) => _mapApiGameToModel(item)).toList();

          debugPrint(
            'Successfully fetched ${games.length} games for category: $category',
          );
          return games;
        }
      } else {
        debugPrint('Failed to fetch games by category: ${response.statusCode}');
      }

      // Return fallback games filtered by category
      return _getFallbackGames(); // Note: fallback doesn't have category filtering
    } catch (e) {
      debugPrint('Error fetching games by category: $e');
      return _getFallbackGames();
    }
  }

  /// Search games by name
  Future<List<Game>> searchGames(String searchTerm, {int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse(
          '${ApiConfig.baseUrl}/games/search?q=${Uri.encodeComponent(searchTerm)}&limit=$limit',
        ),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final gamesList = data['data'] as List;
          final games =
              gamesList.map((item) => _mapApiGameToModel(item)).toList();

          debugPrint(
            'Successfully searched games: ${games.length} results for "$searchTerm"',
          );
          return games;
        }
      } else {
        debugPrint('Failed to search games: ${response.statusCode}');
      }

      // Return fallback search results
      final fallbackGames = _getFallbackGames();
      return fallbackGames
          .where(
            (game) =>
                game.name.toLowerCase().contains(searchTerm.toLowerCase()),
          )
          .take(limit)
          .toList();
    } catch (e) {
      debugPrint('Error searching games: $e');
      final fallbackGames = _getFallbackGames();
      return fallbackGames
          .where(
            (game) =>
                game.name.toLowerCase().contains(searchTerm.toLowerCase()),
          )
          .take(limit)
          .toList();
    }
  }

  /// Get game categories
  Future<List<String>> getGameCategories() async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/games/categories'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final categories = List<String>.from(data['data']);
          debugPrint(
            'Successfully fetched ${categories.length} game categories',
          );
          return categories;
        }
      } else {
        debugPrint('Failed to fetch game categories: ${response.statusCode}');
      }

      // Return fallback categories
      return ['Battle Royale', 'FPS', 'Strategy', 'Sports', 'Sandbox'];
    } catch (e) {
      debugPrint('Error fetching game categories: $e');
      return ['Battle Royale', 'FPS', 'Strategy', 'Sports', 'Sandbox'];
    }
  }

  /// Map API game data to Game model
  Game _mapApiGameToModel(Map<String, dynamic> apiGame) {
    return Game(
      id: apiGame['id']?.toString() ?? '',
      name: apiGame['name'] ?? 'Unknown Game',
      icon: _getIconForGame(apiGame['name'] ?? ''),
      imageUrl: apiGame['imageUrl'] ?? apiGame['image_url'],
      bannerImageUrl:
          apiGame['imageUrl'] ??
          apiGame['image_url'], // Use same image for banner
      description: apiGame['description'] ?? '',
      playersOnline: apiGame['playersOnline'] ?? apiGame['players_online'] ?? 0,
      totalDownloads:
          apiGame['totalDownloads'] ?? apiGame['total_downloads'] ?? '0',
      rating: 4.5, // Default rating since API doesn't provide this
    );
  }

  /// Get appropriate icon for game name
  IconData _getIconForGame(String gameName) {
    // This is a simple mapping - in a real app you might want to store icons in the API
    final name = gameName.toLowerCase();
    if (name.contains('fortnite') ||
        name.contains('pubg') ||
        name.contains('free fire')) {
      return Icons.sports_esports;
    } else if (name.contains('valorant') ||
        name.contains('cod') ||
        name.contains('call of duty')) {
      return Icons.gps_fixed;
    } else if (name.contains('clash') || name.contains('strategy')) {
      return Icons.castle;
    } else if (name.contains('minecraft')) {
      return Icons.view_in_ar;
    } else {
      return Icons.gamepad;
    }
  }

  /// Filter games locally
  List<Game> _filterGames(
    List<Game> games, {
    String? category,
    bool? featured,
  }) {
    var filtered = games;

    if (featured == true) {
      // Filter for high-rated games as "featured"
      filtered =
          filtered
              .where((game) => game.rating != null && game.rating! >= 4.5)
              .toList();
    }

    // Note: Category filtering would need to be implemented if categories are added to the Game model

    return filtered;
  }

  /// Cache games locally
  Future<void> _cacheGames(List<Game> games) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final gamesJson = json.encode(
        games
            .map(
              (game) => {
                'id': game.id,
                'name': game.name,
                'imageUrl': game.imageUrl,
                'bannerImageUrl': game.bannerImageUrl,
                'description': game.description,
                'playersOnline': game.playersOnline,
                'totalDownloads': game.totalDownloads,
                'rating': game.rating,
              },
            )
            .toList(),
      );
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await prefs.setString(_cacheKey, gamesJson);
      await prefs.setInt(_cacheTimestampKey, timestamp);

      debugPrint('Cached ${games.length} games');
    } catch (e) {
      debugPrint('Error caching games: $e');
    }
  }

  /// Get cached games if not expired
  Future<List<Game>?> _getCachedGames() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final gamesJson = prefs.getString(_cacheKey);
      final timestamp = prefs.getInt(_cacheTimestampKey);

      if (gamesJson != null && timestamp != null) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - timestamp;
        final cacheExpired = cacheAge > _cacheExpiry.inMilliseconds;

        if (!cacheExpired) {
          final gamesList = json.decode(gamesJson) as List;
          return gamesList
              .map(
                (item) => Game(
                  id: item['id'] ?? '',
                  name: item['name'] ?? '',
                  icon: _getIconForGame(item['name'] ?? ''),
                  imageUrl: item['imageUrl'],
                  bannerImageUrl: item['bannerImageUrl'],
                  description: item['description'],
                  playersOnline: item['playersOnline'],
                  totalDownloads: item['totalDownloads'],
                  rating: item['rating']?.toDouble(),
                ),
              )
              .toList();
        } else {
          debugPrint('Games cache expired');
          await _clearCache();
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error reading cached games: $e');
      return null;
    }
  }

  /// Clear cached games
  Future<void> _clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimestampKey);
      debugPrint('Cleared games cache');
    } catch (e) {
      debugPrint('Error clearing games cache: $e');
    }
  }

  /// Get fallback games data (same as current hardcoded data)
  List<Game> _getFallbackGames() {
    return Game.getAllGames(); // Use existing static method as fallback
  }

  /// Clear all cached data (for logout)
  Future<void> clearAllCache() async {
    await _clearCache();
  }
}
