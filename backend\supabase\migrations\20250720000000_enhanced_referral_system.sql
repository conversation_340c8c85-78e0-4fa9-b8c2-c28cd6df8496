-- Enhanced Referral System Migration
-- Date: 2025-07-20
-- Description: Comprehensive referral system with fraud detection and non-withdrawable points

-- ==================== REFERRAL POINTS SYSTEM ====================

-- Referral Points Table - Non-withdrawable points for match entries only
CREATE TABLE IF NOT EXISTS referral_points (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  total_points INTEGER NOT NULL DEFAULT 0,
  earned_from_referrals INTEGER DEFAULT 0,
  used_for_matches INTEGER DEFAULT 0,
  available_points INTEGER GENERATED ALWAYS AS (total_points - used_for_matches) STORED,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Referral Points Transactions - Track all referral point movements
CREATE TABLE IF NOT EXISTS referral_point_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('earned', 'used', 'expired', 'admin_adjustment')),
  points INTEGER NOT NULL,
  source_type VARCHAR(30) NOT NULL CHECK (source_type IN ('referral_reward', 'match_entry', 'admin_adjustment', 'expiration')),
  source_id UUID, -- Reference to referral, match, or admin action
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'
);

-- ==================== FRAUD DETECTION SYSTEM ====================

-- Referral Fraud Detection Table
CREATE TABLE IF NOT EXISTS referral_fraud_detection (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  referral_id UUID REFERENCES user_referrals(id) ON DELETE CASCADE,
  device_fingerprint VARCHAR(255),
  ip_address INET,
  user_agent TEXT,
  geolocation JSONB, -- {country, region, city, lat, lng}
  fraud_score DECIMAL(5,2) DEFAULT 0.0,
  risk_factors JSONB DEFAULT '[]', -- Array of detected risk factors
  ml_prediction JSONB, -- ML model output
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'flagged')),
  admin_reviewed BOOLEAN DEFAULT FALSE,
  admin_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Device Fingerprint Tracking
CREATE TABLE IF NOT EXISTS device_fingerprints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  fingerprint_hash VARCHAR(255) UNIQUE NOT NULL,
  user_count INTEGER DEFAULT 1,
  first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_suspicious BOOLEAN DEFAULT FALSE,
  metadata JSONB DEFAULT '{}' -- Device details, browser info, etc.
);

-- IP Address Tracking for Referrals
CREATE TABLE IF NOT EXISTS referral_ip_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ip_address INET NOT NULL,
  referral_count INTEGER DEFAULT 1,
  user_count INTEGER DEFAULT 1,
  first_referral TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_referral TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_blocked BOOLEAN DEFAULT FALSE,
  risk_score DECIMAL(3,2) DEFAULT 0.0,
  metadata JSONB DEFAULT '{}'
);

-- ==================== REFERRAL SETTINGS & CONFIGURATION ====================

-- Referral System Settings
CREATE TABLE IF NOT EXISTS referral_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  setting_key VARCHAR(50) UNIQUE NOT NULL,
  setting_value JSONB NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default referral settings
INSERT INTO referral_settings (setting_key, setting_value, description) VALUES
('referral_reward_points', '25', 'Points awarded to both referrer and referee'),
('verification_required', 'true', 'Whether verification is required before rewards'),
('minimum_activity_days', '7', 'Minimum days of activity before reward eligibility'),
('cooldown_period_hours', '24', 'Hours between referral rewards for same user'),
('max_referrals_per_ip_daily', '5', 'Maximum referrals allowed per IP per day'),
('max_referrals_per_device_daily', '3', 'Maximum referrals allowed per device per day'),
('fraud_threshold_score', '0.7', 'Fraud score threshold for automatic rejection'),
('admin_review_threshold', '0.5', 'Fraud score threshold requiring admin review')
ON CONFLICT (setting_key) DO NOTHING;

-- ==================== ENHANCE EXISTING TABLES ====================

-- Add fraud detection and verification fields to user_referrals
ALTER TABLE user_referrals 
ADD COLUMN IF NOT EXISTS verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'failed')),
ADD COLUMN IF NOT EXISTS device_fingerprint VARCHAR(255),
ADD COLUMN IF NOT EXISTS ip_address INET,
ADD COLUMN IF NOT EXISTS fraud_score DECIMAL(5,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS admin_approved BOOLEAN DEFAULT NULL,
ADD COLUMN IF NOT EXISTS minimum_activity_met BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS reward_points_awarded INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS referrer_points_awarded INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS cooldown_expires_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS fraud_detection_id UUID REFERENCES referral_fraud_detection(id),
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'flagged', 'approved', 'rejected'));

-- Add verification tracking to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS phone_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS account_activity_score INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS first_activity_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS referral_eligible BOOLEAN DEFAULT FALSE;

-- ==================== INDEXES FOR PERFORMANCE ====================

-- Referral Points indexes
CREATE INDEX IF NOT EXISTS idx_referral_points_user_id ON referral_points(user_id);
CREATE INDEX IF NOT EXISTS idx_referral_point_transactions_user_id ON referral_point_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_referral_point_transactions_type ON referral_point_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_referral_point_transactions_created_at ON referral_point_transactions(created_at);

-- Fraud Detection indexes
CREATE INDEX IF NOT EXISTS idx_referral_fraud_detection_referral_id ON referral_fraud_detection(referral_id);
CREATE INDEX IF NOT EXISTS idx_referral_fraud_detection_status ON referral_fraud_detection(status);
CREATE INDEX IF NOT EXISTS idx_referral_fraud_detection_fraud_score ON referral_fraud_detection(fraud_score);
CREATE INDEX IF NOT EXISTS idx_device_fingerprints_hash ON device_fingerprints(fingerprint_hash);
CREATE INDEX IF NOT EXISTS idx_referral_ip_tracking_ip ON referral_ip_tracking(ip_address);

-- Enhanced user_referrals indexes
CREATE INDEX IF NOT EXISTS idx_user_referrals_verification_status ON user_referrals(verification_status);
CREATE INDEX IF NOT EXISTS idx_user_referrals_fraud_score ON user_referrals(fraud_score);
CREATE INDEX IF NOT EXISTS idx_user_referrals_device_fingerprint ON user_referrals(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_user_referrals_ip_address ON user_referrals(ip_address);
CREATE INDEX IF NOT EXISTS idx_user_referrals_status ON user_referrals(status);

-- User verification indexes
CREATE INDEX IF NOT EXISTS idx_users_referral_eligible ON users(referral_eligible);
CREATE INDEX IF NOT EXISTS idx_users_verification_status ON users(phone_verified, email_verified);

-- ==================== ROW LEVEL SECURITY ====================

-- Enable RLS on new tables
ALTER TABLE referral_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_point_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_fraud_detection ENABLE ROW LEVEL SECURITY;
ALTER TABLE device_fingerprints ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_ip_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies for referral_points
CREATE POLICY "Users can view their own referral points" 
  ON referral_points FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own referral points" 
  ON referral_points FOR UPDATE 
  USING (auth.uid() = user_id);

-- RLS Policies for referral_point_transactions
CREATE POLICY "Users can view their own referral point transactions" 
  ON referral_point_transactions FOR SELECT 
  USING (auth.uid() = user_id);

-- Admin policies for fraud detection tables
CREATE POLICY "Admins can view all fraud detection data" 
  ON referral_fraud_detection FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'manager')
    )
  );

CREATE POLICY "Admins can view all referral settings" 
  ON referral_settings FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'manager')
    )
  );

-- ==================== TRIGGERS ====================

-- Update timestamp trigger for referral_points
CREATE OR REPLACE FUNCTION update_referral_points_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_referral_points_updated_at
  BEFORE UPDATE ON referral_points
  FOR EACH ROW
  EXECUTE FUNCTION update_referral_points_updated_at();

-- Update timestamp trigger for referral_fraud_detection
CREATE TRIGGER trigger_update_referral_fraud_detection_updated_at
  BEFORE UPDATE ON referral_fraud_detection
  FOR EACH ROW
  EXECUTE FUNCTION update_referral_points_updated_at();

-- Update timestamp trigger for referral_settings
CREATE TRIGGER trigger_update_referral_settings_updated_at
  BEFORE UPDATE ON referral_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_referral_points_updated_at();
