/**
 * Support System Validation Schemas
 * Joi validation schemas for support-related API endpoints
 */

import Jo<PERSON> from 'joi';

// Support Message Schemas
export const createSupportMessageSchema = Joi.object({
  subject: Joi.string()
    .min(5)
    .max(200)
    .required()
    .messages({
      'string.min': 'Subject must be at least 5 characters long',
      'string.max': 'Subject cannot exceed 200 characters',
      'any.required': 'Subject is required'
    }),
  
  category: Joi.string()
    .valid('bug_report', 'feature_request', 'general_inquiry', 'account_issue', 'payment_issue', 'technical_support')
    .required()
    .messages({
      'any.only': 'Category must be one of: bug_report, feature_request, general_inquiry, account_issue, payment_issue, technical_support',
      'any.required': 'Category is required'
    }),
  
  message: Joi.string()
    .min(10)
    .max(2000)
    .required()
    .messages({
      'string.min': 'Message must be at least 10 characters long',
      'string.max': 'Message cannot exceed 2000 characters',
      'any.required': 'Message is required'
    })
});

export const supportMessageReplySchema = Joi.object({
  reply_text: Joi.string()
    .min(5)
    .max(2000)
    .required()
    .messages({
      'string.min': 'Reply must be at least 5 characters long',
      'string.max': 'Reply cannot exceed 2000 characters',
      'any.required': 'Reply text is required'
    }),
  
  is_internal: Joi.boolean()
    .default(false)
    .messages({
      'boolean.base': 'is_internal must be a boolean value'
    })
});

export const updateSupportMessageSchema = Joi.object({
  status: Joi.string()
    .valid('new', 'in_progress', 'resolved', 'closed')
    .optional()
    .messages({
      'any.only': 'Status must be one of: new, in_progress, resolved, closed'
    }),
  
  priority: Joi.string()
    .valid('low', 'normal', 'high', 'urgent')
    .optional()
    .messages({
      'any.only': 'Priority must be one of: low, normal, high, urgent'
    }),
  
  admin_notes: Joi.string()
    .max(1000)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Admin notes cannot exceed 1000 characters'
    }),
  
  assigned_to: Joi.string()
    .uuid()
    .optional()
    .allow(null)
    .messages({
      'string.uuid': 'assigned_to must be a valid UUID'
    })
});

export const updateSupportMessageStatusSchema = Joi.object({
  status: Joi.string()
    .valid('new', 'in_progress', 'resolved', 'closed')
    .required()
    .messages({
      'any.only': 'Status must be one of: new, in_progress, resolved, closed',
      'any.required': 'Status is required'
    })
});

export const supportMessageFiltersSchema = Joi.object({
  status: Joi.string()
    .valid('new', 'in_progress', 'resolved', 'closed')
    .optional(),
  
  category: Joi.string()
    .valid('bug_report', 'feature_request', 'general_inquiry', 'account_issue', 'payment_issue', 'technical_support')
    .optional(),
  
  priority: Joi.string()
    .valid('low', 'normal', 'high', 'urgent')
    .optional(),
  
  assigned_to: Joi.string()
    .uuid()
    .optional(),
  
  created_after: Joi.date()
    .iso()
    .optional(),
  
  created_before: Joi.date()
    .iso()
    .optional(),
  
  page: Joi.number()
    .integer()
    .min(1)
    .default(1),
  
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20),
  
  search: Joi.string()
    .max(100)
    .optional()
});

// Chat System Schemas
export const createChatSessionSchema = Joi.object({
  session_type: Joi.string()
    .valid('support', 'ai_bot')
    .default('support')
    .messages({
      'any.only': 'Session type must be either support or ai_bot'
    })
});

export const sendChatMessageSchema = Joi.object({
  message_text: Joi.string()
    .min(1)
    .max(1000)
    .required()
    .messages({
      'string.min': 'Message cannot be empty',
      'string.max': 'Message cannot exceed 1000 characters',
      'any.required': 'Message text is required'
    }),
  
  message_type: Joi.string()
    .valid('text', 'image', 'file', 'system')
    .default('text')
    .messages({
      'any.only': 'Message type must be one of: text, image, file, system'
    }),
  
  metadata: Joi.object()
    .optional()
    .default({})
});

export const chatSessionFiltersSchema = Joi.object({
  status: Joi.string()
    .valid('active', 'waiting', 'closed', 'transferred')
    .optional(),
  
  session_type: Joi.string()
    .valid('support', 'ai_bot')
    .optional(),
  
  admin_user_id: Joi.string()
    .uuid()
    .optional(),
  
  started_after: Joi.date()
    .iso()
    .optional(),
  
  started_before: Joi.date()
    .iso()
    .optional(),
  
  page: Joi.number()
    .integer()
    .min(1)
    .default(1),
  
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20)
});

// FAQ Schemas
export const faqFeedbackSchema = Joi.object({
  is_helpful: Joi.boolean()
    .required()
    .messages({
      'boolean.base': 'is_helpful must be a boolean value',
      'any.required': 'is_helpful is required'
    }),
  
  feedback_text: Joi.string()
    .max(500)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Feedback text cannot exceed 500 characters'
    })
});

export const createFaqCategorySchema = Joi.object({
  name: Joi.string()
    .min(3)
    .max(100)
    .required()
    .messages({
      'string.min': 'Category name must be at least 3 characters long',
      'string.max': 'Category name cannot exceed 100 characters',
      'any.required': 'Category name is required'
    }),
  
  description: Joi.string()
    .max(500)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Description cannot exceed 500 characters'
    }),
  
  display_order: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.integer': 'Display order must be an integer',
      'number.min': 'Display order cannot be negative'
    })
});

export const updateFaqCategorySchema = Joi.object({
  name: Joi.string()
    .min(3)
    .max(100)
    .optional(),
  
  description: Joi.string()
    .max(500)
    .optional()
    .allow(''),
  
  display_order: Joi.number()
    .integer()
    .min(0)
    .optional(),
  
  is_active: Joi.boolean()
    .optional()
});

export const createFaqItemSchema = Joi.object({
  category_id: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': 'Category ID must be a valid UUID',
      'any.required': 'Category ID is required'
    }),
  
  question: Joi.string()
    .min(10)
    .max(500)
    .required()
    .messages({
      'string.min': 'Question must be at least 10 characters long',
      'string.max': 'Question cannot exceed 500 characters',
      'any.required': 'Question is required'
    }),
  
  answer: Joi.string()
    .min(10)
    .max(2000)
    .required()
    .messages({
      'string.min': 'Answer must be at least 10 characters long',
      'string.max': 'Answer cannot exceed 2000 characters',
      'any.required': 'Answer is required'
    }),
  
  display_order: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.integer': 'Display order must be an integer',
      'number.min': 'Display order cannot be negative'
    })
});

export const updateFaqItemSchema = Joi.object({
  category_id: Joi.string()
    .uuid()
    .optional(),
  
  question: Joi.string()
    .min(10)
    .max(500)
    .optional(),
  
  answer: Joi.string()
    .min(10)
    .max(2000)
    .optional(),
  
  display_order: Joi.number()
    .integer()
    .min(0)
    .optional(),
  
  is_active: Joi.boolean()
    .optional()
});
