/**
 * Admin withdrawal management controller
 * Handles admin operations for withdrawal requests
 */
import { Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import { walletService } from '../services/walletService';
import { successResponse, errorResponse } from '../../../utils/responseFormatter';

/**
 * Get all withdrawal requests for admin dashboard
 * @route GET /api/v1/admin/withdrawals
 */
export const getWithdrawalRequests = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super admin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    // Validate query parameters
    const schema = Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      status: Joi.string().valid('pending', 'approved', 'completed', 'rejected').optional()
    });

    const { error, value } = schema.validate(req.query);
    if (error) {
      return res.status(400).json(errorResponse(error.details[0].message));
    }

    const { page, limit, status } = value;

    // Get withdrawal requests
    const result = await walletService.getWithdrawalRequests(page, limit, status);
    
    return res.status(200).json(successResponse(result, 'Withdrawal requests retrieved successfully'));
  } catch (err: any) {
    console.error('Error fetching withdrawal requests:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Get a specific withdrawal request by ID
 * @route GET /api/v1/admin/withdrawals/:id
 */
export const getWithdrawalRequest = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super admin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    const { id } = req.params;

    if (!id) {
      return res.status(400).json(errorResponse('Withdrawal request ID is required'));
    }

    // Get withdrawal request
    const request = await walletService.getWithdrawalRequest(id);
    
    return res.status(200).json(successResponse(request, 'Withdrawal request retrieved successfully'));
  } catch (err: any) {
    console.error('Error fetching withdrawal request:', err);
    
    if (err.message === 'Withdrawal request not found') {
      return res.status(404).json(errorResponse('Withdrawal request not found'));
    }
    
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Approve a withdrawal request
 * @route POST /api/v1/admin/withdrawals/:id/approve
 */
export const approveWithdrawalRequest = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super admin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    const { id } = req.params;

    if (!id) {
      return res.status(400).json(errorResponse('Withdrawal request ID is required'));
    }

    // Validate request body
    const schema = Joi.object({
      admin_notes: Joi.string().max(500).optional()
    });

    const { error, value } = schema.validate(req.body);
    if (error) {
      return res.status(400).json(errorResponse(error.details[0].message));
    }

    const { admin_notes } = value;

    // Approve withdrawal request
    const updatedRequest = await walletService.approveWithdrawalRequest(
      id, 
      req.user.userId, 
      admin_notes
    );
    
    return res.status(200).json(successResponse(updatedRequest, 'Withdrawal request approved successfully'));
  } catch (err: any) {
    console.error('Error approving withdrawal request:', err);
    
    if (err.message.includes('not found') || err.message.includes('not in pending status')) {
      return res.status(404).json(errorResponse(err.message));
    }
    
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Reject a withdrawal request
 * @route POST /api/v1/admin/withdrawals/:id/reject
 */
export const rejectWithdrawalRequest = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super admin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    const { id } = req.params;

    if (!id) {
      return res.status(400).json(errorResponse('Withdrawal request ID is required'));
    }

    // Validate request body
    const schema = Joi.object({
      admin_notes: Joi.string().min(1).max(500).required()
    });

    const { error, value } = schema.validate(req.body);
    if (error) {
      return res.status(400).json(errorResponse(error.details[0].message));
    }

    const { admin_notes } = value;

    // Reject withdrawal request
    const updatedRequest = await walletService.rejectWithdrawalRequest(
      id, 
      req.user.userId, 
      admin_notes
    );
    
    return res.status(200).json(successResponse(updatedRequest, 'Withdrawal request rejected successfully'));
  } catch (err: any) {
    console.error('Error rejecting withdrawal request:', err);
    
    if (err.message.includes('not found') || err.message.includes('not in pending status')) {
      return res.status(404).json(errorResponse(err.message));
    }
    
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Mark a withdrawal request as completed
 * @route POST /api/v1/admin/withdrawals/:id/complete
 */
export const completeWithdrawalRequest = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super admin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    const { id } = req.params;

    if (!id) {
      return res.status(400).json(errorResponse('Withdrawal request ID is required'));
    }

    // Validate request body
    const schema = Joi.object({
      admin_notes: Joi.string().max(500).optional()
    });

    const { error, value } = schema.validate(req.body);
    if (error) {
      return res.status(400).json(errorResponse(error.details[0].message));
    }

    const { admin_notes } = value;

    // Complete withdrawal request
    const updatedRequest = await walletService.completeWithdrawalRequest(
      id, 
      req.user.userId, 
      admin_notes
    );
    
    return res.status(200).json(successResponse(updatedRequest, 'Withdrawal request marked as completed successfully'));
  } catch (err: any) {
    console.error('Error completing withdrawal request:', err);
    
    if (err.message.includes('not found') || err.message.includes('not in approved status')) {
      return res.status(404).json(errorResponse(err.message));
    }
    
    return res.status(500).json(errorResponse('Internal server error'));
  }
};

/**
 * Get withdrawal statistics for admin dashboard
 * @route GET /api/v1/admin/withdrawals/stats
 */
export const getWithdrawalStats = async (req: Request, res: Response) => {
  try {
    // Validate admin role
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super admin')) {
      return res.status(403).json(errorResponse('Access denied. Admin privileges required.'));
    }

    // Get statistics for different statuses
    const [pending, approved, completed, rejected] = await Promise.all([
      walletService.getWithdrawalRequests(1, 1, 'pending'),
      walletService.getWithdrawalRequests(1, 1, 'approved'),
      walletService.getWithdrawalRequests(1, 1, 'completed'),
      walletService.getWithdrawalRequests(1, 1, 'rejected')
    ]);

    const stats = {
      pending: pending.pagination.total_items,
      approved: approved.pagination.total_items,
      completed: completed.pagination.total_items,
      rejected: rejected.pagination.total_items,
      total: pending.pagination.total_items + approved.pagination.total_items + 
             completed.pagination.total_items + rejected.pagination.total_items
    };
    
    return res.status(200).json(successResponse(stats, 'Withdrawal statistics retrieved successfully'));
  } catch (err: any) {
    console.error('Error fetching withdrawal statistics:', err);
    return res.status(500).json(errorResponse('Internal server error'));
  }
};
