-- Help & Support System Database Schema
-- This file creates all necessary tables for the WiggyZ help and support system

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Support Messages Table
-- Stores contact form submissions from users
CREATE TABLE support_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  subject VARCHAR(200) NOT NULL,
  category VARCHAR(50) NOT NULL CHECK (category IN ('bug_report', 'feature_request', 'general_inquiry', 'account_issue', 'payment_issue', 'technical_support')),
  message TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
  priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  admin_notes TEXT,
  assigned_to UUID REFERENCES users(id), -- Admin user assigned to handle this message
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE
);

-- Support Message Replies Table
-- Stores admin replies to support messages
CREATE TABLE support_message_replies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  support_message_id UUID REFERENCES support_messages(id) ON DELETE CASCADE,
  admin_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  reply_text TEXT NOT NULL,
  is_internal BOOLEAN DEFAULT FALSE, -- Internal notes vs user-visible replies
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat Sessions Table
-- Manages real-time chat sessions between users and support
CREATE TABLE chat_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  admin_user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- Assigned admin
  session_type VARCHAR(20) DEFAULT 'support' CHECK (session_type IN ('support', 'ai_bot')),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'waiting', 'closed', 'transferred')),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}' -- Store additional session data
);

-- Chat Messages Table
-- Stores individual messages within chat sessions
CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  chat_session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES users(id) ON DELETE SET NULL,
  sender_type VARCHAR(20) NOT NULL CHECK (sender_type IN ('user', 'admin', 'ai_bot')),
  message_text TEXT NOT NULL,
  message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'system')),
  is_read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}' -- Store additional message data like file URLs
);

-- Rate Limiting Table
-- Prevents spam by tracking user submission rates
CREATE TABLE rate_limiting (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  action_type VARCHAR(50) NOT NULL, -- 'support_message', 'chat_message', etc.
  action_count INTEGER DEFAULT 1,
  window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  window_end TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 hour'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, action_type, window_start)
);

-- FAQ Categories Table
-- Organizes FAQ content by category
CREATE TABLE faq_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- FAQ Items Table
-- Stores frequently asked questions and answers
CREATE TABLE faq_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category_id UUID REFERENCES faq_categories(id) ON DELETE CASCADE,
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  view_count INTEGER DEFAULT 0,
  helpful_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User FAQ Feedback Table
-- Tracks user feedback on FAQ helpfulness
CREATE TABLE faq_feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  faq_item_id UUID REFERENCES faq_items(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  is_helpful BOOLEAN NOT NULL,
  feedback_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(faq_item_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_support_messages_user_id ON support_messages(user_id);
CREATE INDEX idx_support_messages_status ON support_messages(status);
CREATE INDEX idx_support_messages_category ON support_messages(category);
CREATE INDEX idx_support_messages_created_at ON support_messages(created_at);
CREATE INDEX idx_support_messages_assigned_to ON support_messages(assigned_to);

CREATE INDEX idx_support_message_replies_message_id ON support_message_replies(support_message_id);
CREATE INDEX idx_support_message_replies_admin_user_id ON support_message_replies(admin_user_id);

CREATE INDEX idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX idx_chat_sessions_admin_user_id ON chat_sessions(admin_user_id);
CREATE INDEX idx_chat_sessions_status ON chat_sessions(status);
CREATE INDEX idx_chat_sessions_last_activity ON chat_sessions(last_activity);

CREATE INDEX idx_chat_messages_session_id ON chat_messages(chat_session_id);
CREATE INDEX idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_chat_messages_is_read ON chat_messages(is_read);

CREATE INDEX idx_rate_limiting_user_action ON rate_limiting(user_id, action_type);
CREATE INDEX idx_rate_limiting_window ON rate_limiting(window_start, window_end);

CREATE INDEX idx_faq_items_category_id ON faq_items(category_id);
CREATE INDEX idx_faq_items_active_order ON faq_items(is_active, display_order);

-- Enable Row Level Security (RLS)
ALTER TABLE support_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_message_replies ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limiting ENABLE ROW LEVEL SECURITY;
ALTER TABLE faq_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE faq_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE faq_feedback ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Support Messages
CREATE POLICY "Users can view their own support messages" 
  ON support_messages FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own support messages" 
  ON support_messages FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all support messages" 
  ON support_messages FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager', 'spectator')
    )
  );

CREATE POLICY "Admins can update support messages" 
  ON support_messages FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager', 'spectator')
    )
  );

-- RLS Policies for Support Message Replies
CREATE POLICY "Users can view replies to their messages" 
  ON support_message_replies FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM support_messages 
      WHERE support_messages.id = support_message_replies.support_message_id 
      AND support_messages.user_id = auth.uid()
    )
  );

CREATE POLICY "Admins can view all replies" 
  ON support_message_replies FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager', 'spectator')
    )
  );

CREATE POLICY "Admins can create replies" 
  ON support_message_replies FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager', 'spectator')
    )
  );

-- RLS Policies for Chat Sessions
CREATE POLICY "Users can view their own chat sessions" 
  ON chat_sessions FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own chat sessions" 
  ON chat_sessions FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all chat sessions" 
  ON chat_sessions FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager', 'spectator')
    )
  );

CREATE POLICY "Admins can update chat sessions" 
  ON chat_sessions FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager', 'spectator')
    )
  );

-- RLS Policies for Chat Messages
CREATE POLICY "Users can view messages in their chat sessions" 
  ON chat_messages FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM chat_sessions 
      WHERE chat_sessions.id = chat_messages.chat_session_id 
      AND chat_sessions.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create messages in their chat sessions" 
  ON chat_messages FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM chat_sessions 
      WHERE chat_sessions.id = chat_messages.chat_session_id 
      AND chat_sessions.user_id = auth.uid()
    )
  );

CREATE POLICY "Admins can view all chat messages" 
  ON chat_messages FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager', 'spectator')
    )
  );

CREATE POLICY "Admins can create chat messages" 
  ON chat_messages FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager', 'spectator')
    )
  );

-- RLS Policies for Rate Limiting
CREATE POLICY "Users can view their own rate limiting data" 
  ON rate_limiting FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own rate limiting data" 
  ON rate_limiting FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "System can update rate limiting data" 
  ON rate_limiting FOR UPDATE 
  USING (auth.uid() = user_id);

-- RLS Policies for FAQ (Public read access)
CREATE POLICY "Anyone can view active FAQ categories" 
  ON faq_categories FOR SELECT 
  USING (is_active = true);

CREATE POLICY "Anyone can view active FAQ items" 
  ON faq_items FOR SELECT 
  USING (is_active = true);

CREATE POLICY "Authenticated users can provide FAQ feedback" 
  ON faq_feedback FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own FAQ feedback" 
  ON faq_feedback FOR SELECT 
  USING (auth.uid() = user_id);

-- Admins can manage FAQ content
CREATE POLICY "Admins can manage FAQ categories" 
  ON faq_categories FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager')
    )
  );

CREATE POLICY "Admins can manage FAQ items" 
  ON faq_items FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'manager')
    )
  );

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_support_messages_updated_at 
  BEFORE UPDATE ON support_messages 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_faq_items_updated_at 
  BEFORE UPDATE ON faq_items 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default FAQ categories
INSERT INTO faq_categories (name, description, display_order) VALUES
('Getting Started', 'Basic information for new users', 1),
('Matches & Tournaments', 'Questions about gameplay and competitions', 2),
('Wallet & Payments', 'Payment, wallet, and transaction related questions', 3),
('Account & Profile', 'Account management and profile settings', 4),
('Technical Support', 'Technical issues and troubleshooting', 5),
('Rules & Policies', 'Platform rules, terms of service, and policies', 6);

-- Insert sample FAQ items
INSERT INTO faq_items (category_id, question, answer, display_order) VALUES
(
  (SELECT id FROM faq_categories WHERE name = 'Getting Started'),
  'How do I create an account on WiggyZ?',
  'To create an account, download the WiggyZ app and tap "Sign Up". Enter your email, create a password, and verify your email address. You can then complete your profile with your gaming information.',
  1
),
(
  (SELECT id FROM faq_categories WHERE name = 'Getting Started'),
  'What games are supported on WiggyZ?',
  'WiggyZ currently supports Free Fire and PUBG Mobile. We are constantly working to add more popular mobile games to our platform.',
  2
),
(
  (SELECT id FROM faq_categories WHERE name = 'Matches & Tournaments'),
  'How do I join a match?',
  'Browse available matches in the Games section, select a match that fits your skill level and schedule, pay the entry fee, and you will receive the room details when the match starts.',
  1
),
(
  (SELECT id FROM faq_categories WHERE name = 'Matches & Tournaments'),
  'What happens if I win a match?',
  'Winners are determined by admin verification of submitted results. Once verified, prize money is automatically credited to your wallet within 24 hours.',
  2
),
(
  (SELECT id FROM faq_categories WHERE name = 'Wallet & Payments'),
  'How do I add money to my wallet?',
  'Go to the Wallet section in the app and tap "Add Funds". You can add money using various payment methods including UPI, credit/debit cards, and net banking.',
  1
),
(
  (SELECT id FROM faq_categories WHERE name = 'Wallet & Payments'),
  'How do I withdraw my winnings?',
  'Navigate to the Wallet section and tap "Withdraw". Enter the amount and your bank details. Withdrawals are processed within 2-3 business days.',
  2
),
(
  (SELECT id FROM faq_categories WHERE name = 'Technical Support'),
  'The app is not loading properly. What should I do?',
  'Try closing and reopening the app. If the issue persists, check your internet connection and ensure you have the latest version of the app. Contact support if problems continue.',
  1
),
(
  (SELECT id FROM faq_categories WHERE name = 'Account & Profile'),
  'How do I update my gaming profile information?',
  'Go to your Profile section, tap "Personal Information", and update your gaming details including your game username, UID, and preferred server.',
  1
);
