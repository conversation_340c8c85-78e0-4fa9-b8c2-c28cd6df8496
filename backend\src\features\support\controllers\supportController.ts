/**
 * Support Controller
 * Handles support message related operations
 */

import { Request, Response } from 'express';
import { supportService } from '../services/supportService';
import { errorHandler } from '../../../utils/errorHandler';
import { logger } from '../../../utils/logger';

/**
 * Create a new support message
 */
export const createSupportMessage = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { subject, category, message } = req.body;

    const supportMessage = await supportService.createSupportMessage({
      userId,
      subject,
      category,
      message
    });

    logger.info(`Support message created: ${supportMessage.id} by user ${userId}`);

    res.status(201).json({
      message: 'Support message created successfully',
      data: supportMessage
    });
  } catch (error) {
    logger.error(`Error creating support message: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get user's support messages
 */
export const getUserSupportMessages = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const filters = req.query;
    const messages = await supportService.getUserSupportMessages(userId, filters);

    res.json({
      message: 'Support messages retrieved successfully',
      data: messages
    });
  } catch (error) {
    logger.error(`Error getting user support messages: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get all support messages (admin only)
 */
export const getAllSupportMessages = async (req: Request, res: Response) => {
  try {
    const filters = req.query;
    const messages = await supportService.getAllSupportMessages(filters);

    res.json({
      message: 'All support messages retrieved successfully',
      data: messages
    });
  } catch (error) {
    logger.error(`Error getting all support messages: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get support message by ID
 */
export const getSupportMessageById = async (req: Request, res: Response) => {
  try {
    const { messageId } = req.params;
    const userId = req.user?.userId;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const message = await supportService.getSupportMessageById(messageId, userId, userRole);

    if (!message) {
      return res.status(404).json({ error: 'Support message not found' });
    }

    res.json({
      message: 'Support message retrieved successfully',
      data: message
    });
  } catch (error) {
    logger.error(`Error getting support message by ID: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Update support message (admin only)
 */
export const updateSupportMessage = async (req: Request, res: Response) => {
  try {
    const { messageId } = req.params;
    const adminUserId = req.user?.userId;
    const updates = req.body;

    if (!adminUserId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const updatedMessage = await supportService.updateSupportMessage(
      messageId,
      updates,
      adminUserId
    );

    if (!updatedMessage) {
      return res.status(404).json({ error: 'Support message not found' });
    }

    logger.info(`Support message updated: ${messageId} by admin ${adminUserId}`);

    res.json({
      message: 'Support message updated successfully',
      data: updatedMessage
    });
  } catch (error) {
    logger.error(`Error updating support message: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Create support message reply (admin only)
 */
export const createSupportMessageReply = async (req: Request, res: Response) => {
  try {
    const { messageId } = req.params;
    const adminUserId = req.user?.userId;
    const { reply_text, is_internal } = req.body;

    if (!adminUserId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const reply = await supportService.createSupportMessageReply({
      supportMessageId: messageId,
      adminUserId,
      replyText: reply_text,
      isInternal: is_internal || false
    });

    logger.info(`Support message reply created: ${reply.id} by admin ${adminUserId}`);

    res.status(201).json({
      message: 'Support message reply created successfully',
      data: reply
    });
  } catch (error) {
    logger.error(`Error creating support message reply: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get support message replies
 */
export const getSupportMessageReplies = async (req: Request, res: Response) => {
  try {
    const { messageId } = req.params;
    const userId = req.user?.userId;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const replies = await supportService.getSupportMessageReplies(messageId, userId, userRole);

    res.json({
      message: 'Support message replies retrieved successfully',
      data: replies
    });
  } catch (error) {
    logger.error(`Error getting support message replies: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Assign support message to admin
 */
export const assignSupportMessage = async (req: Request, res: Response) => {
  try {
    const { messageId } = req.params;
    const { assigned_to } = req.body;
    const adminUserId = req.user?.userId;

    if (!adminUserId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const updatedMessage = await supportService.assignSupportMessage(
      messageId,
      assigned_to,
      adminUserId
    );

    if (!updatedMessage) {
      return res.status(404).json({ error: 'Support message not found' });
    }

    logger.info(`Support message assigned: ${messageId} to ${assigned_to} by ${adminUserId}`);

    res.json({
      message: 'Support message assigned successfully',
      data: updatedMessage
    });
  } catch (error) {
    logger.error(`Error assigning support message: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Update support message status
 */
export const updateSupportMessageStatus = async (req: Request, res: Response) => {
  try {
    const { messageId } = req.params;
    const { status } = req.body;
    const adminUserId = req.user?.userId;

    if (!adminUserId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const updatedMessage = await supportService.updateSupportMessageStatus(
      messageId,
      status,
      adminUserId
    );

    if (!updatedMessage) {
      return res.status(404).json({ error: 'Support message not found' });
    }

    logger.info(`Support message status updated: ${messageId} to ${status} by ${adminUserId}`);

    res.json({
      message: 'Support message status updated successfully',
      data: updatedMessage
    });
  } catch (error) {
    logger.error(`Error updating support message status: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get support analytics (admin only)
 */
export const getSupportAnalytics = async (req: Request, res: Response) => {
  try {
    const analytics = await supportService.getSupportAnalytics();

    res.json({
      message: 'Support analytics retrieved successfully',
      data: analytics
    });
  } catch (error) {
    logger.error(`Error getting support analytics: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get response time analytics (admin only)
 */
export const getResponseTimeAnalytics = async (req: Request, res: Response) => {
  try {
    const analytics = await supportService.getResponseTimeAnalytics();

    res.json({
      message: 'Response time analytics retrieved successfully',
      data: analytics
    });
  } catch (error) {
    logger.error(`Error getting response time analytics: ${error}`);
    return errorHandler(error as Error, res);
  }
};

/**
 * Get satisfaction analytics (admin only)
 */
export const getSatisfactionAnalytics = async (req: Request, res: Response) => {
  try {
    const analytics = await supportService.getSatisfactionAnalytics();

    res.json({
      message: 'Satisfaction analytics retrieved successfully',
      data: analytics
    });
  } catch (error) {
    logger.error(`Error getting satisfaction analytics: ${error}`);
    return errorHandler(error as Error, res);
  }
};
