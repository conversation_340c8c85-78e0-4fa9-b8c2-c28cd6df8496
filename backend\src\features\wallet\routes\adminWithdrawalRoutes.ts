/**
 * Admin withdrawal management routes
 */
import express from 'express';
import {
  getWithdrawalRequests,
  getWithdrawalRequest,
  approveWithdrawalRequest,
  rejectWithdrawalRequest,
  completeWithdrawalRequest,
  getWithdrawalStats
} from '../controllers/adminWithdrawalController';
import { authenticate } from '../../../middleware/auth';
import { 
  auditPaymentOperation,
  paymentInitiationRateLimit 
} from '../../../middleware/paymentSecurity';

const router = express.Router();

// All routes require authentication and admin role
// Role validation is done in each controller method

// Get withdrawal statistics
router.get('/stats',
  authenticate,
  auditPaymentOperation('admin_withdrawal_stats'),
  getWithdrawalStats
);

// Get all withdrawal requests with pagination and filtering
router.get('/',
  authenticate,
  auditPaymentOperation('admin_withdrawal_list'),
  getWithdrawalRequests
);

// Get specific withdrawal request by ID
router.get('/:id',
  authenticate,
  auditPaymentOperation('admin_withdrawal_view'),
  getWithdrawalRequest
);

// Approve withdrawal request
router.post('/:id/approve',
  authenticate,
  paymentInitiationRateLimit,
  auditPaymentOperation('admin_withdrawal_approve'),
  approveWithdrawalRequest
);

// Reject withdrawal request
router.post('/:id/reject',
  authenticate,
  paymentInitiationRateLimit,
  auditPaymentOperation('admin_withdrawal_reject'),
  rejectWithdrawalRequest
);

// Mark withdrawal request as completed
router.post('/:id/complete',
  authenticate,
  paymentInitiationRateLimit,
  auditPaymentOperation('admin_withdrawal_complete'),
  completeWithdrawalRequest
);

export default router;
