#!/usr/bin/env node

/**
 * Comprehensive Rewards System Integration Test
 * Tests the complete data flow from database to frontend and admin panel
 */

const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:5000/api/v1';
const FRONTEND_URL = 'http://localhost:3000';
const ADMIN_URL = 'http://localhost:3001';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Advanture101$'
};

let authToken = null;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, ...args) {
  console.log(color + args.join(' ') + colors.reset);
}

function section(title) {
  console.log('\n' + colors.bold + colors.blue + '='.repeat(50) + colors.reset);
  console.log(colors.bold + colors.blue + title + colors.reset);
  console.log(colors.bold + colors.blue + '='.repeat(50) + colors.reset);
}

async function login() {
  try {
    log(colors.blue, '🔐 Authenticating test user...');
    const response = await axios.post(`${BASE_URL}/auth/login`, TEST_USER);
    
    if (response.data && response.data.access_token) {
      authToken = response.data.access_token;
      log(colors.green, '✅ Authentication successful');
      log(colors.green, `   User: ${response.data.user.name} (${response.data.user.role})`);
      return true;
    } else {
      log(colors.red, '❌ Login failed - no token received');
      return false;
    }
  } catch (error) {
    log(colors.red, '❌ Login failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testDailyRewards() {
  section('Testing Daily Rewards API');
  
  try {
    log(colors.blue, '📅 Testing daily rewards status endpoint...');
    const response = await axios.get(`${BASE_URL}/rewards/daily-status`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    if (response.data.success) {
      log(colors.green, '✅ Daily rewards API working');
      log(colors.green, `   Current streak: ${response.data.data.streak.current_streak}`);
      log(colors.green, `   Available rewards: ${response.data.data.rewards.length}`);
      log(colors.green, `   Has claimed today: ${response.data.data.hasClaimedToday}`);
      return true;
    } else {
      log(colors.red, '❌ Daily rewards API failed');
      return false;
    }
  } catch (error) {
    log(colors.red, '❌ Daily rewards test failed:', error.response?.data?.message || error.message);
    return false;
  }
}

async function testLoyaltyRewards() {
  section('Testing Loyalty Rewards API');
  
  try {
    log(colors.blue, '🏆 Testing loyalty status endpoint...');
    const statusResponse = await axios.get(`${BASE_URL}/rewards/loyalty`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    if (statusResponse.data.success) {
      log(colors.green, '✅ Loyalty status API working');
      log(colors.green, `   Current points: ${statusResponse.data.data.points}`);
      log(colors.green, `   Current tier: ${statusResponse.data.data.tier.name}`);
      log(colors.green, `   Points to next tier: ${statusResponse.data.data.points_to_next_tier}`);
    }
    
    log(colors.blue, '🏅 Testing loyalty tiers endpoint...');
    const tiersResponse = await axios.get(`${BASE_URL}/rewards/loyalty/tiers`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    if (tiersResponse.data.success) {
      log(colors.green, '✅ Loyalty tiers API working');
      log(colors.green, `   Available tiers: ${tiersResponse.data.data.length}`);
      tiersResponse.data.data.forEach(tier => {
        log(colors.green, `   - ${tier.name}: ${tier.min_points} points`);
      });
      return true;
    } else {
      log(colors.red, '❌ Loyalty tiers API failed');
      return false;
    }
  } catch (error) {
    log(colors.red, '❌ Loyalty rewards test failed:', error.response?.data?.message || error.message);
    return false;
  }
}

async function testAdminEndpoints() {
  section('Testing Admin Panel API Endpoints');
  
  try {
    log(colors.blue, '👑 Testing admin daily rewards endpoint...');
    const dailyResponse = await axios.get(`${BASE_URL}/rewards/admin/daily-rewards`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    if (dailyResponse.data.success) {
      log(colors.green, '✅ Admin daily rewards API working');
      log(colors.green, `   Total daily rewards: ${dailyResponse.data.data.length}`);
    }
    
    log(colors.blue, '👑 Testing admin loyalty tiers endpoint...');
    const loyaltyResponse = await axios.get(`${BASE_URL}/rewards/admin/loyalty-tiers`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    if (loyaltyResponse.data.success) {
      log(colors.green, '✅ Admin loyalty tiers API working');
      log(colors.green, `   Total loyalty tiers: ${loyaltyResponse.data.data.length}`);
      return true;
    } else {
      log(colors.red, '❌ Admin endpoints failed');
      return false;
    }
  } catch (error) {
    log(colors.red, '❌ Admin endpoints test failed:', error.response?.data?.message || error.message);
    return false;
  }
}

async function testFrontendConnectivity() {
  section('Testing Frontend Connectivity');
  
  try {
    log(colors.blue, '🌐 Testing Flutter frontend availability...');
    const frontendResponse = await axios.get(FRONTEND_URL, { timeout: 5000 });
    
    if (frontendResponse.status === 200) {
      log(colors.green, '✅ Flutter frontend is accessible');
      log(colors.green, `   Frontend URL: ${FRONTEND_URL}`);
    }
    
    log(colors.blue, '🌐 Testing admin panel availability...');
    const adminResponse = await axios.get(ADMIN_URL, { timeout: 5000 });
    
    if (adminResponse.status === 200) {
      log(colors.green, '✅ Admin panel is accessible');
      log(colors.green, `   Admin URL: ${ADMIN_URL}`);
      return true;
    }
  } catch (error) {
    log(colors.yellow, '⚠️ Frontend connectivity test failed:', error.message);
    log(colors.yellow, '   This is expected if the frontend servers are not running');
    return false;
  }
}

async function runTests() {
  section('WiggyZ Rewards System Integration Test');
  
  const results = {
    authentication: false,
    dailyRewards: false,
    loyaltyRewards: false,
    adminEndpoints: false,
    frontendConnectivity: false
  };
  
  // Test authentication
  results.authentication = await login();
  if (!results.authentication) {
    log(colors.red, '❌ Cannot proceed without authentication');
    return results;
  }
  
  // Test API endpoints
  results.dailyRewards = await testDailyRewards();
  results.loyaltyRewards = await testLoyaltyRewards();
  results.adminEndpoints = await testAdminEndpoints();
  results.frontendConnectivity = await testFrontendConnectivity();
  
  // Summary
  section('Test Results Summary');
  
  Object.entries(results).forEach(([test, passed]) => {
    const icon = passed ? '✅' : '❌';
    const color = passed ? colors.green : colors.red;
    log(color, `${icon} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log('\n' + colors.bold + colors.blue + '='.repeat(50) + colors.reset);
  log(colors.bold, `Overall Result: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    log(colors.green, '🎉 All tests passed! Rewards system is fully functional.');
  } else if (passedTests >= 3) {
    log(colors.yellow, '⚠️ Core functionality working, some optional features may need attention.');
  } else {
    log(colors.red, '❌ Critical issues detected. Please check the backend and database.');
  }
  
  return results;
}

// Run the tests
runTests().catch(error => {
  log(colors.red, '❌ Test suite failed:', error.message);
  process.exit(1);
});
