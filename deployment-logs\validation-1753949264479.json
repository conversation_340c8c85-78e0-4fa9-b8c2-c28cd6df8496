{"timestamp": "2025-07-31T08:07:44.479Z", "results": [{"timestamp": "2025-07-31T08:07:43.950Z", "message": "Loaded environment variables from backend/.env", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.963Z", "message": "🔍 Running pre-deployment validation...", "type": "info", "category": "validation"}, {"timestamp": "2025-07-31T08:07:43.963Z", "message": "Validating environment variables...", "type": "info", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.964Z", "message": "Found required variable: SUPABASE_URL", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.964Z", "message": "Found required variable: SUPABASE_KEY", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.964Z", "message": "Found required variable: JWT_REFRESH_SECRET", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.965Z", "message": "Found required variable: RAZORPAY_KEY_ID", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.965Z", "message": "Found required variable: RAZORPAY_KEY_SECRET", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.966Z", "message": "Found optional variable: JWT_ACCESS_EXPIRY", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.966Z", "message": "Found optional variable: JWT_REFRESH_EXPIRY", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.967Z", "message": "Missing optional environment variable: PAYMENT_TIMEOUT_MINUTES (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.967Z", "message": "Missing optional environment variable: DISABLE_FRAUD_DETECTION (using default)", "type": "warn", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.967Z", "message": "✅ All required environment variables are present", "type": "success", "category": "env"}, {"timestamp": "2025-07-31T08:07:43.968Z", "message": "Validating Supabase connection...", "type": "info", "category": "database"}, {"timestamp": "2025-07-31T08:07:44.475Z", "message": "Supabase connection successful", "type": "success", "category": "database"}, {"timestamp": "2025-07-31T08:07:44.476Z", "message": "Validating Razorpay credentials...", "type": "info", "category": "payment"}, {"timestamp": "2025-07-31T08:07:44.476Z", "message": "Razorpay credentials format validation passed", "type": "success", "category": "payment"}, {"timestamp": "2025-07-31T08:07:44.476Z", "message": "Validating build output...", "type": "info", "category": "build"}, {"timestamp": "2025-07-31T08:07:44.477Z", "message": "Build output is recent", "type": "success", "category": "build"}, {"timestamp": "2025-07-31T08:07:44.478Z", "message": "Build output validation passed", "type": "success", "category": "build"}, {"timestamp": "2025-07-31T08:07:44.478Z", "message": "✅ All pre-deployment validations passed", "type": "success", "category": "validation"}]}