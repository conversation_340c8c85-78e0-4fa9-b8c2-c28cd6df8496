/**
 * Match Audit Service
 * 
 * Comprehensive audit logging and monitoring for match deadline automation
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { supabase as sb } from '../../../config/supabase';
import { logger } from '../../../utils/logger';

export interface AuditLogEntry {
  match_id: string;
  action_type: 'auto_finalized' | 'deadline_expired' | 'participant_defaulted' | 'auto_winner_assigned' | 'auto_loser_assigned' | 'match_auto_completed' | 'deadline_notification_sent' | 'manual_admin_override' | 'creator_refunded';
  triggered_by: 'system' | 'admin' | 'cron_job';
  admin_user_id?: string;
  affected_participants: string[];
  action_details: any;
  reason: string;
  deadline_time?: string;
  participants_submitted: number;
  participants_defaulted: number;
  total_participants: number;
  system_version?: string;
}

export interface AuditMetrics {
  totalActions: number;
  autoFinalizedMatches: number;
  participantsDefaulted: number;
  successRate: number;
  averageProcessingTime: number;
  errorCount: number;
  lastProcessedAt?: string;
}

export interface AlertCondition {
  type: 'high_error_rate' | 'processing_delay' | 'mass_defaults' | 'system_failure';
  threshold: number;
  timeWindow: number; // minutes
  enabled: boolean;
}

export class MatchAuditService {
  private supabase: SupabaseClient;
  private alertConditions: AlertCondition[] = [
    { type: 'high_error_rate', threshold: 0.1, timeWindow: 60, enabled: true }, // 10% error rate in 1 hour
    { type: 'processing_delay', threshold: 10, timeWindow: 30, enabled: true }, // 10+ minute delay
    { type: 'mass_defaults', threshold: 50, timeWindow: 60, enabled: true }, // 50+ defaults in 1 hour
    { type: 'system_failure', threshold: 5, timeWindow: 15, enabled: true }, // 5+ consecutive failures
  ];

  constructor() {
    this.supabase = sb;
  }

  /**
   * Log an audit entry for automated actions
   */
  async logAction(entry: AuditLogEntry): Promise<void> {
    try {
      const auditEntry = {
        match_id: entry.match_id,
        action_type: entry.action_type,
        triggered_by: entry.triggered_by,
        admin_user_id: entry.admin_user_id,
        affected_participants: entry.affected_participants,
        action_details: entry.action_details,
        reason: entry.reason,
        deadline_time: entry.deadline_time,
        participants_submitted: entry.participants_submitted,
        participants_defaulted: entry.participants_defaulted,
        total_participants: entry.total_participants,
        system_version: entry.system_version || '1.0',
        action_timestamp: new Date().toISOString()
      };

      const { error } = await this.supabase
        .from('match_audit_log')
        .insert(auditEntry);

      if (error) {
        logger.error('Failed to log audit entry: ' + error.message);
        throw new Error(`Audit logging failed: ${error.message}`);
      }

      // Check for alert conditions after logging
      await this.checkAlertConditions();

      logger.info(`Audit entry logged for match ${entry.match_id}: ${entry.action_type}`);

    } catch (error) {
      logger.error('Error in audit logging: ' + (error instanceof Error ? error.message : String(error)));
      // Don't throw - audit logging should not break the main flow
    }
  }

  /**
   * Get audit metrics for monitoring dashboard
   */
  async getAuditMetrics(timeWindow: number = 24): Promise<AuditMetrics> {
    try {
      const since = new Date(Date.now() - timeWindow * 60 * 60 * 1000).toISOString();

      // Get total actions
      const { count: totalActions } = await this.supabase
        .from('match_audit_log')
        .select('*', { count: 'exact', head: true })
        .gte('action_timestamp', since);

      // Get auto-finalized matches
      const { count: autoFinalizedMatches } = await this.supabase
        .from('match_audit_log')
        .select('*', { count: 'exact', head: true })
        .eq('action_type', 'auto_finalized')
        .gte('action_timestamp', since);

      // Get total participants defaulted
      const { data: defaultData } = await this.supabase
        .from('match_audit_log')
        .select('participants_defaulted')
        .gte('action_timestamp', since);

      const participantsDefaulted = defaultData?.reduce((sum, entry) => sum + (entry.participants_defaulted || 0), 0) || 0;

      // Get error count (from action_details containing error)
      const { data: errorData } = await this.supabase
        .from('match_audit_log')
        .select('action_details')
        .gte('action_timestamp', since);

      const errorCount = errorData?.filter(entry => 
        entry.action_details && 
        (entry.action_details.error || entry.action_details.failed)
      ).length || 0;

      // Calculate success rate
      const successRate = totalActions ? ((totalActions - errorCount) / totalActions) * 100 : 100;

      // Get last processed timestamp
      const { data: lastProcessed } = await this.supabase
        .from('match_audit_log')
        .select('action_timestamp')
        .order('action_timestamp', { ascending: false })
        .limit(1);

      return {
        totalActions: totalActions || 0,
        autoFinalizedMatches: autoFinalizedMatches || 0,
        participantsDefaulted,
        successRate,
        averageProcessingTime: 0, // TODO: Calculate from processing timestamps
        errorCount,
        lastProcessedAt: lastProcessed?.[0]?.action_timestamp
      };

    } catch (error) {
      logger.error('Error getting audit metrics: ' + (error instanceof Error ? error.message : String(error)));
      throw error;
    }
  }

  /**
   * Get recent audit logs with pagination
   */
  async getRecentAuditLogs(limit: number = 50, offset: number = 0): Promise<AuditLogEntry[]> {
    try {
      const { data, error } = await this.supabase
        .from('match_audit_log')
        .select('*')
        .order('action_timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new Error(`Failed to get audit logs: ${error.message}`);
      }

      return data || [];

    } catch (error) {
      logger.error('Error getting recent audit logs: ' + (error instanceof Error ? error.message : String(error)));
      throw error;
    }
  }

  /**
   * Get audit logs for a specific match
   */
  async getMatchAuditLogs(matchId: string): Promise<AuditLogEntry[]> {
    try {
      const { data, error } = await this.supabase
        .from('match_audit_log')
        .select('*')
        .eq('match_id', matchId)
        .order('action_timestamp', { ascending: false });

      if (error) {
        throw new Error(`Failed to get match audit logs: ${error.message}`);
      }

      return data || [];

    } catch (error) {
      logger.error('Error getting match audit logs: ' + (error instanceof Error ? error.message : String(error)));
      throw error;
    }
  }

  /**
   * Check for alert conditions and trigger notifications
   */
  private async checkAlertConditions(): Promise<void> {
    try {
      for (const condition of this.alertConditions) {
        if (!condition.enabled) continue;

        const shouldAlert = await this.evaluateAlertCondition(condition);
        if (shouldAlert) {
          await this.triggerAlert(condition);
        }
      }
    } catch (error) {
      logger.error('Error checking alert conditions: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * Evaluate a specific alert condition
   */
  private async evaluateAlertCondition(condition: AlertCondition): Promise<boolean> {
    const since = new Date(Date.now() - condition.timeWindow * 60 * 1000).toISOString();

    switch (condition.type) {
      case 'high_error_rate':
        return await this.checkHighErrorRate(since, condition.threshold);
      
      case 'processing_delay':
        return await this.checkProcessingDelay(condition.threshold);
      
      case 'mass_defaults':
        return await this.checkMassDefaults(since, condition.threshold);
      
      case 'system_failure':
        return await this.checkSystemFailure(since, condition.threshold);
      
      default:
        return false;
    }
  }

  /**
   * Check for high error rate
   */
  private async checkHighErrorRate(since: string, threshold: number): Promise<boolean> {
    const { count: totalActions } = await this.supabase
      .from('match_audit_log')
      .select('*', { count: 'exact', head: true })
      .gte('action_timestamp', since);

    const { data: errorData } = await this.supabase
      .from('match_audit_log')
      .select('action_details')
      .gte('action_timestamp', since);

    const errorCount = errorData?.filter(entry => 
      entry.action_details && entry.action_details.error
    ).length || 0;

    const errorRate = totalActions ? errorCount / totalActions : 0;
    return errorRate > threshold;
  }

  /**
   * Check for processing delays
   */
  private async checkProcessingDelay(thresholdMinutes: number): Promise<boolean> {
    // Check if there are expired matches that haven't been processed
    const { data: expiredMatches } = await this.supabase
      .rpc('get_expired_matches_for_finalization');

    if (!expiredMatches || expiredMatches.length === 0) return false;

    // Check if any match has been expired for longer than threshold
    const now = new Date();
    return expiredMatches.some((match: any) => {
      const deadline = new Date(match.deadline);
      const minutesOverdue = (now.getTime() - deadline.getTime()) / (1000 * 60);
      return minutesOverdue > thresholdMinutes;
    });
  }

  /**
   * Check for mass defaults
   */
  private async checkMassDefaults(since: string, threshold: number): Promise<boolean> {
    const { data: defaultData } = await this.supabase
      .from('match_audit_log')
      .select('participants_defaulted')
      .gte('action_timestamp', since);

    const totalDefaults = defaultData?.reduce((sum, entry) => sum + (entry.participants_defaulted || 0), 0) || 0;
    return totalDefaults > threshold;
  }

  /**
   * Check for system failures
   */
  private async checkSystemFailure(since: string, threshold: number): Promise<boolean> {
    const { data: recentLogs } = await this.supabase
      .from('match_audit_log')
      .select('action_details')
      .gte('action_timestamp', since)
      .order('action_timestamp', { ascending: false })
      .limit(threshold);

    if (!recentLogs || recentLogs.length < threshold) return false;

    // Check if all recent logs contain errors
    return recentLogs.every(log => log.action_details && log.action_details.error);
  }

  /**
   * Trigger alert notification
   */
  private async triggerAlert(condition: AlertCondition): Promise<void> {
    try {
      const alertMessage = this.generateAlertMessage(condition);
      
      // Log the alert
      logger.warn(`ALERT: ${condition.type} - ${alertMessage}`);

      // TODO: Integrate with notification system (email, Slack, etc.)
      // await notificationService.sendAlert({
      //   type: condition.type,
      //   message: alertMessage,
      //   severity: 'high',
      //   timestamp: new Date().toISOString()
      // });

      // Log the alert to audit trail
      await this.supabase
        .from('match_audit_log')
        .insert({
          match_id: 'system',
          action_type: 'manual_admin_override',
          triggered_by: 'system',
          affected_participants: [],
          action_details: {
            alert_type: condition.type,
            alert_message: alertMessage,
            threshold: condition.threshold,
            time_window: condition.timeWindow
          },
          reason: `System alert triggered: ${condition.type}`,
          participants_submitted: 0,
          participants_defaulted: 0,
          total_participants: 0
        });

    } catch (error) {
      logger.error('Error triggering alert: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * Generate alert message based on condition
   */
  private generateAlertMessage(condition: AlertCondition): string {
    switch (condition.type) {
      case 'high_error_rate':
        return `High error rate detected: ${(condition.threshold * 100).toFixed(1)}% threshold exceeded in ${condition.timeWindow} minutes`;
      
      case 'processing_delay':
        return `Processing delay detected: Matches overdue by ${condition.threshold}+ minutes`;
      
      case 'mass_defaults':
        return `Mass participant defaults detected: ${condition.threshold}+ defaults in ${condition.timeWindow} minutes`;
      
      case 'system_failure':
        return `System failure detected: ${condition.threshold} consecutive failures in ${condition.timeWindow} minutes`;
      
      default:
        return `Unknown alert condition: ${condition.type}`;
    }
  }

  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    lastCheck: string;
  }> {
    try {
      const issues: string[] = [];
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';

      // Check recent error rate
      const metrics = await this.getAuditMetrics(1); // Last hour
      if (metrics.successRate < 90) {
        issues.push(`Low success rate: ${metrics.successRate.toFixed(1)}%`);
        status = 'warning';
      }
      if (metrics.successRate < 75) {
        status = 'critical';
      }

      // Check for processing delays
      const hasDelays = await this.checkProcessingDelay(10);
      if (hasDelays) {
        issues.push('Processing delays detected');
        status = status === 'critical' ? 'critical' : 'warning';
      }

      // Check last processing time
      if (metrics.lastProcessedAt) {
        const lastProcessed = new Date(metrics.lastProcessedAt);
        const minutesSinceLastProcess = (Date.now() - lastProcessed.getTime()) / (1000 * 60);
        if (minutesSinceLastProcess > 30) {
          issues.push(`No processing in ${Math.round(minutesSinceLastProcess)} minutes`);
          status = 'warning';
        }
      }

      return {
        status,
        issues,
        lastCheck: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Error getting system health: ' + (error instanceof Error ? error.message : String(error)));
      return {
        status: 'critical',
        issues: ['Failed to check system health'],
        lastCheck: new Date().toISOString()
      };
    }
  }

  /**
   * Export audit logs for external analysis
   */
  async exportAuditLogs(startDate: string, endDate: string): Promise<AuditLogEntry[]> {
    try {
      const { data, error } = await this.supabase
        .from('match_audit_log')
        .select('*')
        .gte('action_timestamp', startDate)
        .lte('action_timestamp', endDate)
        .order('action_timestamp', { ascending: true });

      if (error) {
        throw new Error(`Failed to export audit logs: ${error.message}`);
      }

      return data || [];

    } catch (error) {
      logger.error('Error exporting audit logs: ' + (error instanceof Error ? error.message : String(error)));
      throw error;
    }
  }
}

// Export singleton instance
export const matchAuditService = new MatchAuditService();
