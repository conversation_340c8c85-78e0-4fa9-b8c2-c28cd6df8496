/**
 * Test script to verify that match entry fee refunds now create proper wallet transaction records
 * 
 * This script tests the fix for the missing wallet transaction records issue.
 * Before the fix: Wallet balance was updated but no transaction record was created
 * After the fix: Both wallet balance and transaction record are created atomically
 */

import { supabase } from '../../../config/supabase';
import { walletService } from '../../wallet/services/walletService';
import { logger } from '../../../utils/logger';

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Test the processRefund method to ensure it creates both wallet balance updates and transaction records
 */
async function testRefundTransactionCreation(): Promise<TestResult> {
  try {
    logger.info('Testing refund transaction creation...');

    // Create a test user (or use existing one)
    const testUserId = 'test-user-refund-' + Date.now();
    
    // Get initial wallet state
    const initialBalance = await walletService.getCurrentBalance(testUserId);
    logger.info(`Initial wallet balance: ${initialBalance}`);

    // Get initial transaction count
    const { data: initialTransactions, error: initialTxError } = await supabase
      .from('wallet_transactions')
      .select('*')
      .eq('user_id', testUserId);

    if (initialTxError) {
      throw new Error(`Failed to get initial transactions: ${initialTxError.message}`);
    }

    const initialTxCount = initialTransactions?.length || 0;
    logger.info(`Initial transaction count: ${initialTxCount}`);

    // Process a test refund
    const refundAmount = 100;
    const refundDescription = 'Test match entry fee refund - Match expired with no participants';
    const refundMetadata = { 
      match_id: 'test-match-123', 
      refund_reason: 'No participants joined before deadline' 
    };

    logger.info(`Processing refund of ${refundAmount} for user ${testUserId}...`);
    
    const refundResult = await walletService.processRefund(
      testUserId,
      refundAmount,
      refundDescription,
      refundMetadata
    );

    logger.info(`Refund processed successfully: ${JSON.stringify(refundResult)}`);

    // Verify wallet balance was updated
    const newBalance = await walletService.getCurrentBalance(testUserId);
    const expectedBalance = initialBalance + refundAmount;
    
    if (newBalance !== expectedBalance) {
      return {
        success: false,
        message: `Wallet balance mismatch. Expected: ${expectedBalance}, Actual: ${newBalance}`,
        details: { initialBalance, refundAmount, newBalance, expectedBalance }
      };
    }

    logger.info(`✓ Wallet balance updated correctly: ${initialBalance} + ${refundAmount} = ${newBalance}`);

    // Verify transaction record was created
    const { data: finalTransactions, error: finalTxError } = await supabase
      .from('wallet_transactions')
      .select('*')
      .eq('user_id', testUserId)
      .order('created_at', { ascending: false });

    if (finalTxError) {
      throw new Error(`Failed to get final transactions: ${finalTxError.message}`);
    }

    const finalTxCount = finalTransactions?.length || 0;
    
    if (finalTxCount !== initialTxCount + 1) {
      return {
        success: false,
        message: `Transaction count mismatch. Expected: ${initialTxCount + 1}, Actual: ${finalTxCount}`,
        details: { initialTxCount, finalTxCount, transactions: finalTransactions }
      };
    }

    // Verify the transaction details
    const latestTransaction = finalTransactions[0];
    
    if (!latestTransaction) {
      return {
        success: false,
        message: 'No transaction record found',
        details: { finalTransactions }
      };
    }

    // Check transaction properties
    const expectedProps = {
      user_id: testUserId,
      type: 'refund',
      amount: refundAmount,
      status: 'completed',
      description: refundDescription
    };

    for (const [key, expectedValue] of Object.entries(expectedProps)) {
      if (latestTransaction[key] !== expectedValue) {
        return {
          success: false,
          message: `Transaction property mismatch for ${key}. Expected: ${expectedValue}, Actual: ${latestTransaction[key]}`,
          details: { latestTransaction, expectedProps }
        };
      }
    }

    // Check metadata
    if (!latestTransaction.metadata || 
        latestTransaction.metadata.match_id !== refundMetadata.match_id ||
        latestTransaction.metadata.refund_reason !== refundMetadata.refund_reason) {
      return {
        success: false,
        message: 'Transaction metadata mismatch',
        details: { 
          actualMetadata: latestTransaction.metadata, 
          expectedMetadata: refundMetadata 
        }
      };
    }

    logger.info(`✓ Transaction record created correctly: ${JSON.stringify(latestTransaction)}`);

    return {
      success: true,
      message: 'Refund transaction creation test passed successfully',
      details: {
        initialBalance,
        newBalance,
        refundAmount,
        transactionRecord: latestTransaction,
        transactionCount: { initial: initialTxCount, final: finalTxCount }
      }
    };

  } catch (error: any) {
    logger.error(`Test failed with error: ${error.message}`);
    return {
      success: false,
      message: `Test failed with error: ${error.message}`,
      details: { error: error.stack }
    };
  }
}

/**
 * Main test runner
 */
async function runTests() {
  logger.info('🧪 Starting refund transaction fix verification tests...');
  
  const testResult = await testRefundTransactionCreation();
  
  if (testResult.success) {
    logger.info('✅ All tests passed! The refund transaction fix is working correctly.');
    logger.info(`Summary: ${testResult.message}`);
    if (testResult.details) {
      logger.info(`Details: ${JSON.stringify(testResult.details, null, 2)}`);
    }
  } else {
    logger.error(`❌ Test failed: ${testResult.message}`);
    if (testResult.details) {
      logger.error(`Details: ${JSON.stringify(testResult.details, null, 2)}`);
    }
  }
  
  return testResult.success;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      logger.error(`Unexpected error: ${error.message}`);
      process.exit(1);
    });
}

export { runTests, testRefundTransactionCreation };
