import 'package:flutter_test/flutter_test.dart';
import 'package:wiggyz_app/models/referral_models.dart';
import 'package:wiggyz_app/services/referral_service.dart';

void main() {

  group('ReferralShareContent Tests', () {
    test('should create share content with default message', () {
      // Arrange
      const referralCode = 'TEST1234';

      // Act
      final shareContent = ReferralShareContent.create(referralCode: referralCode);

      // Assert
      expect(shareContent.referralCode, equals(referralCode));
      expect(shareContent.shareText, contains(referralCode));
      expect(shareContent.shareText, contains('WiggyZ'));
      expect(shareContent.shareText, contains('25 points'));
      expect(shareContent.shareUrl, contains(referralCode));
    });

    test('should create share content with custom message', () {
      // Arrange
      const referralCode = 'TEST1234';
      const customMessage = 'Join me on this awesome gaming platform!';

      // Act
      final shareContent = ReferralShareContent.create(
        referralCode: referralCode,
        customMessage: customMessage,
      );

      // Assert
      expect(shareContent.referralCode, equals(referralCode));
      expect(shareContent.shareText, contains(customMessage));
      expect(shareContent.shareText, contains(referralCode));
    });
  });

  group('ReferralService Static Methods', () {
    test('isValidReferralCode should validate referral codes correctly', () {
      // Valid codes
      expect(ReferralService.isValidReferralCode('TEST1234'), isTrue);
      expect(ReferralService.isValidReferralCode('ABC123'), isTrue);
      expect(ReferralService.isValidReferralCode('USER9999'), isTrue);

      // Invalid codes
      expect(ReferralService.isValidReferralCode(''), isFalse);
      expect(ReferralService.isValidReferralCode('123'), isFalse); // Too short
      expect(ReferralService.isValidReferralCode('test@123'), isFalse); // Special chars
      expect(ReferralService.isValidReferralCode('test 123'), isFalse); // Space
    });

    test('extractReferralCodeFromUrl should extract codes from URLs', () {
      // Valid URLs
      expect(
        ReferralService.extractReferralCodeFromUrl('https://wiggyz.com/refer/TEST1234'),
        equals('TEST1234'),
      );
      expect(
        ReferralService.extractReferralCodeFromUrl('https://wiggyz.com/refer/ABC123'),
        equals('ABC123'),
      );
      expect(
        ReferralService.extractReferralCodeFromUrl('https://wiggyz.com/app?ref=USER9999'),
        equals('USER9999'),
      );

      // Invalid URLs
      expect(
        ReferralService.extractReferralCodeFromUrl('https://wiggyz.com/refer/'),
        isNull,
      );
      expect(
        ReferralService.extractReferralCodeFromUrl('https://wiggyz.com/other/page'),
        isNull,
      );
      expect(
        ReferralService.extractReferralCodeFromUrl('invalid-url'),
        isNull,
      );
    });
  });

  group('ReferralShareResult Tests', () {
    test('should create success result', () {
      final result = ReferralShareResult.success(platform: 'WhatsApp');
      
      expect(result.status, equals(ReferralShareStatus.success));
      expect(result.platform, equals('WhatsApp'));
      expect(result.isSuccess, isTrue);
      expect(result.isError, isFalse);
    });

    test('should create error result', () {
      const errorMessage = 'Network error';
      final result = ReferralShareResult.error(errorMessage);
      
      expect(result.status, equals(ReferralShareStatus.error));
      expect(result.errorMessage, equals(errorMessage));
      expect(result.isError, isTrue);
      expect(result.isSuccess, isFalse);
    });

    test('should create cancelled result', () {
      final result = ReferralShareResult.cancelled();
      
      expect(result.status, equals(ReferralShareStatus.cancelled));
      expect(result.isCancelled, isTrue);
      expect(result.isSuccess, isFalse);
    });

    test('should create unavailable result', () {
      final result = ReferralShareResult.unavailable();
      
      expect(result.status, equals(ReferralShareStatus.unavailable));
      expect(result.isUnavailable, isTrue);
      expect(result.errorMessage, isNotNull);
    });
  });
}
